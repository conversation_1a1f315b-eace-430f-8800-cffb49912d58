# TradeFusion 桌面版 v2.0 (Electron版)

## 🚀 v2.0 Electron版更新

**重大功能升级**：从浏览器版本升级为真正的桌面应用，实现真正的一键启动

### ✨ **Electron版本特性**
- ✅ **真正的一键启动**：点击启动按钮，Python模块直接启动，无需额外操作
- ✅ **系统级权限**：可以直接控制系统进程，启动/停止Python脚本
- ✅ **桌面应用体验**：独立窗口，无浏览器依赖，更专业的界面
- ✅ **进程管理**：实时监控Python进程状态，自动清理退出进程
- ✅ **错误处理**：完善的错误提示和异常处理机制

### ✅ **简化成果**
- **文件数量**：从15个文件简化为8个核心文件
- **目录结构**：从7层目录简化为3层清晰结构
- **代码行数**：保持1800行，但结构更合理
- **配置管理**：恢复外部配置文件，避免CORS问题
- **文档整理**：所有文档整理到docs/目录

### 📁 **新架构**
```
TradeFusion桌面版/
├── index_v2.html       # 简化版主文件
├── config.js           # 外部配置文件
├── js/
│   ├── app.js          # 主应用逻辑
│   ├── controller.js   # 模块控制
│   ├── renderer.js     # UI渲染
│   └── batch.js        # 批处理生成
├── css/
│   └── style.css       # 统一样式
└── docs/               # 所有文档
```

## 概述

TradeFusion桌面版是一个纯HTML实现的数据流可视化界面，提供了TradeFusion数据流的完整管理功能。它允许用户通过图形界面管理和监控26个数据处理模块的运行状态，无需安装任何额外软件。

## 特性

- **纯HTML实现**：无需安装Node.js或其他依赖，直接在浏览器中运行
- **可视化数据流**：以思维导图的形式展示26个模块之间的数据流关系
- **模块管理**：支持启动、停止和监控各个Python模块
- **实时状态**：实时显示模块运行状态和数据库连接状态
- **配置管理**：可配置Python路径、工作目录等参数
- **跨平台兼容**：支持所有现代浏览器

## 系统要求

- 现代浏览器（Chrome 80+, Firefox 75+, Safari 13+, Edge 80+）
- Python 3.7 或更高版本（用于运行TradeFusion模块）
- 操作系统：Windows 10/11, macOS 10.14+, 或 Linux

## 快速开始

### 🚀 **方法一：Electron桌面版（推荐）**

**真正的一键启动体验！**

1. **首次使用**：双击 `启动Electron版.bat`
   - 自动检查Node.js环境
   - 自动安装依赖包（首次需要几分钟）
   - 启动独立桌面应用

2. **日常使用**：双击 `启动Electron版.bat` 即可

**Electron版本优势**：
- ✅ **真正一键启动**：点击启动按钮，Python模块直接运行
- ✅ **无需手动操作**：不需要下载和执行批处理文件
- ✅ **桌面应用体验**：独立窗口，专业界面
- ✅ **进程管理**：实时监控Python进程状态



### 📋 **环境要求**

**系统要求**：
- Node.js v16.0.0+ （[下载地址](https://nodejs.org/)）
- Python 3.7+
- Windows 10/11

## 使用说明

### 界面布局

- **左上角**：RQ调度中心（人气数据流程）
- **右上角**：XGB调度中心（选股宝接力流程）
- **中间区域**：数据采集、清洗、分析模块
- **右侧**：数据库表
- **底部**：输出模块（生成大智慧文件）

### 模块操作

1. **启动模块**：
   - 点击模块上的"启动"按钮
   - 选择"生成批处理文件启动"或"复制命令手动启动"
   - 按照提示操作

2. **停止模块**：
   - 点击模块上的"停止"按钮
   - 系统会尝试终止对应的Python进程

3. **配置模块**：
   - 首次使用需要配置Python路径和文件路径
   - 配置信息会自动保存到浏览器本地存储

### 状态指示

- **绿色**：模块正在运行
- **灰色**：模块已停止
- **红色**：模块运行出错
- **蓝色**：数据库表（始终运行状态）

### 数据流连接

- **蓝色连接线**：RQ调度流程
- **橙色连接线**：XGB调度流程  
- **绿色连接线**：数据库写入流程

## 配置说明

### 模块配置文件

配置信息存储在 `modules-config.json` 文件中，包含：

- **modules**：26个模块的详细配置
- **connections**：模块间的连接关系
- **config**：全局配置（Python路径、工作目录等）

### 首次配置

1. 启动任意模块时，系统会提示进行配置
2. 设置正确的Python解释器路径
3. 确认TradeFusion项目的根目录路径
4. 配置会自动保存，后续使用无需重复配置

## 批处理文件使用

### 生成批处理文件

1. 点击模块的"启动"按钮
2. 选择"生成批处理文件启动"
3. 系统会下载对应的 `.bat` 文件到下载文件夹

### 运行批处理文件

1. 打开下载文件夹
2. 找到生成的 `.bat` 文件
3. 双击运行
4. 等待模块启动完成
5. 返回浏览器查看模块状态

## 故障排除

### 常见问题

1. **配置文件加载失败**
   - 确保 `modules-config.json` 文件存在
   - 检查JSON格式是否正确
   - 刷新页面重新加载

2. **模块启动失败**
   - 检查Python路径是否正确
   - 确认Python脚本文件存在
   - 检查工作目录路径是否正确

3. **批处理文件无法运行**
   - 确认Python已正确安装
   - 检查文件路径中是否包含中文或特殊字符
   - 以管理员身份运行批处理文件

4. **界面显示异常**
   - 刷新页面
   - 清除浏览器缓存
   - 检查浏览器控制台错误信息

### 调试信息

- 按F12打开浏览器开发者工具
- 查看Console标签页的错误信息
- 检查Network标签页的网络请求状态

## 项目结构

```
TradeFusion桌面版/
├── index.html              # 主界面文件（完整实现）
├── modules-config.json     # 模块配置文件
├── 项目结构说明.md         # 项目结构文档（📋详细说明）
├── TradeFusion桌面版数据流规范文档.md # 数据流规范（⚠️修改必读）
├── 打开桌面版.bat          # 一键启动脚本
└── README.md              # 使用说明
```

**📋 详细结构说明**：请参考 `项目结构说明.md` 文档，其中包含每个目录和文件的详细功能说明。

## 技术特点

- **无依赖**：纯HTML/CSS/JavaScript实现
- **配置外置**：模块配置独立于代码
- **本地存储**：用户配置保存在浏览器本地
- **响应式设计**：适配不同屏幕尺寸
- **实时更新**：动态状态监控

## ⚠️ 重要文档

### 数据流规范文档
**文件**：`TradeFusion桌面版数据流规范文档.md`

**⚠️ 修改代码前必读**：
- 包含完整的数据流逻辑规范
- 定义了严格的修改约束和禁止事项
- 防止破坏核心业务逻辑的权威参考
- 任何连接关系、模块配置的修改都必须参考此文档

**关键约束**：
- XGB流程T1-T11严格串行，禁止并行
- 数据枢纽模块必须保持3表写入特征
- 复杂算法模块必须保持4表读取特征
- 调度中心不能改为直接控制所有模块

## 更新日志

### v2.0.0 (当前版本)
- 简化架构，移除Electron依赖
- 配置外置到独立JSON文件
- 改进批处理文件生成机制
- 优化用户体验和错误提示
- 移除硬编码路径，支持相对路径
- **新增数据流规范文档，规范修改行为**

### v1.0.0
- 初始版本，基于Electron + React实现

## 许可证

本项目采用 MIT 许可证。

## 贡献

欢迎提交Issue和建议来改进这个项目。

## 联系方式

如有问题或建议，请通过项目Issue页面反馈。
