#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
TradeFusion临时表引用更新脚本
批量更新所有模块中的临时表名称引用
"""

import os
import re
from pathlib import Path

# 替换映射
REPLACEMENTS = {
    '东财人气_临时表': '临时表_东财人气',
    '同花人气_临时表': '临时表_同花人气',
    '选股宝原始_临时表': '临时表_选股宝原始',
    '选股宝清洗_临时表': '临时表_选股宝清洗'
}

# 需要更新的文件列表
FILES_TO_UPDATE = [
    '数据库1_基础表模块/人气临时表管理.py',
    '数据3_网络采集_bk/选股宝抓取.py',
    '数据3_网络采集_bk/选股宝清洗.py',
    '数据3_网络采集_bk/个股解读_板块信息_关联表.py'
]

def update_file_references(file_path: Path) -> bool:
    """更新单个文件中的临时表引用"""
    if not file_path.exists():
        print(f"⚠️  文件不存在: {file_path}")
        return False
    
    try:
        # 读取文件内容
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        original_content = content
        changes_made = []
        
        # 执行替换
        for old_name, new_name in REPLACEMENTS.items():
            if old_name in content:
                content = content.replace(old_name, new_name)
                count = original_content.count(old_name)
                changes_made.append(f"{old_name} → {new_name} ({count}处)")
        
        # 如果有变更，写回文件
        if content != original_content:
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(content)
            
            print(f"✅ 更新文件: {file_path.name}")
            for change in changes_made:
                print(f"   - {change}")
            return True
        else:
            print(f"ℹ️  无需更新: {file_path.name}")
            return True
            
    except Exception as e:
        print(f"❌ 更新失败: {file_path.name} - {str(e)}")
        return False

def main():
    """主函数"""
    print("=" * 60)
    print("TradeFusion临时表引用更新工具")
    print("批量更新所有模块中的临时表名称引用")
    print("=" * 60)
    
    project_root = Path(__file__).parent.parent
    success_count = 0
    total_count = len(FILES_TO_UPDATE)
    
    for file_rel_path in FILES_TO_UPDATE:
        file_path = project_root / file_rel_path
        if update_file_references(file_path):
            success_count += 1
    
    print(f"\n📊 更新结果: {success_count}/{total_count} 个文件更新成功")
    
    if success_count == total_count:
        print("🎉 所有文件更新完成！")
        print("\n📝 替换映射:")
        for old_name, new_name in REPLACEMENTS.items():
            print(f"   {old_name} → {new_name}")
    else:
        print("⚠️  部分文件更新失败，请检查错误信息")
    
    return success_count == total_count

if __name__ == "__main__":
    main()
