# TradeFusion PostgreSQL 数据库连接说明

## 📋 基本信息

- **数据库类型**: PostgreSQL
- **数据库名称**: tradefusion
- **数据库版本**: PostgreSQL 13+
- **数据库位置**: `E:\TradeFusion\数据库_PostgreSQL`
- **字符编码**: UTF-8

## 🔌 连接参数

### 标准连接配置
```yaml
host: localhost
port: 5432
database: tradefusion
user: postgres
password: ***********
```

### 连接字符串格式
```
postgresql://postgres:***********@localhost:5432/tradefusion
```

## 💻 各语言连接示例

### Python (psycopg2)
```python
import psycopg2

# 方式1: 使用连接参数
conn = psycopg2.connect(
    host='localhost',
    port=5432,
    database='tradefusion',
    user='postgres',
    password='***********'
)

# 方式2: 使用连接字符串
conn = psycopg2.connect(
    "postgresql://postgres:***********@localhost:5432/tradefusion"
)

# 方式3: 使用字典配置
db_config = {
    'host': 'localhost',
    'port': 5432,
    'database': 'tradefusion',
    'user': 'postgres',
    'password': '***********'
}
conn = psycopg2.connect(**db_config)
```

### Python (SQLAlchemy)
```python
from sqlalchemy import create_engine

# 创建数据库引擎
engine = create_engine(
    'postgresql://postgres:***********@localhost:5432/tradefusion'
)

# 获取连接
conn = engine.connect()
```

### Node.js (pg)
```javascript
const { Client } = require('pg');

const client = new Client({
    host: 'localhost',
    port: 5432,
    database: 'tradefusion',
    user: 'postgres',
    password: '***********'
});

await client.connect();
```

### Java (JDBC)
```java
import java.sql.Connection;
import java.sql.DriverManager;

String url = "********************************************";
String username = "postgres";
String password = "***********";

Connection conn = DriverManager.getConnection(url, username, password);
```

### C# (.NET)
```csharp
using Npgsql;

string connectionString = "Host=localhost;Port=5432;Database=tradefusion;Username=postgres;Password=***********";
using var conn = new NpgsqlConnection(connectionString);
conn.Open();
```

## 🗂️ 数据库结构

### 数据库概览
- **数据库名称**: tradefusion
- **表总数**: 15个
- **总记录数**: 约47,000条
- **主要功能**: 股票交易数据分析、人气统计、板块分析

### 完整表结构

#### 1. 个股人气表 (7,210 条记录)
**用途**: 存储个股人气排名数据
```sql
CREATE TABLE "个股人气表" (
    "日期" INTEGER NOT NULL,
    "股票代码" VARCHAR(8) NOT NULL,
    "东财人气排名" INTEGER,
    "同花人气排名" INTEGER,
    "综合人气评分" REAL,
    "股票名称" VARCHAR(50),
    "更新时间戳" TEXT,
    PRIMARY KEY ("日期", "股票代码")
);
```

#### 2. 个股接力表 (709 条记录)
**用途**: 存储个股接力值数据
```sql
CREATE TABLE "个股接力表" (
    "日期" INTEGER NOT NULL,
    "股票代码" VARCHAR(8) NOT NULL,
    "接力" INTEGER,
    "股票类型" TEXT DEFAULT 'X支',
    "更新时间戳" TEXT,
    PRIMARY KEY ("日期", "股票代码")
);
```

#### 3. 个股板块关联表 (9,974 条记录)
**用途**: 存储个股与板块的关联关系
```sql
CREATE TABLE "个股板块关联表" (
    "日期" INTEGER NOT NULL,
    "股票代码" VARCHAR(8) NOT NULL,
    "所属板块名称" TEXT NOT NULL,
    "更新时间戳" TEXT,
    PRIMARY KEY ("日期", "股票代码", "所属板块名称")
);
```

#### 4. 个股解读表 (10,186 条记录)
**用途**: 存储个股解读信息
```sql
CREATE TABLE "个股解读表" (
    "日期" INTEGER NOT NULL,
    "股票代码" VARCHAR(8) NOT NULL,
    "个股解读" TEXT,
    PRIMARY KEY ("日期", "股票代码")
);
```

#### 5. 个股连板高度表 (8,290 条记录)
**用途**: 存储个股涨停相关数据
```sql
CREATE TABLE "个股连板高度表" (
    "日期" INTEGER NOT NULL,
    "股票代码" VARCHAR(8) NOT NULL,
    "连板高度" REAL,
    "涨停评分" REAL,
    "涨停时间" REAL,
    "一字板" REAL,
    "T字板" REAL,
    "黄金换手" REAL,
    "成交金额" REAL,
    PRIMARY KEY ("日期", "股票代码")
);
```

#### 6. 板块信息表 (2,247 条记录)
**用途**: 存储板块基础信息
```sql
CREATE TABLE "板块信息表" (
    "日期" INTEGER NOT NULL,
    "板块名称" TEXT NOT NULL,
    "板块涨幅" TEXT,
    "板块消息" TEXT,
    PRIMARY KEY ("日期", "板块名称")
);
```

#### 7. 板块涨停表 (650 条记录)
**用途**: 存储板块涨停统计
```sql
CREATE TABLE "板块涨停表" (
    "日期" INTEGER NOT NULL,
    "板块名称" TEXT NOT NULL,
    "板块评分" REAL,
    "更新时间戳" TEXT,
    PRIMARY KEY ("日期", "板块名称")
);
```

#### 8. 板块精选表 (32 条记录)
**用途**: 存储板块精选结果
```sql
CREATE TABLE "板块精选表" (
    "日期" INTEGER NOT NULL,
    "板块名称" TEXT NOT NULL,
    "综合评分" REAL,
    "更新时间戳" TEXT,
    PRIMARY KEY ("日期", "板块名称")
);
```

#### 9. 所属板块评分表 (8,364 条记录)
**用途**: 存储个股所属板块评分
```sql
CREATE TABLE "所属板块评分表" (
    "日期" INTEGER NOT NULL,
    "股票代码" VARCHAR(8) NOT NULL,
    "所属板块评分" INTEGER,
    PRIMARY KEY ("日期", "股票代码")
);
```

#### 10. DAT文件处理记录表 (61 条记录)
**用途**: 记录数据文件处理状态
```sql
CREATE TABLE "DAT文件处理记录表" (
    "日期" INTEGER NOT NULL,
    "股票代码" TEXT NOT NULL,
    PRIMARY KEY ("日期", "股票代码")
);
```

### 临时数据表

#### 11. 临时表_东财人气 (100 条记录)
**用途**: 东方财富人气数据临时存储
```sql
CREATE TABLE "临时表_东财人气" (
    "id" SERIAL PRIMARY KEY,
    "股票代码" TEXT NOT NULL,
    "股票名称" TEXT NOT NULL,
    "人气排名" INTEGER NOT NULL,
    "采集时间" TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    "处理状态" TEXT DEFAULT '待处理',
    "备注" TEXT
);
```

#### 12. 临时表_同花人气 (93 条记录)
**用途**: 同花顺人气数据临时存储
```sql
CREATE TABLE "临时表_同花人气" (
    "id" SERIAL PRIMARY KEY,
    "股票代码" TEXT NOT NULL,
    "股票名称" TEXT NOT NULL,
    "人气排名" INTEGER NOT NULL,
    "采集时间" TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    "处理状态" TEXT DEFAULT '待处理',
    "备注" TEXT
);
```

#### 13. 临时表_选股宝原始 (1 条记录)
**用途**: 选股宝原始数据临时存储
```sql
CREATE TABLE "临时表_选股宝原始" (
    "id" SERIAL PRIMARY KEY,
    "原始数据" TEXT NOT NULL,
    "采集时间" TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    "处理状态" TEXT DEFAULT '待处理',
    "备注" TEXT
);
```

#### 14. 临时表_选股宝清洗 (39 条记录)
**用途**: 选股宝清洗后数据临时存储
```sql
CREATE TABLE "临时表_选股宝清洗" (
    "id" SERIAL PRIMARY KEY,
    "日期" INTEGER NOT NULL,
    "股票代码" TEXT NOT NULL,
    "板块名称" TEXT NOT NULL,
    "板块涨幅" TEXT,
    "板块消息" TEXT,
    "个股解读" TEXT,
    "采集时间" TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    "处理状态" TEXT DEFAULT '待处理',
    "备注" TEXT
);
```

#### 15. mcp_insights (8 条记录)
**用途**: MCP系统洞察数据存储
```sql
CREATE TABLE "mcp_insights" (
    "id" SERIAL PRIMARY KEY,
    "insight" TEXT NOT NULL,
    "created_at" TEXT DEFAULT CURRENT_TIMESTAMP
);
```

### 常用查询示例

#### 查看所有表
```sql
SELECT table_name,
       (SELECT COUNT(*) FROM information_schema.columns WHERE table_name = t.table_name) as column_count
FROM information_schema.tables t
WHERE table_schema = 'public'
ORDER BY table_name;
```

#### 查看表结构
```sql
SELECT column_name, data_type, is_nullable, column_default
FROM information_schema.columns
WHERE table_name = '个股人气表'
ORDER BY ordinal_position;
```

#### 查询最新人气数据
```sql
SELECT * FROM "个股人气表"
WHERE "日期" = (SELECT MAX("日期") FROM "个股人气表")
ORDER BY "综合人气评分" DESC NULLS LAST
LIMIT 10;
```

#### 查询板块信息
```sql
SELECT "板块名称", "板块涨幅", "板块消息"
FROM "板块信息表"
WHERE "日期" = 20250304
ORDER BY CAST("板块涨幅" AS NUMERIC) DESC;
```

#### 查询个股板块关联
```sql
SELECT g."股票代码", g."综合人气评分", b."所属板块名称"
FROM "个股人气表" g
JOIN "个股板块关联表" b ON g."股票代码" = b."股票代码" AND g."日期" = b."日期"
WHERE g."日期" = 20250304
ORDER BY g."综合人气评分" DESC NULLS LAST;
```

### 索引信息

#### 主键索引
- **个股人气表**: (日期, 股票代码)
- **个股接力表**: (日期, 股票代码)
- **个股板块关联表**: (日期, 股票代码, 所属板块名称)
- **个股解读表**: (日期, 股票代码)
- **个股连板高度表**: (日期, 股票代码)
- **板块信息表**: (日期, 板块名称)
- **板块涨停表**: (日期, 板块名称)
- **板块精选表**: (日期, 板块名称)
- **所属板块评分表**: (日期, 股票代码)

#### 性能优化索引
- **临时表_东财人气**: 股票代码、采集时间、处理状态
- **临时表_同花人气**: 股票代码、采集时间、处理状态
- **临时表_选股宝清洗**: 日期、股票代码、采集时间、处理状态

### 数据字典

#### 日期格式
- **格式**: YYYYMMDD (整数)
- **示例**: 20250304 表示 2025年3月4日

#### 股票代码格式
- **上海**: SH + 6位数字 (如: SH600000)
- **深圳**: SZ + 6位数字 (如: SZ000001)
- **长度**: 8位字符

#### 人气排名
- **东财人气排名**: 东方财富网人气排名 (数字越小排名越高)
- **同花人气排名**: 同花顺人气排名 (数字越小排名越高)
- **综合人气评分**: 0-100分，分数越高人气越高

#### 板块涨幅
- **格式**: 百分比字符串 (如: "5.67", "-2.34")
- **单位**: 百分比

#### 处理状态
- **待处理**: 数据已采集，等待处理
- **处理中**: 正在处理数据
- **已完成**: 数据处理完成
- **失败**: 数据处理失败

## 🔧 配置管理

### 使用TradeFusion配置管理器
```python
# 导入配置管理
from 公共模块.配置管理 import get_config

# 获取数据库配置
config = get_config()
db_config = config.get('database')

# 连接数据库
import psycopg2
conn = psycopg2.connect(**db_config)
```

### 配置文件位置
- 主配置文件: `config/data_sources.yaml`
- 默认配置: `公共模块/配置管理.py`

## 🌐 网络访问配置

### 本地访问 (默认)
- 使用 `localhost` 或 `127.0.0.1`
- 适用于同一台机器上的应用

### 远程访问配置
如需远程访问，需要修改以下配置：

1. **修改 postgresql.conf**
```conf
listen_addresses = '*'  # 允许所有IP连接
port = 5432
```

2. **修改 pg_hba.conf**
```conf
# 允许指定IP段连接
host    tradefusion    postgres    ***********/24    md5
# 或允许所有IP连接（不推荐生产环境）
host    tradefusion    postgres    0.0.0.0/0         md5
```

3. **防火墙设置**
```bash
# Windows防火墙
netsh advfirewall firewall add rule name="PostgreSQL" dir=in action=allow protocol=TCP localport=5432

# 或在Windows防火墙界面中添加5432端口规则
```

## 🔒 安全建议

### 1. 创建专用用户
```sql
-- 创建只读用户
CREATE USER readonly_user WITH PASSWORD 'your_password';
GRANT CONNECT ON DATABASE tradefusion TO readonly_user;
GRANT USAGE ON SCHEMA public TO readonly_user;
GRANT SELECT ON ALL TABLES IN SCHEMA public TO readonly_user;

-- 创建读写用户
CREATE USER readwrite_user WITH PASSWORD 'your_password';
GRANT CONNECT ON DATABASE tradefusion TO readwrite_user;
GRANT USAGE ON SCHEMA public TO readwrite_user;
GRANT SELECT, INSERT, UPDATE, DELETE ON ALL TABLES IN SCHEMA public TO readwrite_user;
```

### 2. 连接池配置
```python
# 使用连接池 (psycopg2)
from psycopg2 import pool

connection_pool = psycopg2.pool.SimpleConnectionPool(
    1, 20,  # 最小和最大连接数
    host='localhost',
    port=5432,
    database='tradefusion',
    user='postgres',
    password='***********'
)
```

## 🔍 连接测试

### 快速连接测试
```python
#!/usr/bin/env python3
import psycopg2

def test_connection():
    try:
        conn = psycopg2.connect(
            host='localhost',
            port=5432,
            database='tradefusion',
            user='postgres',
            password='***********'
        )
        
        cursor = conn.cursor()
        cursor.execute('SELECT version();')
        version = cursor.fetchone()[0]
        print(f"✅ 连接成功! PostgreSQL版本: {version}")
        
        cursor.execute("""
            SELECT COUNT(*) FROM information_schema.tables
            WHERE table_schema = 'public' AND table_type = 'BASE TABLE'
        """)
        table_count = cursor.fetchone()[0]
        print(f"📊 数据库共有 {table_count} 个表")
        
        conn.close()
        return True
        
    except Exception as e:
        print(f"❌ 连接失败: {e}")
        return False

if __name__ == "__main__":
    test_connection()
```

## 📞 技术支持

### 常见问题

1. **连接被拒绝**
   - 检查PostgreSQL服务是否启动
   - 确认端口5432是否开放
   - 验证用户名密码是否正确

2. **权限不足**
   - 确认用户是否有数据库访问权限
   - 检查表级别权限设置

3. **编码问题**
   - 确保客户端和数据库都使用UTF-8编码
   - 设置正确的locale

### 联系方式
- 项目位置: `E:\TradeFusion`
- 配置文件: `config/data_sources.yaml`
- 日志位置: 查看TradeFusion系统日志

---

**最后更新**: 2025-01-28  
**文档版本**: v1.0  
**适用版本**: TradeFusion v2.0+
