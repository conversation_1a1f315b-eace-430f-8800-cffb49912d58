#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
TradeFusion PostgreSQL快速查询
"""

import psycopg2
import pandas as pd

def quick_query():
    """快速查询数据库"""
    try:
        # 连接数据库
        print("🔌 连接PostgreSQL数据库...")
        # {{ AURA-X: Modify - 统一PostgreSQL密码为标准密码. Approval: 寸止(ID:1737734400). }}
        conn = psycopg2.connect(
            host='localhost',
            port=5432,
            database='tradefusion',
            user='postgres',
            password='ymjatTUU520'
        )
        print("✅ 连接成功！")
        
        # 查询所有表
        print("\n📊 数据库表列表:")
        print("=" * 50)
        
        cursor = conn.cursor()
        cursor.execute("""
            SELECT table_name 
            FROM information_schema.tables 
            WHERE table_schema = 'public' 
            ORDER BY table_name;
        """)
        
        tables = cursor.fetchall()
        for i, (table_name,) in enumerate(tables, 1):
            # 查询每个表的记录数
            try:
                cursor.execute(f'SELECT COUNT(*) FROM "{table_name}";')
                count = cursor.fetchone()[0]
                print(f"{i:2d}. {table_name:<20} ({count:,} 条记录)")
            except Exception as e:
                print(f"{i:2d}. {table_name:<20} (查询失败)")
        
        # 查询个股人气表的样例数据
        print(f"\n📋 '个股人气表' 样例数据 (前5行):")
        print("=" * 80)
        try:
            df = pd.read_sql('SELECT * FROM "个股人气表" LIMIT 5;', conn)
            print(df.to_string(index=False))
        except Exception as e:
            print(f"查询失败: {e}")
        
        # 查询板块信息表的样例数据
        print(f"\n📋 '板块信息表' 样例数据 (前5行):")
        print("=" * 80)
        try:
            df = pd.read_sql('SELECT * FROM "板块信息表" LIMIT 5;', conn)
            print(df.to_string(index=False))
        except Exception as e:
            print(f"查询失败: {e}")
        
        cursor.close()
        conn.close()
        print("\n🔌 数据库连接已断开")
        
    except Exception as e:
        print(f"❌ 操作失败: {e}")

if __name__ == "__main__":
    quick_query()
