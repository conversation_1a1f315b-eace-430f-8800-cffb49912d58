import datetime
import struct
import logging

"""交易日期工具模块
主要功能：
1. 判断是否为交易日（处理节假日和周末）
2. 获取当前交易日期（考虑9:16分的特殊处理）
"""

# 配置基础日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# 定义2025年休市日期列表（包括法定节假日和周末休市）
holiday_list = [
    {"name": "元旦", "start_date": datetime.date(2025, 1, 1), "end_date": datetime.date(2025, 1, 1)},
    {"name": "春节", "start_date": datetime.date(2025, 1, 28), "end_date": datetime.date(2025, 2, 4)},
    {"name": "春节周末休市", "start_date": datetime.date(2025, 1, 26), "end_date": datetime.date(2025, 1, 26)},  # 星期日
    {"name": "春节周末休市", "start_date": datetime.date(2025, 2, 8), "end_date": datetime.date(2025, 2, 8)},  # 星期六
    {"name": "清明节", "start_date": datetime.date(2025, 4, 4), "end_date": datetime.date(2025, 4, 6)},
    {"name": "劳动节", "start_date": datetime.date(2025, 5, 1), "end_date": datetime.date(2025, 5, 5)},
    {"name": "劳动节周末休市", "start_date": datetime.date(2025, 4, 27), "end_date": datetime.date(2025, 4, 27)},  # 星期日
    {"name": "端午节", "start_date": datetime.date(2025, 5, 31), "end_date": datetime.date(2025, 6, 2)},
    {"name": "国庆节、中秋节", "start_date": datetime.date(2025, 10, 1), "end_date": datetime.date(2025, 10, 8)},
    {"name": "国庆节、中秋节周末休市", "start_date": datetime.date(2025, 9, 28), "end_date": datetime.date(2025, 9, 28)},  # 星期日
    {"name": "国庆节、中秋节周末休市", "start_date": datetime.date(2025, 10, 11), "end_date": datetime.date(2025, 10, 11)}  # 星期六
]

def is_holiday(date, holiday_list):
    """
    判断是否为休市日
    包括：1. 周末 2. 法定节假日
    """
    for holiday in holiday_list:
        if holiday['start_date'] <= date <= holiday['end_date']:
            return True
    return date.weekday() in [5, 6]  # 5代表周六，6代表周日

def get_previous_trading_day(date, holiday_list):
    """
    获取指定日期的上一个交易日
    通过循环往前查找，直到找到非休市日
    """
    date -= datetime.timedelta(days=1)  # 首先减去一天
    while is_holiday(date, holiday_list):
        date -= datetime.timedelta(days=1)
    return date

# 缓存当前交易日
cached_trading_date = None

def get_current_trading_date_YYYYMMDD():
    """获取当前交易日期，格式为 YYYYMMDD"""
    global cached_trading_date
    current_date = datetime.datetime.now().date()
    current_time = datetime.datetime.now().time()
    
    # 如果当前时间小于9点16，则返回上一个交易日
    if current_time < datetime.time(9, 16):
        if cached_trading_date is None or cached_trading_date != current_date:
            cached_trading_date = get_previous_trading_day(current_date, holiday_list)
    else:
        if cached_trading_date is None or cached_trading_date != current_date:
            while is_holiday(current_date, holiday_list):
                current_date -= datetime.timedelta(days=1)
            cached_trading_date = current_date
    
    trading_date = int(cached_trading_date.strftime('%Y%m%d'))
    return trading_date

# 封装输出函数
def get_trading_date_from_datetime(dt):
    """根据输入日期获取对应的交易日"""
    date = dt.date()
    time = dt.time()
    
    # 如果时间在9:16之前，使用前一个交易日
    if time < datetime.time(9, 16):
        trading_date = get_previous_trading_day(date, holiday_list)
    else:
        trading_date = date
        while is_holiday(trading_date, holiday_list):
            trading_date -= datetime.timedelta(days=1)
    
    return int(trading_date.strftime('%Y%m%d'))

def get_trading_date():
    """获取当前交易日期"""
    return get_current_trading_date_YYYYMMDD()

def log_trading_date():
    """记录当前交易日期到日志"""
    trading_date = get_trading_date()
    logger.info(f"当前交易日期: {trading_date}")

# 添加对 print_trading_date 函数的调用
#print_trading_date()