# {{ AURA-X: Modify - 将SQLite连接改为PostgreSQL连接. Approval: 寸止(ID:1737734400). }}
import psycopg2
import psycopg2.extras
from datetime import datetime
import sys
import logging
import os
from pathlib import Path
from logging.handlers import TimedRotatingFileHandler

# 添加项目根目录到路径
current_file = Path(__file__).resolve()
project_root = current_file.parent.parent
if str(project_root) not in sys.path:
    sys.path.insert(0, str(project_root))

# 导入TradeFusion统一日志标准
from 公共模块.TradeFusion统一日志标准 import 获取日志器

# 获取统一日志器
logger = 获取日志器("板块涨停表")

def calculate_sector_stocks():
    """
    计算板块涨停评分，智能更新数据库
    只保留前7名板块，自动删除掉出前7名的板块
    """
    # 移除开始日志，只保留完成日志

    # 连接数据库
    # {{ AURA-X: Modify - 修改为PostgreSQL连接配置. Approval: 寸止(ID:1737734400). }}
    # {{ AURA-X: Modify - 使用统一配置管理而非硬编码. Approval: 寸止(ID:1737734400). }}
    try:
        from 公共模块.配置管理 import get_config
        config = get_config()
        db_config = config.get('database')
    except ImportError:
        # 备用配置
        db_config = {
            'host': 'localhost',
            'port': 5432,
            'database': 'tradefusion',
            'user': 'postgres',
            'password': 'ymjatTUU520'
        }
    conn = psycopg2.connect(**db_config)
    cursor = conn.cursor()

    try:
        # 获取最新日期
        # {{ AURA-X: Modify - 修改为PostgreSQL语法. Approval: 寸止(ID:1737734400). }}
        cursor.execute('SELECT MAX("日期") FROM "个股板块关联表"')
        latest_date = cursor.fetchone()[0]
        if not latest_date:
            return False, "未找到有效日期数据", 0


        # {{ AURA-X: Modify - PostgreSQL事务语法. Approval: 寸止(ID:1737734400). }}
        # 使用事务处理数据更新
        cursor.execute("BEGIN")

        # 获取当前数据库中该日期的所有板块
        # {{ AURA-X: Modify - 修改为PostgreSQL语法. Approval: 寸止(ID:1737734400). }}
        cursor.execute('SELECT "板块名称" FROM "板块涨停表" WHERE "日期" = %s', (latest_date,))
        existing_sectors = set([row[0] for row in cursor.fetchall()])

        # {{ AURA-X: Modify - 修改为PostgreSQL语法. Approval: 寸止(ID:1737734400). }}
        # 统计板块涨停评分（关联个股连板高度表）并获取所有板块数据
        cursor.execute('''
            SELECT
                a."所属板块名称",
                SUM(b."涨停评分") AS "板块涨停评分"
            FROM "个股板块关联表" a
            INNER JOIN "个股连板高度表" b
                ON a."日期" = b."日期" AND a."股票代码" = b."股票代码"
            WHERE a."日期" = %s
            GROUP BY a."所属板块名称"
            ORDER BY "板块涨停评分" DESC
        ''', (latest_date,))

        all_sector_data = cursor.fetchall()

        # 只保留前7名
        top_7_sectors = all_sector_data[:7]
        top_7_names = set([sector[0] for sector in top_7_sectors])

        # 找出需要删除的板块（存在于数据库但不在前7名中）
        sectors_to_remove = existing_sectors - top_7_names

        # 删除掉出前7名的板块（同时清理板块涨停表和个股板块关联表）
        deleted_count = 0
        deleted_relation_count = 0
        if sectors_to_remove:
            for sector_name in sectors_to_remove:
                # {{ AURA-X: Modify - 修改为PostgreSQL语法. Approval: 寸止(ID:1737734400). }}
                # 删除板块涨停表中的记录
                cursor.execute('''
                    DELETE FROM "板块涨停表"
                    WHERE "日期" = %s AND "板块名称" = %s
                ''', (latest_date, sector_name))
                deleted_count += 1

                # 同步删除个股板块关联表中的对应记录
                cursor.execute('''
                    DELETE FROM "个股板块关联表"
                    WHERE "日期" = %s AND "所属板块名称" = %s
                ''', (latest_date, sector_name))
                deleted_relation_count += cursor.rowcount

            if deleted_relation_count > 0:
                pass  # 删除操作已完成

        # 更新或插入前7名板块数据
        added_count = 0
        updated_count = 0

        for sector_name, sector_score in top_7_sectors:
            # {{ AURA-X: Modify - 修改为PostgreSQL语法. Approval: 寸止(ID:1737734400). }}
            # 检查板块是否已存在
            cursor.execute('''
                SELECT COUNT(*) FROM "板块涨停表"
                WHERE "日期" = %s AND "板块名称" = %s
            ''', (latest_date, sector_name))

            exists = cursor.fetchone()[0] > 0

            if exists:
                # 更新现有记录
                cursor.execute('''
                    UPDATE "板块涨停表"
                    SET "板块评分" = %s
                    WHERE "日期" = %s AND "板块名称" = %s
                ''', (sector_score, latest_date, sector_name))
                updated_count += 1
            else:
                # 插入新记录
                cursor.execute('''
                    INSERT INTO "板块涨停表" ("日期", "板块名称", "板块评分")
                    VALUES (%s, %s, %s)
                ''', (latest_date, sector_name, sector_score))
                added_count += 1

        conn.commit()

        # 统计结果和关键信息
        total_sectors = len(all_sector_data)
        final_count = len(top_7_sectors)

        # 主要操作结果（简洁版）
        operations = []
        if added_count > 0:
            operations.append(f"新增{added_count}个")
        if updated_count > 0:
            operations.append(f"更新{updated_count}个")
        if deleted_count > 0:
            operations.append(f"删除{deleted_count}个")

        operation_summary = "、".join(operations) if operations else "无变化"

        # 如果有删除操作，显示关联表清理统计
        if deleted_relation_count > 0:
            pass  # 关联表清理已完成

        # 详细统计信息（DEBUG级别）
        debug_info = f"详细统计 - 参与计算: {total_sectors}个，保留: {final_count}个，新增: {added_count}个，更新: {updated_count}个，删除: {deleted_count}个"
        if deleted_relation_count > 0:
            debug_info += f"，关联表删除: {deleted_relation_count}条"

        # 记录处理结果（符合TradeFusion统一日志标准）
        logger.记录模块执行("板块涨停统计完成", added_count + updated_count, "采集_本地数据")

        # 🔄 板块涨停表写入完成后，自动触发下游模块
        _trigger_downstream_modules()

        return True, latest_date, final_count

    except Exception as e:
        logger.记录错误("板块涨停统计失败", e)
        conn.rollback()
        return False, str(e), 0
    finally:
        conn.close()

def _trigger_downstream_modules():
    """触发板块涨停表的下游模块"""
    try:
        # 触发所属板块评分表模块
        from 数据库3_个股所属板块层统计.A3_所属板块评分表 import calculate_stock_strength

        logger.记录模块执行("自动触发所属板块评分表模块")

        success, message, count = calculate_stock_strength()
        if success:
            logger.记录模块执行("所属板块评分表模块执行成功")

            # 所属板块评分表完成后，触发板块精选模块
            _trigger_selection_modules()
        else:
            logger.记录错误(f"所属板块评分表模块执行失败: {message}")

    except Exception as e:
        logger.记录错误("触发下游模块异常", e)

def _trigger_selection_modules():
    """触发精选决策层模块"""
    try:
        # 触发板块精选模块
        from 数据库2_板块层统计.板块精选 import main as 板块精选_main

        logger.记录模块执行("自动触发板块精选模块")

        result = 板块精选_main()
        if result:
            logger.记录模块执行("板块精选模块执行成功")

            # 板块精选完成后，触发个股接力表模块
            _trigger_relay_module()
        else:
            logger.记录错误("板块精选模块执行失败")

    except Exception as e:
        logger.记录错误("触发精选决策层模块异常", e)

def _trigger_relay_module():
    """触发个股接力表模块"""
    try:
        from 数据库3_个股所属板块层统计.个股接力表 import main as 个股接力_main

        logger.记录模块执行("自动触发个股接力表模块")

        result = 个股接力_main()
        if result:
            logger.记录模块执行("个股接力表模块执行成功")
        else:
            logger.记录错误("个股接力表模块执行失败")

    except Exception as e:
        logger.记录错误("触发个股接力表模块异常", e)

def main():
    """主函数，供触发器系统调用"""
    success, message, count = calculate_sector_stocks()
    if not success:
        logger.记录错误(f"执行失败: {message}")
        return False
    return True

if __name__ == "__main__":
    success, message, count = calculate_sector_stocks()
    if not success:
        logger.记录错误(f"执行失败: {message}")