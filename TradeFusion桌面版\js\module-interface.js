/**
 * 标准化模块接口
 * 
 * 功能：定义统一的模块接口规范和生命周期管理
 * 职责：模块注册、生命周期、依赖管理、热插拔
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 */

class ModuleInterface {
    constructor(app) {
        this.app = app;
        this.modules = new Map();
        this.dependencies = new Map();
        this.lifecycle = new Map();
        this.hooks = new Map();
        
        this.initializeInterface();
    }

    /**
     * 初始化模块接口
     */
    initializeInterface() {
        // 注册生命周期钩子
        this.registerLifecycleHooks();
        
        // 注册核心模块
        this.registerCoreModules();
        
        console.log('[ModuleInterface] 模块接口已初始化');
    }

    /**
     * 注册生命周期钩子
     */
    registerLifecycleHooks() {
        this.hooks.set('beforeInit', []);
        this.hooks.set('afterInit', []);
        this.hooks.set('beforeDestroy', []);
        this.hooks.set('afterDestroy', []);
        this.hooks.set('onError', []);
        this.hooks.set('onUpdate', []);
    }

    /**
     * 注册核心模块
     */
    registerCoreModules() {
        // 注册渲染器模块
        this.registerModule('renderer', {
            name: 'ModuleRenderer',
            version: '1.0.0',
            dependencies: ['dom-utils', 'event-utils'],
            lifecycle: {
                init: () => this.app.renderer,
                destroy: () => this.app.renderer?.cleanup?.()
            },
            interface: {
                render: 'function',
                updateModuleStatus: 'function',
                addModuleLog: 'function'
            }
        });

        // 注册连接线渲染器模块
        this.registerModule('connection-renderer', {
            name: 'ConnectionRenderer',
            version: '1.0.0',
            dependencies: ['dom-utils', 'event-utils', 'performance-utils'],
            lifecycle: {
                init: () => this.app.connectionRenderer,
                destroy: () => this.app.connectionRenderer?.cleanup?.()
            },
            interface: {
                render: 'function',
                updateAllConnections: 'function',
                addConnection: 'function',
                deleteConnection: 'function'
            }
        });

        // 注册控制器模块
        this.registerModule('controller', {
            name: 'ModuleController',
            version: '1.0.0',
            dependencies: ['event-utils'],
            lifecycle: {
                init: () => this.app.controller,
                destroy: () => this.app.controller?.cleanup?.()
            },
            interface: {
                startModule: 'function',
                stopModule: 'function',
                getModuleStatus: 'function'
            }
        });

        // 注册位置管理器模块
        this.registerModule('position-manager', {
            name: 'PositionManager',
            version: '1.0.0',
            dependencies: ['dom-utils', 'event-utils'],
            lifecycle: {
                init: () => this.app.positionManager,
                destroy: () => this.app.positionManager?.cleanup?.()
            },
            interface: {
                savePositions: 'function',
                loadPositions: 'function',
                enableDragging: 'function'
            }
        });
    }

    /**
     * 注册模块
     */
    registerModule(id, moduleConfig) {
        try {
            // 验证模块配置
            this.validateModuleConfig(moduleConfig);
            
            // 检查依赖
            this.checkDependencies(id, moduleConfig.dependencies);
            
            // 注册模块
            this.modules.set(id, {
                ...moduleConfig,
                id: id,
                status: 'registered',
                instance: null,
                registeredAt: Date.now()
            });
            
            // 记录依赖关系
            this.dependencies.set(id, moduleConfig.dependencies || []);
            
            console.log(`[ModuleInterface] 模块已注册: ${id}`);
            
            // 触发注册事件
            this.app.emit('module:registered', { id, config: moduleConfig });
            
            return true;
        } catch (error) {
            console.error(`[ModuleInterface] 模块注册失败 ${id}:`, error);
            return false;
        }
    }

    /**
     * 验证模块配置
     */
    validateModuleConfig(config) {
        const required = ['name', 'version', 'lifecycle', 'interface'];
        
        for (const field of required) {
            if (!config[field]) {
                throw new Error(`缺少必需字段: ${field}`);
            }
        }
        
        // 验证生命周期方法
        if (!config.lifecycle.init || typeof config.lifecycle.init !== 'function') {
            throw new Error('lifecycle.init 必须是函数');
        }
        
        // 验证接口定义
        if (typeof config.interface !== 'object') {
            throw new Error('interface 必须是对象');
        }
    }

    /**
     * 检查依赖
     */
    checkDependencies(moduleId, dependencies = []) {
        const missing = [];
        
        for (const dep of dependencies) {
            if (!this.isModuleAvailable(dep)) {
                missing.push(dep);
            }
        }
        
        if (missing.length > 0) {
            console.warn(`[ModuleInterface] 模块 ${moduleId} 缺少依赖:`, missing);
        }
        
        return missing;
    }

    /**
     * 检查模块是否可用
     */
    isModuleAvailable(moduleId) {
        // 检查已注册模块
        if (this.modules.has(moduleId)) {
            return true;
        }
        
        // 检查全局工具模块
        const globalModules = {
            'dom-utils': () => typeof DOMUtils !== 'undefined',
            'event-utils': () => typeof EventUtils !== 'undefined',
            'performance-utils': () => typeof PerformanceUtils !== 'undefined'
        };
        
        if (globalModules[moduleId]) {
            return globalModules[moduleId]();
        }
        
        return false;
    }

    /**
     * 初始化模块
     */
    async initializeModule(moduleId) {
        const module = this.modules.get(moduleId);
        if (!module) {
            throw new Error(`模块未注册: ${moduleId}`);
        }
        
        if (module.status === 'initialized') {
            console.warn(`[ModuleInterface] 模块已初始化: ${moduleId}`);
            return module.instance;
        }
        
        try {
            // 执行beforeInit钩子
            await this.executeHooks('beforeInit', { moduleId, module });
            
            // 初始化依赖
            await this.initializeDependencies(moduleId);
            
            // 初始化模块
            module.status = 'initializing';
            const instance = await module.lifecycle.init();
            
            if (instance) {
                module.instance = instance;
                module.status = 'initialized';
                module.initializedAt = Date.now();
                
                // 验证接口
                this.validateModuleInterface(moduleId, instance, module.interface);
                
                // 执行afterInit钩子
                await this.executeHooks('afterInit', { moduleId, module, instance });
                
                console.log(`[ModuleInterface] 模块已初始化: ${moduleId}`);
                this.app.emit('module:initialized', { id: moduleId, instance });
                
                return instance;
            } else {
                throw new Error('模块初始化返回空实例');
            }
        } catch (error) {
            module.status = 'error';
            module.error = error;
            
            // 执行错误钩子
            await this.executeHooks('onError', { moduleId, module, error });
            
            console.error(`[ModuleInterface] 模块初始化失败 ${moduleId}:`, error);
            this.app.emit('module:init-failed', { id: moduleId, error });
            
            throw error;
        }
    }

    /**
     * 初始化依赖
     */
    async initializeDependencies(moduleId) {
        const dependencies = this.dependencies.get(moduleId) || [];
        
        for (const depId of dependencies) {
            if (this.modules.has(depId)) {
                const depModule = this.modules.get(depId);
                if (depModule.status !== 'initialized') {
                    await this.initializeModule(depId);
                }
            }
        }
    }

    /**
     * 验证模块接口
     */
    validateModuleInterface(moduleId, instance, interfaceSpec) {
        for (const [method, type] of Object.entries(interfaceSpec)) {
            if (type === 'function' && typeof instance[method] !== 'function') {
                console.warn(`[ModuleInterface] 模块 ${moduleId} 缺少方法: ${method}`);
            }
        }
    }

    /**
     * 销毁模块
     */
    async destroyModule(moduleId) {
        const module = this.modules.get(moduleId);
        if (!module || module.status !== 'initialized') {
            return;
        }
        
        try {
            // 执行beforeDestroy钩子
            await this.executeHooks('beforeDestroy', { moduleId, module });
            
            // 销毁模块
            if (module.lifecycle.destroy) {
                await module.lifecycle.destroy();
            }
            
            module.status = 'destroyed';
            module.instance = null;
            module.destroyedAt = Date.now();
            
            // 执行afterDestroy钩子
            await this.executeHooks('afterDestroy', { moduleId, module });
            
            console.log(`[ModuleInterface] 模块已销毁: ${moduleId}`);
            this.app.emit('module:destroyed', { id: moduleId });
            
        } catch (error) {
            console.error(`[ModuleInterface] 模块销毁失败 ${moduleId}:`, error);
            await this.executeHooks('onError', { moduleId, module, error });
        }
    }

    /**
     * 热重载模块
     */
    async reloadModule(moduleId) {
        console.log(`[ModuleInterface] 热重载模块: ${moduleId}`);
        
        try {
            // 销毁现有模块
            await this.destroyModule(moduleId);
            
            // 重新初始化
            await this.initializeModule(moduleId);
            
            this.app.emit('module:reloaded', { id: moduleId });
            console.log(`[ModuleInterface] 模块热重载完成: ${moduleId}`);
            
        } catch (error) {
            console.error(`[ModuleInterface] 模块热重载失败 ${moduleId}:`, error);
            throw error;
        }
    }

    /**
     * 执行钩子
     */
    async executeHooks(hookName, context) {
        const hooks = this.hooks.get(hookName) || [];
        
        for (const hook of hooks) {
            try {
                await hook(context);
            } catch (error) {
                console.error(`[ModuleInterface] 钩子执行失败 ${hookName}:`, error);
            }
        }
    }

    /**
     * 添加钩子
     */
    addHook(hookName, callback) {
        if (!this.hooks.has(hookName)) {
            this.hooks.set(hookName, []);
        }
        
        this.hooks.get(hookName).push(callback);
    }

    /**
     * 获取模块信息
     */
    getModuleInfo(moduleId) {
        return this.modules.get(moduleId);
    }

    /**
     * 获取所有模块
     */
    getAllModules() {
        return Array.from(this.modules.entries()).map(([id, module]) => ({
            id,
            ...module
        }));
    }

    /**
     * 获取依赖图
     */
    getDependencyGraph() {
        const graph = {};
        
        this.dependencies.forEach((deps, moduleId) => {
            graph[moduleId] = deps;
        });
        
        return graph;
    }

    /**
     * 清理资源
     */
    async cleanup() {
        console.log('[ModuleInterface] 开始清理模块...');
        
        // 按依赖顺序销毁模块
        const destroyOrder = this.getDestroyOrder();
        
        for (const moduleId of destroyOrder) {
            await this.destroyModule(moduleId);
        }
        
        this.modules.clear();
        this.dependencies.clear();
        this.lifecycle.clear();
        this.hooks.clear();
        
        console.log('[ModuleInterface] 模块清理完成');
    }

    /**
     * 获取销毁顺序（依赖的反向）
     */
    getDestroyOrder() {
        const visited = new Set();
        const order = [];
        
        const visit = (moduleId) => {
            if (visited.has(moduleId)) return;
            visited.add(moduleId);
            
            // 先访问依赖此模块的其他模块
            this.dependencies.forEach((deps, id) => {
                if (deps.includes(moduleId)) {
                    visit(id);
                }
            });
            
            order.push(moduleId);
        };
        
        this.modules.forEach((_, moduleId) => {
            visit(moduleId);
        });
        
        return order;
    }
}

// 导出模块接口类
window.ModuleInterface = ModuleInterface;
