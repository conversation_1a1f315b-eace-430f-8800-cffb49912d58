# ===================== 个股人气表.py =====================
# TradeFusion股票人气数据融合处理模块
# 功能：多源人气数据智能融合、综合评分计算、质量控制
# 版本: v2.1 - 优化日志表达，准确反映模块功能
# {{ AURA-X: Modify - 将SQLite连接改为PostgreSQL连接. Approval: 寸止(ID:1737734400). }}
import psycopg2
import psycopg2.extras
import sys
import logging
import os
from pathlib import Path
import time
import hashlib
from typing import List, Tuple, Optional, Dict, Any
from logging.handlers import TimedRotatingFileHandler

project_root = Path(__file__).absolute().parent.parent
if str(project_root) not in sys.path:
    sys.path.append(str(project_root))

from 公共模块.交易日期 import get_trading_date

# 导入TradeFusion统一日志标准
from 公共模块.TradeFusion统一日志标准 import 获取日志器

# 获取统一日志器
logger = 获取日志器("个股人气表")

class PopularityDatabase:
    """
    股票人气数据融合处理器

    核心功能：
    1. 多源数据融合：整合东财、同花顺等不同来源的人气排名数据
    2. 智能评分计算：基于多源数据计算综合人气评分（东财70% + 同花30%）
    3. 质量控制：自动清理掉出榜单、单一数据源、低质量记录
    4. 临时表集成：从临时表读取待处理数据并进行融合处理
    5. 数据一致性：确保数据完整性和时间一致性
    """
    def __init__(self, db_config: Optional[Dict[str, Any]] = None):
        # {{ AURA-X: Modify - 修改为PostgreSQL连接配置. Approval: 寸止(ID:1737734400). }}
        # {{ AURA-X: Modify - 使用统一配置管理而非硬编码. Approval: 寸止(ID:1737734400). }}
        if db_config is None:
            try:
                from 公共模块.配置管理 import get_config
                config = get_config()
                self.db_config = config.get('database')
            except ImportError:
                # 备用配置
                self.db_config = {
                    'host': 'localhost',
                    'port': 5432,
                    'database': 'tradefusion',
                    'user': 'postgres',
                    'password': 'ymjatTUU520'
                }
        else:
            self.db_config = db_config

        self.conn: Optional[psycopg2.extensions.connection] = None
        self._connect()
        self._ensure_stock_name_column()

    def _connect(self):
        # {{ AURA-X: Modify - 修改为PostgreSQL连接方法. Approval: 寸止(ID:1737734400). }}
        try:
            self.conn = psycopg2.connect(**self.db_config)
            self.conn.autocommit = False  # 使用事务模式
            logger.debug("PostgreSQL数据库连接成功")
        except Exception as e:
            logger.记录错误("PostgreSQL数据库连接失败", e)
            raise

    def _ensure_stock_name_column(self):
        """确保个股人气表中有股票名称列"""
        # {{ AURA-X: Modify - 修改为PostgreSQL列检查语法. Approval: 寸止(ID:1737734400). }}
        try:
            cursor = self.conn.cursor()
            cursor.execute("""
                SELECT column_name
                FROM information_schema.columns
                WHERE table_name = '个股人气表' AND table_schema = 'public'
            """)
            columns = [row[0] for row in cursor.fetchall()]

            if "股票名称" not in columns:
                cursor.execute('ALTER TABLE "个股人气表" ADD COLUMN "股票名称" VARCHAR(50);')
                self.conn.commit()
                logger.debug("已添加股票名称列")

        except Exception as e:
            logger.记录错误("添加股票名称列失败", e)
            raise

    def _create_temp_table(self, stock_data: List[Tuple[str, str]]):
        # {{ AURA-X: Modify - 修改为PostgreSQL临时表语法. Approval: 寸止(ID:1737734400). }}
        try:
            cursor = self.conn.cursor()
            # PostgreSQL临时表语法
            cursor.execute("""
                CREATE TEMP TABLE IF NOT EXISTS temp_top100 (
                    "股票代码" CHAR(8) PRIMARY KEY,
                    "股票名称" VARCHAR(50)
                )
            """)
            cursor.execute('DELETE FROM temp_top100;')
            # PostgreSQL使用executemany
            cursor.executemany(
                'INSERT INTO temp_top100 ("股票代码", "股票名称") VALUES (%s, %s) ON CONFLICT ("股票代码") DO UPDATE SET "股票名称" = EXCLUDED."股票名称";',
                stock_data
            )
            self.conn.commit()
        except Exception as e:
            logger.记录错误("创建临时表失败", e)
            raise

    def _update_popularity(self, date: int, field: str, stock_data: List[Tuple[str, str, int]]):
        from 公共模块 import 交易日期 as riqi
        min_date = int(min(h['start_date'].strftime('%Y%m%d') for h in riqi.holiday_list))
        max_date = int(max(h['end_date'].strftime('%Y%m%d') for h in riqi.holiday_list))
        if not (min_date <= date <= max_date):
            # 超出范围，保持原有逻辑
            if not stock_data:
                return
            try:
                # {{ AURA-X: Modify - 修改为PostgreSQL UPSERT语法. Approval: 寸止(ID:1737734400). }}
                cursor = self.conn.cursor()
                cursor.executemany(
                    f'''INSERT INTO "个股人气表" ("日期", "股票代码", "股票名称", "{field}")
                        VALUES (%s, %s, %s, %s)
                        ON CONFLICT ("日期", "股票代码") DO UPDATE SET
                        "股票名称" = EXCLUDED."股票名称",
                        "{field}" = EXCLUDED."{field}";''',
                    [(date, code, name, popularity) for code, name, popularity in stock_data]
                )
                # {{ AURA-X: Modify - 移除commit避免递归重入，由外层事务管理. Approval: 寸止(ID:1737734400). }}
            except Exception as e:
                logger.记录错误(f"更新{field}数据失败", e)
                raise
            return
        # 在时间范围内，先将当日所有同花人气排名置为NULL，再写入
        if not stock_data:
            return
        try:
            # {{ AURA-X: Modify - 修改为PostgreSQL语法. Approval: 寸止(ID:1737734400). }}
            cursor = self.conn.cursor()
            # 先将当日所有字段置为NULL
            cursor.execute(f'UPDATE "个股人气表" SET "{field}"=NULL WHERE "日期"=%s', (date,))
            cursor.executemany(
                f'''INSERT INTO "个股人气表" ("日期", "股票代码", "股票名称", "{field}")
                    VALUES (%s, %s, %s, %s)
                    ON CONFLICT ("日期", "股票代码") DO UPDATE SET
                    "股票名称" = EXCLUDED."股票名称",
                    "{field}" = EXCLUDED."{field}"
                    WHERE "个股人气表"."{field}" IS NULL OR "个股人气表"."{field}" != EXCLUDED."{field}";''',
                [(date, code, name, popularity) for code, name, popularity in stock_data]
            )
            # {{ AURA-X: Modify - 移除commit避免递归重入，由外层事务管理. Approval: 寸止(ID:1737734400). }}
        except Exception as e:
            raise

    def _zero_outdated_records(self, date: int, field: str):
        """
        清零过期记录的指定字段（分字段清零策略）

        Args:
            date: 交易日期
            field: 要清零的字段名（'东财人气排名' 或 '同花人气排名'）
        """
        try:
            # {{ AURA-X: Modify - 移除with语句避免递归重入. Approval: 寸止(ID:1737734400). }}
            # 根据字段类型构建不同的查询条件
            if field == '东财人气排名':
                # 只查找有东财数据但不在当前TOP100中的股票
                condition = "东财人气排名 IS NOT NULL"
            elif field == '同花人气排名':
                # 只查找有同花数据但不在当前TOP100中的股票
                condition = "同花人气排名 IS NOT NULL"
            else:
                raise ValueError(f"不支持的字段: {field}")

            # {{ AURA-X: Modify - 修改为PostgreSQL语法. Approval: 寸止(ID:1737734400). }}
            cursor = self.conn.cursor()
            cursor.execute(
                f'''SELECT "股票代码" FROM "个股人气表"
                   WHERE "日期" = %s AND "股票代码" IN (
                       SELECT "股票代码" FROM "个股人气表" WHERE "日期" = %s
                       EXCEPT SELECT "股票代码" FROM temp_top100
                   ) AND "{field.replace('人气排名', '人气排名')}" IS NOT NULL''',
                (date, date)
            )
            target_codes = [row[0] for row in cursor.fetchall()]

            if target_codes:
                # 只清零指定字段，设置为NULL（掉出榜单的数据）
                cursor.executemany(
                    f'''UPDATE "个股人气表" SET
                        "{field}" = NULL
                       WHERE "日期" = %s AND "股票代码" = %s''',
                    [(date, code) for code in target_codes]
                )
                # {{ AURA-X: Modify - 移除commit避免递归重入，由外层事务管理. Approval: 寸止(ID:1737734400). }}

                # 记录清零操作（调试信息，符合三色标准）
                logger.debug(f"\033[91m[个股人气表]\033[0m 清零掉出榜单的{field}: \033[92m{len(target_codes)}只\033[0m股票")

        except Exception as e:
            logger.记录错误(f"清零{field}失败", e)
            raise

    def _calculate_popularity_score(self, date: int):
        # {{ AURA-X: Modify - 修改为PostgreSQL语法. Approval: 寸止(ID:1737734400). }}
        try:
            cursor = self.conn.cursor()
            cursor.execute(
                '''UPDATE "个股人气表"
                   SET "综合人气评分" = CASE WHEN
                       ("东财人气排名" BETWEEN 1 AND 99) AND
                       ("同花人气排名" BETWEEN 1 AND 99) AND
                       ((100 - "东财人气排名" + 1) > 10) AND
                       ((100 - "同花人气排名" + 1) > 10)
                   THEN ROUND(
                       ((100 - "东财人气排名" + 1) * 0.7 +
                       (100 - "同花人气排名" + 1) * 0.3)::numeric, 2)
                   ELSE 0 END,
                   "更新时间戳" = NOW()
                   WHERE "日期" = %s''',
                (date,)
            )
            # {{ AURA-X: Modify - 移除commit避免递归重入，由外层事务管理. Approval: 寸止(ID:1737734400). }}
        except Exception as e:
            logger.记录错误("计算综合人气评分失败", e)
            raise

    def _check_data_freshness(self, date: int, source_type: str):
        """检查数据是否需要更新（基于时间戳的智能检测）"""
        try:
            cursor = self.conn.cursor()

            # {{ AURA-X: Modify - 修改为PostgreSQL语法. Approval: 寸止(ID:1737734400). }}
            # 检查最近更新时间
            if source_type == "东财":
                cursor.execute('''
                    SELECT MAX("更新时间戳") FROM "个股人气表"
                    WHERE "日期" = %s AND "东财人气排名" IS NOT NULL
                ''', (date,))
            else:  # 同花
                cursor.execute('''
                    SELECT MAX("更新时间戳") FROM "个股人气表"
                    WHERE "日期" = %s AND "同花人气排名" IS NOT NULL
                ''', (date,))

            last_update = cursor.fetchone()[0]

            if last_update:
                # 计算距离上次更新的分钟数
                cursor.execute('''
                    SELECT EXTRACT(EPOCH FROM (NOW() - %s)) / 60
                ''', (last_update,))
                minutes_since_update = cursor.fetchone()[0]

                # 东财数据10分钟内不重复计算，同花数据5分钟内不重复计算
                threshold = 10 if source_type == "东财" else 5

                if minutes_since_update < threshold:
                    logger.记录模块执行(f"{source_type}数据更新检测 - 距离上次更新{minutes_since_update:.1f}分钟，跳过重复计算")
                    return False

            return True

        except Exception as e:
            logger.记录错误("数据新鲜度检查失败，继续执行更新", e)
            return True

    def _delete_zero_popularity_records(self, date: int):
        """
        删除综合人气评分达不到要求的记录

        目的：
        1. 综合评分达不到要求的不要出现在数据表，避免观看和计算干扰
        2. 包含掉出榜单的股票（因为掉出榜单→评分=0→被删除）
        3. 包含单一数据源的股票（因为单一数据源→评分=0→被删除）
        """
        try:
            # {{ AURA-X: Modify - 修改为PostgreSQL语法. Approval: 寸止(ID:1737734400). }}
            cursor = self.conn.cursor()
            # 删除综合人气评分为0或NULL的整行记录
            cursor.execute(
                'DELETE FROM "个股人气表" WHERE "日期" = %s AND ("综合人气评分" = 0 OR "综合人气评分" IS NULL)',
                (date,)
            )
            deleted_count = cursor.rowcount
            # {{ AURA-X: Modify - 移除commit避免递归重入，由外层事务管理. Approval: 寸止(ID:1737734400). }}
            if deleted_count > 0:
                logger.debug(f"\033[91m[个股人气表]\033[0m 删除\033[93m个股人气表\033[0m低质量记录: \033[92m{deleted_count}条\033[0m")
            return deleted_count
        except Exception as e:
            logger.记录错误("删除低质量记录失败", e)
            raise



    def _update_flag_file(self, date: int, stock_data: list):
        """更新标志文件（保持向后兼容）"""
        # {{ AURA-X: Modify - 修改为PostgreSQL环境下的标志文件路径. Approval: 寸止(ID:1737734400). }}
        try:
            # 使用项目根目录下的标志文件
            project_root = Path(__file__).parent.parent
            flag_path = project_root / "数据库0_实体模块" / "db_ready.flag"
            with open(flag_path, 'w') as f:
                content = f"{date}|{hashlib.md5(str(stock_data).encode()).hexdigest()}"
                f.write(content)
        except Exception:
            # 标志文件更新失败不影响主要功能，静默处理
            pass

    def update_dcf_popularity(self, date: int = None, stock_data: list = None):
        """
        更新东财人气排名数据（优化版本 - 集成WebSocket直接推送）
        Args:
            date: 交易日期，如果为None则从公共模块获取当前交易日期
            stock_data: 包含(股票代码, 股票名称, 东财人气)的三元组列表
        """
        if date is None:
            date = get_trading_date()

        if stock_data is None:
            stock_data = []

        # 智能更新检测：检查是否需要更新
        if not self._check_data_freshness(date, "东财"):
            return  # 跳过重复更新

        try:
            # 记录处理结果（符合TradeFusion统一日志标准）
            logger.记录模块执行("东财人气数据融合完成", len(stock_data))

            with self.conn:
                # 1. 更新东财人气数据
                self._create_temp_table([(code, name) for code, name, _ in stock_data])
                self._update_popularity(date, '东财人气排名', stock_data)
                self._zero_outdated_records(date, '东财人气排名')

                # 2. 计算综合人气评分（但不删除低质量记录，等待全部数据更新完成）
                self._calculate_popularity_score(date)

                # 注意：不在此处删除低质量记录，避免单一数据源时误删

                # 4. 更新标志文件
                self._update_flag_file(date, stock_data)

        except Exception as e:
            logger.记录错误("东财人气数据更新失败", e)
            raise

    def update_ths_popularity(self, date: int = None, stock_data: list = None):
        """
        更新同花顺人气排名数据（优化版本 - 集成WebSocket直接推送）
        Args:
            date: 交易日期，如果为None则从公共模块获取当前交易日期
            stock_data: 包含(股票代码, 股票名称, 同花人气)的三元组列表
        """
        if date is None:
            date = get_trading_date()

        if stock_data is None:
            stock_data = []

        # 智能更新检测：检查是否需要更新
        if not self._check_data_freshness(date, "同花"):
            return  # 跳过重复更新

        try:
            # 记录处理结果（符合TradeFusion统一日志标准）
            logger.记录模块执行("同花人气数据融合完成", len(stock_data))

            with self.conn:
                # 1. 更新同花人气数据
                self._create_temp_table([(code, name) for code, name, _ in stock_data])
                self._update_popularity(date, '同花人气排名', stock_data)
                self._zero_outdated_records(date, '同花人气排名')

                # 2. 计算综合人气评分（但不删除低质量记录，等待全部数据更新完成）
                self._calculate_popularity_score(date)

                # 注意：不在此处删除低质量记录，避免单一数据源时误删

                # 4. 更新标志文件
                self._update_flag_file(date, stock_data)

        except Exception as e:
            logger.记录错误("同花顺人气数据更新失败", e)
            raise

    def 从临时表更新东财人气(self, date: int = None):
        """
        从临时表读取东财人气数据并更新到正式表
        Args:
            date: 交易日期，如果为None则从公共模块获取当前交易日期
        """
        if date is None:
            date = get_trading_date()
        try:
            from 数据2_网络采集.人气临时表管理 import 获取临时表管理器
            temp_manager = 获取临时表管理器()

            # 读取待处理数据
            temp_data = temp_manager.读取东财待处理数据()

            if temp_data:
                # 转换数据格式：(股票代码, 股票名称, 人气排名) -> (股票代码, 股票名称, 东财人气)
                stock_data = [(code, name, rank) for code, name, rank in temp_data]

                # 调用现有的更新方法（传递正确的日期参数）
                self.update_dcf_popularity(date=date, stock_data=stock_data)

                # 标记临时表数据为已处理
                temp_manager.标记东财数据已处理()

            temp_manager.close()
            return len(temp_data) if temp_data else 0

        except Exception as e:
            logger.记录错误("从东财临时表更新失败", e)
            raise

    def 从临时表更新同花人气(self, date: int = None):
        """
        从临时表读取同花人气数据并更新到正式表
        Args:
            date: 交易日期，如果为None则从公共模块获取当前交易日期
        """
        if date is None:
            date = get_trading_date()
        try:
            from 数据2_网络采集.人气临时表管理 import 获取临时表管理器
            temp_manager = 获取临时表管理器()

            # 读取待处理数据
            temp_data = temp_manager.读取同花待处理数据()

            if temp_data:
                # 转换数据格式：(股票代码, 股票名称, 人气排名) -> (股票代码, 股票名称, 同花人气)
                stock_data = [(code, name, rank) for code, name, rank in temp_data]

                # 调用现有的更新方法（传递正确的日期参数）
                self.update_ths_popularity(date=date, stock_data=stock_data)

                # 标记临时表数据为已处理
                temp_manager.标记同花数据已处理()

            temp_manager.close()
            return len(temp_data) if temp_data else 0

        except Exception as e:
            logger.记录错误("从同花临时表更新失败", e)
            raise

    def 从临时表更新全部人气数据(self, date: int = None):
        """
        从临时表读取全部人气数据并更新到正式表
        Args:
            date: 交易日期，如果为None则使用当前交易日
        """
        if date is None:
            date = get_trading_date()

        try:
            # 更新东财数据（使用公共模块交易日期）
            dcf_count = self.从临时表更新东财人气()

            # 更新同花数据（使用公共模块交易日期）
            ths_count = self.从临时表更新同花人气()

            total_count = dcf_count + ths_count

            # 最终处理：计算综合评分并删除低质量记录
            if total_count > 0:
                self._calculate_popularity_score(date)
                deleted_count = self._delete_zero_popularity_records(date)

                # {{ AURA-X: Modify - 修改为PostgreSQL语法. Approval: 寸止(ID:1737734400). }}
                # 统计最终结果
                cursor = self.conn.cursor()
                cursor.execute(
                    'SELECT COUNT(*) FROM "个股人气表" WHERE "日期" = %s AND "综合人气评分" > 0',
                    (date,)
                )
                final_count = cursor.fetchone()[0]
                logger.记录模块执行(f"最终数据处理完成 - 删除{deleted_count}条低质量记录", final_count)

            return total_count

        except Exception as e:
            logger.记录错误("从临时表更新全部数据失败", e)
            raise

    def upsert_dcf_popularity(self, data: list):
        """
        data: List of (日期, 股票代码, 股票名称, 东财人气)
        以(日期, 股票代码)为主键，插入或更新东财人气排名

        Args:
            data: 股票数据列表
        """
        insert_count = 0
        update_count = 0
        skip_count = 0

        # {{ AURA-X: Modify - 修改为PostgreSQL语法. Approval: 寸止(ID:1737734400). }}
        cursor = self.conn.cursor()
        for row in data:
            date, code, name, dcf_rank = row
            cursor.execute(
                'SELECT "东财人气排名" FROM "个股人气表" WHERE "日期"=%s AND "股票代码"=%s',
                (date, code)
            )
            result = cursor.fetchone()
            if result is None:
                # 插入新记录
                cursor.execute(
                    'INSERT INTO "个股人气表" ("日期", "股票代码", "股票名称", "东财人气排名") VALUES (%s, %s, %s, %s)',
                    (date, code, name, dcf_rank)
                )
                insert_count += 1
            else:
                old_rank = result[0]
                if old_rank == dcf_rank:
                    skip_count += 1
                else:
                    cursor.execute(
                        'UPDATE "个股人气表" SET "东财人气排名"=%s, "股票名称"=%s WHERE "日期"=%s AND "股票代码"=%s',
                        (dcf_rank, name, date, code)
                    )
                    update_count += 1

        self.conn.commit()

        # 执行完整的业务逻辑（与update_dcf_popularity保持一致）
        try:
            # 获取当前日期（从数据中提取）
            current_date = data[0][0] if data else None
            if current_date:
                # 0. 创建临时表（必需的前置条件）
                stock_codes_names = [(code, name) for _, code, name, _ in data]
                self._create_temp_table(stock_codes_names)

                # 1. 清零过期记录（只清零东财字段）
                self._zero_outdated_records(current_date, '东财人气排名')

                # 2. 计算综合人气评分
                self._calculate_popularity_score(current_date)

                # 3. 删除综合人气评分达不到要求的记录（包含掉出榜单和低质量）
                deleted_count = self._delete_zero_popularity_records(current_date)

                # {{ AURA-X: Modify - 修改为PostgreSQL语法. Approval: 寸止(ID:1737734400). }}
                # 4. 统计最终符合条件的记录数
                cursor = self.conn.cursor()
                cursor.execute(
                    'SELECT COUNT(*) FROM "个股人气表" WHERE "日期" = %s AND "综合人气评分" > 0',
                    (current_date,)
                )
                final_count = cursor.fetchone()[0]

                self.conn.commit()

                # 记录完整的处理结果（符合TradeFusion统一日志标准）
                logger.记录模块执行(f"人气数据融合处理完成 - 新增{insert_count}条,更新{update_count}条,删除{deleted_count}条", final_count, "人气_东财采集")
            else:
                # 记录处理结果（符合TradeFusion统一日志标准）
                logger.记录模块执行(f"人气数据融合处理完成 - 新增{insert_count}条,更新{update_count}条,跳过{skip_count}条", insert_count + update_count, "人气_东财采集")

        except Exception as e:
            logger.记录错误("后续处理失败", e)
            raise


    def __del__(self):
        if self.conn:
            try:
                self.conn.close()
            except:
                pass

def main():
    """主函数 - 用于测试个股人气表功能"""
    try:
        # 创建数据库实例
        db = PopularityDatabase()

        # 测试从临时表更新全部人气数据
        total_count = db.从临时表更新全部人气数据()

        # 关闭数据库连接
        if hasattr(db, 'conn') and db.conn:
            db.conn.close()

        logger.记录模块执行("人气数据融合测试完成", total_count)

        # 🔄 个股人气表更新完成后，自动触发输出模块
        _trigger_output_modules()

        return True

    except Exception as e:
        logger.记录错误("测试失败", e)
        return False

def _trigger_output_modules():
    """触发个股人气表的输出模块"""
    try:
        # 触发综合人气190模块
        from 数据库写大智慧.综合人气190 import main as 综合人气190_main

        logger.记录模块执行("自动触发综合人气190模块")

        result = 综合人气190_main()
        if result and not result.startswith('failed'):
            logger.记录模块执行("综合人气190模块执行成功")
        else:
            logger.记录错误(f"综合人气190模块执行失败: {result}")

        # 触发生成DZH3人气板块模块
        from 数据库写大智慧.生成DZH3人气股票板块 import main as DZH3人气板块_main

        logger.记录模块执行("自动触发生成DZH3人气板块模块")

        result = DZH3人气板块_main()
        if result and not result.startswith('failed'):
            logger.记录模块执行("生成DZH3人气板块模块执行成功")
        else:
            logger.记录错误(f"生成DZH3人气板块模块执行失败: {result}")

    except Exception as e:
        logger.记录错误("触发输出模块异常", e)

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)