# {{ AURA-X: Modify - 将SQLite连接改为PostgreSQL连接. Approval: 寸止(ID:1737734400). }}
import psycopg2
import psycopg2.extras
import os
import struct
import sys
from collections import OrderedDict
from datetime import datetime
from pathlib import Path

# 设置项目根目录
current_file = Path(__file__).resolve()
project_root = current_file.parent.parent

if str(project_root) not in sys.path:
    sys.path.insert(0, str(project_root))

# 导入TradeFusion统一日志标准
from 公共模块.TradeFusion统一日志标准 import 获取日志器
logger = 获取日志器("选股宝_大智慧str")

# {{ AURA-X: Modify - 修改为PostgreSQL连接配置. Approval: 寸止(ID:1737734400). }}
class STRGenerator:
    def __init__(self, db_config, str_path, mode):
        self.db_config = db_config
        self.str_path = str_path
        self.mode = mode  # '板块消息'/'个股解读'/'板块名称'
        self.block_size = 512
        self.content_max = 500
        self.existing_data = OrderedDict()
        
        if os.path.exists(str_path):
            self._load_existing_str()

    def _load_existing_str(self):
        with open(self.str_path, 'rb') as f:
            while True:
                block = f.read(self.block_size)
                if not block:
                    break
                code = block[:8].decode('ascii').rstrip('\x00')
                self.existing_data[code] = block

    # {{ AURA-X: Modify - 修改为PostgreSQL连接. Approval: 寸止(ID:1737734400). }}
    def _get_db_data(self):
        conn = psycopg2.connect(**self.db_config)
        cursor = conn.cursor()
        
        if self.mode == '板块消息':
            # {{ AURA-X: Modify - 修改为PostgreSQL兼容的STRING_AGG函数. Approval: 寸止(ID:1735002400). }}
            query = """
            SELECT a.股票代码, STRING_AGG(b.板块消息, '※')
            FROM 个股板块关联表 a
            JOIN (
                SELECT 板块名称, 板块消息
                FROM (
                    SELECT
                        板块名称,
                        板块消息,
                        ROW_NUMBER() OVER (PARTITION BY 板块名称 ORDER BY 日期 DESC) AS rn
                    FROM 板块信息表
                )
                WHERE rn = 1
            ) b ON a.所属板块名称 = b.板块名称
            GROUP BY a.股票代码
            """
        elif self.mode == '板块名称':
            # {{ AURA-X: Modify - 修改为PostgreSQL兼容的STRING_AGG函数. Approval: 寸止(ID:1735002400). }}
            query = """
            SELECT 股票代码, STRING_AGG(所属板块名称, '※')
            FROM 个股板块关联表
            WHERE 日期 = (SELECT MAX(日期) FROM 个股板块关联表)
            GROUP BY 股票代码
            """
        else:  # 个股解读
            query = """
            SELECT 股票代码, 个股解读 
            FROM 个股解读表
            WHERE 日期 = (SELECT MAX(日期) FROM 个股解读表)
            """
            
        cursor.execute(query)
        return {row[0]: row[1] for row in cursor.fetchall()}

    def _process_content(self, content):
        try:
            encoded = content.encode('gbk', errors='replace')
        except Exception as e:
            encoded = b''
        return encoded[:self.content_max].ljust(self.content_max, b'\x00')

    def generate(self):
        db_data = self._get_db_data()
        update_count = 0
        
        for code, content in db_data.items():
            processed = self._process_content(content)
            new_block = self._create_block(code, processed)
            self.existing_data[code] = new_block
            update_count += 1

        final_blocks = list(self.existing_data.values())
        total_count = len(final_blocks)
        
        with open(self.str_path, 'wb') as f:
            for block in final_blocks:
                f.write(block)
        
        return update_count, total_count

    def _create_block(self, code, content_bytes):
        code_bytes = code.ljust(8, '\x00').encode('ascii')
        separator = b'\x00\x00\x00\x00'
        padding = b'\x00' * (self.block_size - 8 - 4 - len(content_bytes))
        return code_bytes + separator + content_bytes + padding

def generate_str_files():
    """生成板块消息、个股解读、板块名称的 STR 文件"""
    # 导入配置管理
    # {{ AURA-X: Modify - 修改为PostgreSQL连接配置. Approval: 寸止(ID:1737734400). }}
    try:
        from 公共模块.配置管理 import get_config
        config_manager = get_config()
        dzh_base_path = config_manager.get('data_sources.dzh_base_path')
    except ImportError as e:
        dzh_base_path = r'E:\dzh2\USERDATA\SelfData'

    # {{ AURA-X: Modify - 统一PostgreSQL密码为标准密码. Approval: 寸止(ID:1737734400). }}
    # PostgreSQL连接配置
    db_config = {
        'host': 'localhost',
        'port': 5432,
        'database': 'tradefusion',
        'user': 'postgres',
        'password': 'ymjatTUU520'
    }

    config = {
        'db_config': db_config,
        '板块消息': os.path.join(dzh_base_path, '涨停_xgb板块消息.str'),
        '个股解读': os.path.join(dzh_base_path, '涨停_xgb解读.str'),
        '板块名称': os.path.join(dzh_base_path, '涨停_xgb板块.str')
    }

    total_update_count = 0
    total_record_count = 0
    success = True
    error_message = ""

    # {{ AURA-X: Modify - 修改STRGenerator调用参数. Approval: 寸止(ID:1737734400). }}
    for mode in ['板块消息', '个股解读', '板块名称']:
        try:
            generator = STRGenerator(
                db_config=config['db_config'],
                str_path=config[mode],
                mode=mode
            )
            update_count, record_count = generator.generate()
            total_update_count += update_count
            total_record_count += record_count
        except Exception as e:
            success = False
            error_message = str(e)
            break

    # 处理完成
    date_str = datetime.now().strftime("%Y-%m-%d")
    if success:
        logger.记录模块执行("STR文件生成完成", total_update_count)
    else:
        logger.记录错误(f"STR文件生成失败 - {error_message}")

if __name__ == "__main__":
    generate_str_files()