# TradeFusion日志系统文档中心

## 📋 文档导航

### ⚠️ 权威指导规则（唯一）
- **[新模块日志添加指导规则.md](./新模块日志添加指导规则.md)** - 新模块日志添加权威指南
  - 必须遵循的实施步骤和核心原则
  - 模块分类指导和完整代码模板
  - 质量检查清单和常见错误避免
  - 三色标识系统规范（🔴模块名 🟡表名 🟢数字）

### 📊 系统记录文档
- **[TradeFusion日志系统文件清单.md](./TradeFusion日志系统文件清单.md)** - 日志系统文件总览
- **[重组完成报告.md](./重组完成报告.md)** - 日志系统重组完成记录

## 🚀 快速开始

### 1. 使用统一日志标准
```python
from 公共模块.TradeFusion统一日志标准 import 获取日志器

# 获取模块日志器
logger = 获取日志器("你的模块名")

# 记录模块执行
logger.记录模块执行("数据采集完成", 1234, "调用方模块")

# 记录数据库操作
logger.记录数据库操作([("表名1", 100), ("表名2", 200)], "调用方模块")

# 记录错误
logger.记录错误("错误描述", 异常对象)
```

### 2. 日志格式标准
```
[时间戳] 🔴[模块名] 操作描述 - 🟡表名+🟢数字条 (由🔴[调用方]调用)
```

### 3. 三色标识系统
- 🔴 **红色**: 模块名称 `\033[91m[模块名]\033[0m`
- 🟡 **黄色**: 数据库表名 `\033[93m表名\033[0m`
- 🟢 **绿色**: 数字数据 `\033[92m{数量:,}\033[0m`

## 🛠️ 日志工具

### 核心模块
- **TradeFusion统一日志标准.py** - 统一日志记录器（位于 `公共模块/`）
- **日志系统性能分析器.py** - 性能分析工具（位于 `公共模块/日志系统/`）
- **日志监控器.py** - 实时监控工具（位于 `公共模块/日志系统/工具/`）

### 便捷启动脚本
- **启动日志监控器.py** - 日志监控器便捷启动脚本
- **启动性能分析器.py** - 性能分析器便捷启动脚本

### 配置系统
- **logging.yaml** - 统一日志配置文件（位于 `配置/`）
- **配置加载器.py** - 配置加载和管理工具

### 快速启动工具
```bash
# 启动日志监控器
python 公共模块/日志系统/启动日志监控器.py

# 启动性能分析器
python 公共模块/日志系统/启动性能分析器.py

# 测试配置加载器
python 公共模块/日志系统/配置/配置加载器.py
```

### 日志存储
- **logs/** - 日志文件存储目录
  - 31个活跃日志文件
  - 自动轮转和历史备份

## 📈 系统状态

### 当前覆盖率
- **模块覆盖率**: 100% (所有业务模块都有对应日志)
- **标准化率**: 100% (统一使用三色日志标准)
- **监控覆盖率**: 100% (所有日志文件都被监控)

### 性能特性
- ✅ 日志器缓存机制
- ✅ 双重输出（控制台+文件）
- ✅ UTF-8编码支持
- ✅ 千分位数字格式化
- ✅ 文件轮转（50MB限制）

## 🔄 重组进度

### ✅ 已完成
- [x] 阶段一：文档整合
  - [x] 创建日志系统文档目录结构
  - [x] 移动现有日志文档
  - [x] 创建文档索引
- [x] 阶段二：工具集中
  - [x] 移动日志监控器到工具目录
  - [x] 移动性能分析器到日志系统目录
  - [x] 创建便捷启动脚本
- [x] 阶段三：配置优化
  - [x] 创建独立日志配置文件
  - [x] 从data_sources.yaml分离日志配置
  - [x] 创建配置加载器
- [x] 阶段四：验证测试
  - [x] 测试所有日志功能
  - [x] 验证配置正确性
  - [x] 更新使用文档

## 🎉 重组完成状态

### 新的目录结构
```
公共模块/日志系统/
├── 日志系统性能分析器.py      # 性能分析工具
├── 启动日志监控器.py          # 监控器启动脚本
├── 启动性能分析器.py          # 分析器启动脚本
├── 工具/
│   └── 日志监控器.py          # 实时日志监控器
├── 配置/
│   ├── logging.yaml           # 统一日志配置文件
│   └── 配置加载器.py          # 配置加载器
└── 文档/
    ├── README.md              # 文档索引
    ├── TradeFusion日志系统规范.md
    ├── TradeFusion日志系统文件清单.md
    └── TradeFusion日志系统全面总结与重组建议.md
```

## 📞 支持与维护

### 问题反馈
如果在使用日志系统过程中遇到问题，请：
1. 查看权威指导规则文档
2. 检查日志配置是否正确
3. 使用日志监控器实时查看日志输出

### 文档更新
本文档会随着日志系统的演进持续更新，请关注：
- 新功能的使用说明
- 配置变更的迁移指南
- 最佳实践的更新

---

**最后更新**: 2025-07-23  
**文档版本**: v1.0  
**维护状态**: 活跃维护
