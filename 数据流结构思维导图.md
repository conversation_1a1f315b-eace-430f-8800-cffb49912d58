# TradeFusion 数据流结构思维导图

## 图例说明
- 🔲 **模块**：方块表示 `[模块名]`
- 🗄️ **数据库**：圆柱体表示 `[(数据库名)]`
- ➡️ **数据流**：实线表示 `-->`
- ⚡ **调用关系**：虚线表示 `-.->` 

## 数据流结构图

```mermaid
graph TD
    %% 样式定义
    classDef moduleStyle fill:#e1f5fe,stroke:#01579b,stroke-width:2px
    classDef dbStyle fill:#f3e5f5,stroke:#4a148c,stroke-width:2px
    classDef dataFlow stroke:#2e7d32,stroke-width:2px
    classDef callFlow stroke:#d84315,stroke-width:2px,stroke-dasharray: 5 5

    %% 示例结构 - 可根据实际需求修改
    subgraph "第1层：数据采集层"
        A1[数据源模块1]
        A2[数据源模块2]
        A3[数据源模块3]
    end
    
    subgraph "第2层：数据处理层"
        B1[数据清洗模块]
        B2[数据转换模块]
        B3[数据验证模块]
    end
    
    subgraph "第3层：数据存储层"
        C1[(原始数据表)]
        C2[(清洗数据表)]
        C3[(业务数据表)]
    end
    
    subgraph "第4层：业务逻辑层"
        D1[业务计算模块]
        D2[策略分析模块]
        D3[报表生成模块]
    end
    
    subgraph "第5层：输出层"
        E1[(结果数据表)]
        E2[输出接口]
    end

    %% 数据流连接（实线）
    A1 --> C1
    A2 --> C1
    A3 --> C1
    
    C1 --> B1
    B1 --> C2
    B2 --> C2
    B3 --> C3
    
    C2 --> D1
    C3 --> D1
    D1 --> E1
    D2 --> E1
    
    E1 --> E2

    %% 调用关系连接（虚线）
    B1 -.-> B2
    B2 -.-> B3
    D1 -.-> D2
    D2 -.-> D3

    %% 应用样式
    class A1,A2,A3,B1,B2,B3,D1,D2,D3,E2 moduleStyle
    class C1,C2,C3,E1 dbStyle
```

## 使用说明

1. **添加新模块**：在相应层级添加 `[模块名]`
2. **添加新数据库**：在相应位置添加 `[(数据库名)]`
3. **添加数据流**：使用 `-->` 连接数据流向
4. **添加调用关系**：使用 `-.->` 连接模块调用关系
5. **分层管理**：按照业务逻辑分层组织，便于维护

## 扩展建议

- 可以根据实际项目需求调整层级结构
- 可以添加更多子图（subgraph）来组织复杂的模块关系
- 可以使用不同颜色区分不同类型的模块
- 可以添加注释说明特殊的数据流或调用关系

---
*创建时间：{{ 当前时间 }}*
*版本：v1.0*
