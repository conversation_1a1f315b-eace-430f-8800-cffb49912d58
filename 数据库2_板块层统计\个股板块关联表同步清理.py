import sqlite3
import shutil
from datetime import datetime
import os
import sys
from pathlib import Path

# 添加项目根目录到路径
current_file = Path(__file__).resolve()
project_root = current_file.parent.parent
if str(project_root) not in sys.path:
    sys.path.insert(0, str(project_root))

# TradeFusion智能日志系统 - 稍后从全局角度重新设计

def backup_database():
    """备份数据库"""
    # 导入配置管理
    try:
        from 公共模块.配置管理 import get_config
        config = get_config()
        db_path = str(config.get_db_path())
        backup_dir = config.get_db_path().parent
    except ImportError as e:
        db_path = str(project_root / "数据库0_实体模块/股票数据.db")
        backup_dir = project_root / "数据库0_实体模块"

    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    backup_path = backup_dir / f'股票数据_备份_{timestamp}.db'
    
    try:
        shutil.copy2(db_path, str(backup_path))
        return True, backup_path
    except Exception as e:
        return False, str(e)

def get_valid_sectors_from_zhangting_table():
    """从板块涨停表中获取所有有效的板块名称"""
    db_path = r'E:\YMJATTUU\数据库\股票数据.db'
    conn = sqlite3.connect(db_path)
    cursor = conn.cursor()
    
    try:
        # 获取板块涨停表中所有的板块名称（去重）
        cursor.execute("SELECT DISTINCT 板块名称 FROM 板块涨停表")
        valid_sectors = set([row[0] for row in cursor.fetchall()])
        return valid_sectors
    except Exception as e:
        return set()
    finally:
        conn.close()

def sync_clean_stock_sector_relation():
    """同步清理个股板块关联表"""
    try:
        from 公共模块.配置管理 import get_config
        config = get_config()
        db_path = str(config.get_db_path())
    except ImportError:
        db_path = str(project_root / "数据库0_实体模块/股票数据.db")
    
    # 1. 先备份数据库
    backup_success, backup_info = backup_database()
    if not backup_success:
        return False, f"备份失败: {backup_info}", 0
    
    # 2. 获取板块涨停表中的有效板块
    valid_sectors = get_valid_sectors_from_zhangting_table()
    if not valid_sectors:
        return False, "无法获取有效板块列表", 0
    
    conn = sqlite3.connect(db_path)
    cursor = conn.cursor()
    
    try:
        # 开始事务
        conn.execute("BEGIN TRANSACTION")
        
        # 3. 统计个股板块关联表中的总记录数
        cursor.execute("SELECT COUNT(*) FROM 个股板块关联表")
        total_records_before = cursor.fetchone()[0]
        
        # 4. 统计需要删除的记录数
        cursor.execute("""
            SELECT COUNT(*) FROM 个股板块关联表 
            WHERE 所属板块名称 NOT IN ({})
        """.format(','.join(['?' for _ in valid_sectors])), list(valid_sectors))
        records_to_delete = cursor.fetchone()[0]
        
        if records_to_delete == 0:
            conn.rollback()
            return True, "数据已同步，无需清理", 0
        
        
        # 5. 获取将要删除的板块名称（用于日志记录）
        cursor.execute("""
            SELECT DISTINCT 所属板块名称 FROM 个股板块关联表 
            WHERE 所属板块名称 NOT IN ({})
        """.format(','.join(['?' for _ in valid_sectors])), list(valid_sectors))
        sectors_to_delete = [row[0] for row in cursor.fetchall()]
        
        # 6. 执行删除操作
        cursor.execute("""
            DELETE FROM 个股板块关联表 
            WHERE 所属板块名称 NOT IN ({})
        """.format(','.join(['?' for _ in valid_sectors])), list(valid_sectors))
        
        deleted_count = cursor.rowcount
        
        # 7. 统计清理后的记录数
        cursor.execute("SELECT COUNT(*) FROM 个股板块关联表")
        total_records_after = cursor.fetchone()[0]
        
        # 提交事务
        conn.commit()
        
        
        return True, f"成功删除 {deleted_count} 条记录", deleted_count
        
    except Exception as e:
        conn.rollback()
        return False, str(e), 0
    finally:
        conn.close()

def get_cleanup_statistics():
    """获取清理统计信息"""
    try:
        from 公共模块.配置管理 import get_config
        config = get_config()
        db_path = str(config.get_db_path())
    except ImportError:
        db_path = str(project_root / "数据库0_实体模块/股票数据.db")
    conn = sqlite3.connect(db_path)
    cursor = conn.cursor()
    
    try:
        # 统计个股板块关联表中的板块分布
        cursor.execute("""
            SELECT 所属板块名称, COUNT(*) as 记录数
            FROM 个股板块关联表 
            GROUP BY 所属板块名称 
            ORDER BY 记录数 DESC
        """)
        sector_stats = cursor.fetchall()
        
        # 统计板块涨停表中的板块
        cursor.execute("SELECT COUNT(DISTINCT 板块名称) FROM 板块涨停表")
        zhangting_sector_count = cursor.fetchone()[0]
        
        
        if len(sector_stats) <= 10:  # 如果板块数不多，显示详细信息
            for sector, count in sector_stats:
        
        return sector_stats, zhangting_sector_count
        
    except Exception as e:
        return [], 0
    finally:
        conn.close()

if __name__ == "__main__":
    
    # 显示清理前的统计信息
    get_cleanup_statistics()
    
    # 执行同步清理
    success, message, deleted_count = sync_clean_stock_sector_relation()
    
    if success:
        # 显示清理后的统计信息
        get_cleanup_statistics()
    else:
        pass
