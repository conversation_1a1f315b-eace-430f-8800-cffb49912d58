# TradeFusion 数据流思维导图样式配置说明

## 样式管理结构

本文档说明了 `数据流结构.mmd` 文件中的样式管理方案，实现了数据流实线和调用关系虚线的分离管理。

## 1. 样式分类

### 1.1 数据流样式（实线连接 `-->`）

| 样式类型 | 颜色代码 | 线宽 | 用途 | 示例 |
|---------|---------|------|------|------|
| `dataFlowStyle` | `#2e7d32` | 3px | 标准数据流 | 采集层→存储层 |
| `dataFlowHeavy` | `#1b5e20` | 4px | 重要数据流 | 处理层、核心计算 |
| `dataFlowLight` | `#4caf50` | 2px | 轻量数据流 | 输出层数据流 |

### 1.2 调用关系样式（虚线连接 `-.->` ）

| 样式类型 | 颜色代码 | 线宽 | 虚线样式 | 用途 |
|---------|---------|------|----------|------|
| `callFlowStyle` | `#d84315` | 2px | `5 5` | 标准调用关系 |
| `callFlowTrigger` | `#ff5722` | 3px | `8 4` | 自动触发关系 |
| `callFlowSchedule` | `#9c27b0` | 1px | `3 3` | 调度器调用 |

## 2. 颜色方案

### 2.1 数据流颜色（绿色系）
- **深绿色** `#1b5e20`：重要数据流，表示核心业务数据
- **标准绿色** `#2e7d32`：常规数据流，表示正常数据传输
- **浅绿色** `#4caf50`：轻量数据流，表示输出或辅助数据

### 2.2 调用关系颜色（橙红色系）
- **红色** `#ff5722`：自动触发关系，表示模块间的自动调用
- **橙红色** `#d84315`：标准调用关系，表示常规模块调用
- **紫色** `#9c27b0`：调度器调用，表示系统调度的调用

## 3. 样式修改指南

### 3.1 修改数据流颜色
在 `.mmd` 文件中找到对应的 `linkStyle` 行，修改 `stroke` 参数：

```mermaid
%% 修改标准数据流颜色为蓝色
linkStyle 0 stroke:#1976d2,stroke-width:3px
```

### 3.2 修改调用关系样式
修改虚线样式的 `stroke-dasharray` 参数：

```mermaid
%% 修改虚线样式为更密集的点线
linkStyle 32 stroke:#ff5722,stroke-width:3px,stroke-dasharray:2 2
```

### 3.3 修改线宽
调整 `stroke-width` 参数：

```mermaid
%% 增加重要数据流的线宽
linkStyle 4 stroke:#1b5e20,stroke-width:5px
```

## 4. 连接线索引对照表

### 4.1 数据流连接（linkStyle 0-31）

| 索引范围 | 连接类型 | 样式 |
|---------|---------|------|
| 0-3 | 采集层→存储层 | 标准数据流 |
| 4-8 | 处理层数据流 | 重要数据流 |
| 9-20 | 业务逻辑层数据流 | 标准数据流 |
| 21-24 | 核心计算数据流 | 重要数据流 |
| 25-31 | 输出层数据流 | 轻量数据流 |

### 4.2 调用关系连接（linkStyle 32-44）

| 索引范围 | 连接类型 | 样式 |
|---------|---------|------|
| 32-35 | 采集层自动触发 | 触发样式 |
| 36-39 | 处理层自动触发 | 触发样式 |
| 40-44 | 输出层调用关系 | 标准调用样式 |

## 5. 快速配置模板

### 5.1 蓝色主题数据流
```mermaid
%% 蓝色系数据流
linkStyle X stroke:#1976d2,stroke-width:3px  %% 标准
linkStyle X stroke:#0d47a1,stroke-width:4px  %% 重要
linkStyle X stroke:#42a5f5,stroke-width:2px  %% 轻量
```

### 5.2 紫色主题调用关系
```mermaid
%% 紫色系调用关系
linkStyle X stroke:#7b1fa2,stroke-width:2px,stroke-dasharray:5 5  %% 标准
linkStyle X stroke:#9c27b0,stroke-width:3px,stroke-dasharray:8 4  %% 触发
linkStyle X stroke:#4a148c,stroke-width:1px,stroke-dasharray:3 3  %% 调度
```

## 6. 注意事项

1. **索引顺序**：linkStyle 的索引必须按照连接线在图中出现的顺序编号
2. **颜色一致性**：建议同类型连接使用相近的颜色，保持视觉一致性
3. **线宽层次**：重要程度高的连接使用更粗的线宽
4. **虚线区分**：不同类型的调用关系使用不同的虚线样式进行区分

---
*创建时间：2025-07-23*  
*版本：v1.0*  
*用途：TradeFusion数据流思维导图样式管理*
