/**
 * 工具模块单元测试
 * 
 * 测试范围：DOMUtils、EventUtils、PerformanceUtils
 */

describe('DOMUtils', () => {
    let testElement;

    beforeEach(() => {
        try {
            testElement = document.createElement('div');
            testElement.id = 'test-element';
            if (document.body) {
                document.body.appendChild(testElement);
            }
        } catch (error) {
            console.warn('DOM元素创建失败，跳过DOM测试');
            testElement = null;
        }
    });

    afterEach(() => {
        try {
            if (testElement && testElement.parentNode) {
                testElement.parentNode.removeChild(testElement);
            }
        } catch (error) {
            // 忽略清理错误
        }
        testElement = null;
    });

    describe('元素创建', () => {
        it('应该创建HTML元素', () => {
            const element = DOMUtils.createElement('div', {
                className: 'test-class',
                id: 'test-id'
            }, 'Test Content');

            expect(element.tagName).toBe('DIV');
            expect(element.className).toBe('test-class');
            expect(element.id).toBe('test-id');
            expect(element.textContent).toBe('Test Content');
        });

        it('应该创建SVG元素', () => {
            const element = DOMUtils.createSVGElement('circle', {
                cx: '50',
                cy: '50',
                r: '25'
            });

            expect(element.tagName).toBe('circle');
            expect(element.getAttribute('cx')).toBe('50');
            expect(element.getAttribute('cy')).toBe('50');
            expect(element.getAttribute('r')).toBe('25');
        });

        it('应该处理dataset属性', () => {
            const element = DOMUtils.createElement('div', {
                dataset: {
                    testKey: 'testValue',
                    anotherKey: 'anotherValue'
                }
            });

            expect(element.dataset.testKey).toBe('testValue');
            expect(element.dataset.anotherKey).toBe('anotherValue');
        });
    });

    describe('位置计算', () => {
        it('应该获取元素位置', () => {
            if (!testElement) {
                console.log('跳过DOM测试：元素不可用');
                return;
            }

            testElement.style.position = 'absolute';
            testElement.style.left = '100px';
            testElement.style.top = '50px';
            testElement.style.width = '200px';
            testElement.style.height = '100px';

            const position = DOMUtils.getElementPosition(testElement);

            expect(typeof position.x).toBe('number');
            expect(typeof position.y).toBe('number');
            expect(typeof position.width).toBe('number');
            expect(typeof position.height).toBe('number');
            expect(typeof position.centerX).toBe('number');
            expect(typeof position.centerY).toBe('number');
        });

        it('应该获取连接点位置', () => {
            testElement.style.position = 'absolute';
            testElement.style.left = '100px';
            testElement.style.top = '50px';
            testElement.style.width = '200px';
            testElement.style.height = '100px';

            const rightPoint = DOMUtils.getConnectionPoint(testElement, 'right');
            const leftPoint = DOMUtils.getConnectionPoint(testElement, 'left');
            const topPoint = DOMUtils.getConnectionPoint(testElement, 'top');
            const bottomPoint = DOMUtils.getConnectionPoint(testElement, 'bottom');

            expect(typeof rightPoint.x).toBe('number');
            expect(typeof rightPoint.y).toBe('number');
            expect(typeof leftPoint.x).toBe('number');
            expect(typeof leftPoint.y).toBe('number');
            expect(typeof topPoint.x).toBe('number');
            expect(typeof topPoint.y).toBe('number');
            expect(typeof bottomPoint.x).toBe('number');
            expect(typeof bottomPoint.y).toBe('number');
        });
    });

    describe('CSS类操作', () => {
        it('应该添加CSS类', () => {
            DOMUtils.addClass(testElement, 'new-class');
            expect(testElement.classList.contains('new-class')).toBe(true);
        });

        it('应该移除CSS类', () => {
            testElement.classList.add('remove-me');
            DOMUtils.removeClass(testElement, 'remove-me');
            expect(testElement.classList.contains('remove-me')).toBe(false);
        });

        it('应该切换CSS类', () => {
            const result1 = DOMUtils.toggleClass(testElement, 'toggle-class');
            expect(testElement.classList.contains('toggle-class')).toBe(true);
            expect(result1).toBe(true);

            const result2 = DOMUtils.toggleClass(testElement, 'toggle-class');
            expect(testElement.classList.contains('toggle-class')).toBe(false);
            expect(result2).toBe(false);
        });

        it('应该检查CSS类', () => {
            testElement.classList.add('check-class');
            expect(DOMUtils.hasClass(testElement, 'check-class')).toBe(true);
            expect(DOMUtils.hasClass(testElement, 'not-exist')).toBe(false);
        });
    });

    describe('样式操作', () => {
        it('应该设置样式', () => {
            DOMUtils.setStyle(testElement, {
                color: 'red',
                fontSize: '16px',
                display: 'block'
            });

            expect(testElement.style.color).toBe('red');
            expect(testElement.style.fontSize).toBe('16px');
            expect(testElement.style.display).toBe('block');
        });

        it('应该获取样式', () => {
            testElement.style.color = 'blue';
            const color = DOMUtils.getStyle(testElement, 'color');
            expect(color).toBeTruthy();
        });
    });

    describe('显示/隐藏', () => {
        it('应该显示元素', () => {
            testElement.style.display = 'none';
            DOMUtils.show(testElement);
            expect(testElement.style.display).toBe('block');
        });

        it('应该隐藏元素', () => {
            DOMUtils.hide(testElement);
            expect(testElement.style.display).toBe('none');
        });

        it('应该切换显示状态', () => {
            const result1 = DOMUtils.toggle(testElement);
            expect(testElement.style.display).toBe('none');
            expect(result1).toBe(true);

            const result2 = DOMUtils.toggle(testElement);
            expect(testElement.style.display).toBe('block');
            expect(result2).toBe(false);
        });

        it('应该检查可见性', () => {
            expect(DOMUtils.isVisible(testElement)).toBe(true);
            
            DOMUtils.hide(testElement);
            expect(DOMUtils.isVisible(testElement)).toBe(false);
        });
    });
});

describe('EventUtils', () => {
    let testElement;

    beforeEach(() => {
        testElement = document.createElement('div');
        document.body.appendChild(testElement);
    });

    afterEach(() => {
        if (testElement && testElement.parentNode) {
            testElement.parentNode.removeChild(testElement);
        }
    });

    describe('事件监听', () => {
        it('应该添加事件监听器', () => {
            let clicked = false;
            const cleanup = EventUtils.addEventListener(testElement, 'click', () => {
                clicked = true;
            });

            testElement.click();
            expect(clicked).toBe(true);
            expect(typeof cleanup).toBe('function');
        });

        it('应该支持一次性事件', () => {
            let clickCount = 0;
            EventUtils.once(testElement, 'click', () => {
                clickCount++;
            });

            testElement.click();
            testElement.click();
            expect(clickCount).toBe(1);
        });
    });

    describe('防抖和节流', () => {
        it('应该创建防抖函数', (done) => {
            let callCount = 0;
            const debouncedFn = EventUtils.debounce(() => {
                callCount++;
            }, 50);

            debouncedFn();
            debouncedFn();
            debouncedFn();

            setTimeout(() => {
                expect(callCount).toBe(1);
                done();
            }, 100);
        });

        it('应该创建节流函数', (done) => {
            let callCount = 0;
            const throttledFn = EventUtils.throttle(() => {
                callCount++;
            }, 50);

            throttledFn();
            throttledFn();
            throttledFn();

            setTimeout(() => {
                expect(callCount).toBe(1);
                done();
            }, 100);
        });
    });

    describe('自定义事件', () => {
        it('应该创建自定义事件', () => {
            const event = EventUtils.createEvent('custom:test', { data: 'test' });
            expect(event.type).toBe('custom:test');
            expect(event.detail.data).toBe('test');
        });

        it('应该触发自定义事件', () => {
            let eventTriggered = false;
            let eventDetail = null;

            testElement.addEventListener('custom:test', (e) => {
                eventTriggered = true;
                eventDetail = e.detail;
            });

            EventUtils.trigger(testElement, 'custom:test', { message: 'hello' });
            
            expect(eventTriggered).toBe(true);
            expect(eventDetail.message).toBe('hello');
        });
    });
});

describe('PerformanceUtils', () => {
    describe('计时器', () => {
        it('应该启动和结束计时器', () => {
            const startTime = PerformanceUtils.startTimer('test');
            expect(typeof startTime).toBe('number');

            const duration = PerformanceUtils.endTimer('test', startTime);
            expect(typeof duration).toBe('number');
            expect(duration).toBeGreaterThanOrEqual(0);
        });
    });

    describe('防抖节流', () => {
        it('应该创建防抖函数', (done) => {
            let callCount = 0;
            const debouncedFn = PerformanceUtils.debounce(() => {
                callCount++;
            }, 50);

            debouncedFn();
            debouncedFn();
            debouncedFn();

            setTimeout(() => {
                expect(callCount).toBe(1);
                done();
            }, 100);
        });

        it('应该创建节流函数', (done) => {
            let callCount = 0;
            const throttledFn = PerformanceUtils.throttle(() => {
                callCount++;
            }, 50);

            throttledFn();
            throttledFn();
            throttledFn();

            setTimeout(() => {
                expect(callCount).toBe(1);
                done();
            }, 100);
        });
    });

    describe('内存监控', () => {
        it('应该获取内存使用情况', () => {
            const memory = PerformanceUtils.getMemoryUsage();
            if (memory) {
                expect(typeof memory.used).toBe('number');
                expect(typeof memory.total).toBe('number');
                expect(typeof memory.limit).toBe('number');
            }
        });
    });

    describe('批处理', () => {
        it('应该批量处理RAF回调', (done) => {
            let callCount = 0;
            
            PerformanceUtils.batchRAF(() => callCount++);
            PerformanceUtils.batchRAF(() => callCount++);
            PerformanceUtils.batchRAF(() => callCount++);

            requestAnimationFrame(() => {
                expect(callCount).toBe(3);
                done();
            });
        });
    });
});
