# TradeFusion数据流依赖关系分析

## 概述

本文档基于MCP数据库工具分析，详细描述了TradeFusion系统中数据库表与处理模块之间的依赖关系，按数据流的时间顺序进行排序。

## 数据流架构层级

### 第1层：数据采集层 (Data Collection Layer)
**功能**：从外部数据源采集原始数据，写入临时表

#### A1. 本地数据采集
- **模块**：`数据1_本地采集/采集_本地数据.py`
- **输出表**：`个股连板高度表`
- **临时表**：`临时表_本地数据` (未使用)
- **数据源**：本地DAT文件
- **运行频率**：3秒间隔
- **状态**：✅ 正常工作

#### A2. 东财人气采集
- **模块**：`数据2_网络采集/人气_东财采集.py`
- **输出表**：`临时表_东财人气`
- **数据源**：东方财富网站
- **运行频率**：10分钟间隔
- **状态**：✅ 正常工作 (100条记录)

#### A3. 同花人气采集
- **模块**：`数据2_网络采集/人气_同花采集.py`
- **输出表**：`临时表_同花人气`
- **数据源**：同花顺网站
- **运行频率**：5分钟间隔
- **状态**：✅ 正常工作 (34条记录)

#### A4. 选股宝数据采集
- **模块**：`数据3_网络采集_bk/选股宝抓取.py`
- **输出表**：`临时表_选股宝原始`
- **数据源**：选股宝网站
- **运行频率**：定时采集
- **状态**：✅ 正常工作 (1条记录)

### 第2层：数据处理层 (Data Processing Layer)
**功能**：处理原始数据，进行清洗、格式化和基础统计

#### B1. 选股宝数据清洗
- **模块**：`数据3_网络采集_bk/选股宝清洗.py`
- **输入表**：`临时表_选股宝原始`
- **输出表**：`临时表_选股宝清洗`
- **处理内容**：解析股票表格，提取板块信息和个股解读
- **状态**：🔄 自动触发

#### B2. 板块涨停统计
- **模块**：`数据库2_板块层统计/板块涨停表.py`
- **输入表**：`个股连板高度表`
- **输出表**：`板块涨停表`
- **处理内容**：统计各板块涨停股票数量，计算板块评分，保留前7名
- **状态**：🔄 自动触发

### 第3层：数据融合层 (Data Integration Layer)
**功能**：将临时表数据融合到正式业务表

#### C1. 个股人气数据融合
- **模块**：`数据2_网络采集/个股人气表.py`
- **输入表**：`临时表_东财人气`, `临时表_同花人气`
- **输出表**：`个股人气表`
- **处理逻辑**：融合东财和同花人气排名，计算综合评分
- **状态**：❌ 需要手动触发

#### C2. 个股解读_板块信息_关联表
- **模块**：`数据3_网络采集_bk/个股解读_板块信息_关联表.py`
- **输入表**：`临时表_选股宝清洗`
- **输出表**：`个股解读表`, `板块信息表`, `个股板块关联表`
- **处理逻辑**：分离个股解读、板块信息和关联关系
- **状态**：❌ 需要手动触发

### 第4层：统计分析层 (Statistical Analysis Layer)
**功能**：基于处理后的数据进行高级统计分析和评分计算

#### D1. 所属板块评分
- **模块**：`数据库3_个股所属板块层统计/A3_所属板块评分表.py`
- **输入表**：`板块涨停表`
- **输出表**：`所属板块评分表`
- **处理逻辑**：为个股计算所属板块的综合评分
- **状态**：❌ 需要手动触发

### 第5层：精选决策层 (Selection Decision Layer)
**功能**：基于统计分析结果进行精选和决策

#### E1. 板块精选
- **模块**：`数据库2_板块层统计/板块精选.py`
- **输入表**：`板块涨停表`
- **输出表**：`板块精选表`
- **处理逻辑**：使用SMA算法精选优质板块
- **状态**：❌ 需要手动触发

#### E2. 个股接力计算
- **模块**：`数据库3_个股所属板块层统计/个股接力表.py`
- **输入表**：`板块精选表`
- **输出表**：`个股接力表`
- **处理逻辑**：基于精选板块计算个股接力值
- **状态**：❌ 需要手动触发

### 第6层：输出应用层 (Output Application Layer)
**功能**：生成最终的应用数据和文件

#### F1. 综合人气190
- **模块**：`数据库写大智慧/综合人气190.py`
- **输入表**：`个股人气表`
- **输出**：大智慧格式文件
- **状态**：❌ 需要手动触发

#### F2. 生成DZH3人气板块
- **模块**：`数据库写大智慧/生成DZH3人气板块.py`
- **输入表**：`个股人气表`
- **输出**：DZH3格式文件
- **状态**：❌ 需要手动触发

#### F3. 接力数据输出
- **模块**：`数据库写大智慧/接力.py`
- **输入表**：`个股接力表`
- **输出**：接力数据文件
- **状态**：❌ 需要手动触发

## 关键依赖关系

### 核心数据流路径

1. **人气数据流**：
   ```
   东财采集 → 临时表_东财人气 ↘
                                  → 个股人气表 → 综合人气190/DZH3输出
   同花采集 → 临时表_同花人气 ↗
   ```

2. **选股宝数据流**：
   ```
   选股宝抓取 → 临时表_选股宝原始 → 选股宝清洗 → 临时表_选股宝清洗 
   → 个股解读_板块信息_关联表 → {个股解读表, 板块信息表, 个股板块关联表}
   ```

3. **板块分析流**：
   ```
   个股连板高度表 → 板块涨停表 → 板块精选表 → 个股接力表 → 接力数据输出
                              ↘
                                所属板块评分表
   ```

### 表间依赖矩阵

| 输出表 | 依赖输入表 | 处理模块 |
|--------|------------|----------|
| 个股连板高度表 | 本地DAT文件 | 采集_本地数据 |
| 临时表_东财人气 | 东财网站 | 人气_东财采集 |
| 临时表_同花人气 | 同花网站 | 人气_同花采集 |
| 临时表_选股宝原始 | 选股宝网站 | 选股宝抓取 |
| 临时表_选股宝清洗 | 临时表_选股宝原始 | 选股宝清洗 |
| 个股人气表 | 临时表_东财人气, 临时表_同花人气 | 个股人气表 |
| 个股解读表 | 临时表_选股宝清洗 | 个股解读_板块信息_关联表 |
| 板块信息表 | 临时表_选股宝清洗 | 个股解读_板块信息_关联表 |
| 个股板块关联表 | 临时表_选股宝清洗 | 个股解读_板块信息_关联表 |
| 板块涨停表 | 个股连板高度表 | 板块涨停表 |
| 所属板块评分表 | 板块涨停表 | A3_所属板块评分表 |
| 板块精选表 | 板块涨停表 | 板块精选 |
| 个股接力表 | 板块精选表 | 个股接力表 |

## 当前系统状态

### ✅ 正常工作的模块 (第1层)
- 本地数据采集：个股连板高度表有108条今日数据
- 东财人气采集：临时表_东财人气有100条记录
- 同花人气采集：临时表_同花人气有34条记录
- 选股宝抓取：临时表_选股宝原始有1条记录

### ❌ 需要手动触发的模块 (第2-6层)
- 所有数据处理、分析和输出模块都需要手动执行
- 数据流在第1层采集完成后完全停滞

## 关键表结构说明

### 临时表结构
- **临时表_东财人气**：股票代码、股票名称、人气排名、采集时间、处理状态
- **临时表_同花人气**：股票代码、股票名称、人气排名、采集时间、处理状态
- **临时表_选股宝原始**：原始抓取数据
- **临时表_选股宝清洗**：日期、股票代码、板块名称、板块涨幅、板块消息、个股解读

### 核心业务表结构
- **个股人气表**：日期、股票代码、东财人气排名、同花人气排名、综合人气评分、股票名称
- **板块涨停表**：日期、板块名称、板块评分
- **板块精选表**：日期、板块名称、综合评分
- **个股接力表**：基于精选板块的个股接力计算结果

## 数据流断点分析

### 当前断点位置
1. **临时表_选股宝清洗为空** - 选股宝清洗模块未自动执行
2. **个股人气表无今日数据** - 人气融合模块未自动执行
3. **所有统计分析表无今日数据** - 后续处理链完全停滞

### 修复策略
删除触发器系统后，需要建立手动执行流程或简单的调度机制来维持数据流的连续性。

## 建议的手动执行顺序

1. **第2层**：`python 数据3_网络采集_bk/选股宝清洗.py` (板块涨停表已自动触发)
2. **第3层**：
   - `python 数据2_网络采集/个股人气表.py`
   - `python 数据3_网络采集_bk/个股解读_板块信息_关联表.py`
3. **第4层**：
   - `python 数据库3_个股所属板块层统计/A3_所属板块评分表.py`
4. **第5层**：
   - `python 数据库2_板块层统计/板块精选.py`
   - `python 数据库3_个股所属板块层统计/个股接力表.py`
5. **第6层**：根据需要执行输出模块

## 性能与优化建议

### 数据采集层优化
- 东财和同花采集频率不同（10分钟 vs 5分钟），需要考虑数据同步问题
- 选股宝数据量较小但处理复杂，建议优化解析算法

### 数据处理层优化
- 临时表数据应及时清理，避免累积过多历史数据
- 考虑实现增量更新机制，避免全量重新计算

### 依赖关系优化
- 板块涨停表是多个下游表的关键依赖，应优先保证其数据质量
- 个股接力表依赖板块精选表，如果精选结果为空会导致接力计算失败

---
*文档生成时间：2025-07-22*
*基于MCP数据库工具分析结果*
*数据表总数：15个，临时表：5个，业务表：10个*
