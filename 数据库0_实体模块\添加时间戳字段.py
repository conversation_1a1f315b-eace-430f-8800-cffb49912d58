#!/usr/bin/env python3
"""
TradeFusion时间戳字段添加脚本
为关键表添加更新时间戳字段，支持智能更新检测
"""

import sqlite3
import sys
from pathlib import Path

# 添加项目根目录到路径
current_file = Path(__file__).resolve()
project_root = current_file.parent.parent
if str(project_root) not in sys.path:
    sys.path.insert(0, str(project_root))

def 添加时间戳字段():
    """为关键表添加时间戳字段"""
    
    # 获取数据库路径
    try:
        from 公共模块.配置管理 import get_config
        config = get_config()
        db_path = str(config.get_db_path())
    except ImportError:
        db_path = str(project_root / "数据库0_实体模块/股票数据.db")
    
    print(f"🔴[时间戳字段] 🟡开始添加时间戳字段")
    print(f"数据库路径: {db_path}")
    
    conn = None
    try:
        conn = sqlite3.connect(db_path, timeout=30)
        cursor = conn.cursor()
        
        # 需要添加时间戳字段的表
        tables = [
            '个股人气表',
            '个股板块关联表', 
            '板块涨停表',
            '板块精选表',
            '个股接力表'
        ]
        
        added_count = 0
        for table in tables:
            try:
                # SQLite不支持动态默认值，使用NULL默认值
                cursor.execute(f'ALTER TABLE {table} ADD COLUMN 更新时间戳 TEXT DEFAULT NULL')
                print(f"✅ {table} 时间戳字段添加成功")
                added_count += 1
                
            except sqlite3.Error as e:
                if "duplicate column name" in str(e):
                    print(f"ℹ️  {table} 时间戳字段已存在")
                else:
                    print(f"❌ {table} 时间戳字段添加失败: {str(e)}")
        
        conn.commit()
        print(f"\n🔴[时间戳字段] 🟢完成 - 添加了 {added_count} 个时间戳字段")
        
        # 验证字段添加
        for table in tables:
            try:
                cursor.execute(f"PRAGMA table_info({table})")
                columns = [row[1] for row in cursor.fetchall()]
                if '更新时间戳' in columns:
                    print(f"✅ {table} 时间戳字段验证成功")
                else:
                    print(f"❌ {table} 时间戳字段验证失败")
            except Exception as e:
                print(f"❌ {table} 验证失败: {str(e)}")
        
        return True
        
    except Exception as e:
        print(f"🔴[时间戳字段] 🟡执行失败: {str(e)}")
        return False
    finally:
        if conn:
            conn.close()

if __name__ == "__main__":
    print("🔧 TradeFusion时间戳字段添加工具")
    print("=" * 50)
    
    if 添加时间戳字段():
        print("\n🎉 时间戳字段添加完成！")
    else:
        print("\n❌ 时间戳字段添加失败！")
        sys.exit(1)
