#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
TradeFusion数据库结构报告生成器

功能：
- 定期扫描SQLite数据库，生成完整的表结构报告
- 智能避开交易时间段（9:00-9:25）运行
- 支持配置管理和日志记录
- 生成格式化的数据库说明文档

作者：TradeFusion团队
版本：2.0（优化版）
"""
import sys
import sqlite3
import time
import datetime
import os
from pathlib import Path
from logging.handlers import TimedRotatingFileHandler
import logging

# ========== 日志配置（符合设计方案） ==========
def setup_logger():
    """设置符合TradeFusion设计方案的日志系统"""
    logger = logging.getLogger('数据库结构报告生成器')
    logger.setLevel(logging.INFO)

    # 清除现有handlers避免重复
    logger.handlers.clear()

    # 确保logs目录存在
    os.makedirs('logs', exist_ok=True)

    # 控制台输出
    console_handler = logging.StreamHandler()
    console_formatter = logging.Formatter('[%(asctime)s] %(message)s', datefmt='%H:%M:%S')
    console_handler.setFormatter(console_formatter)
    logger.addHandler(console_handler)

    # 文件存储
    file_handler = TimedRotatingFileHandler(
        filename=os.path.join('logs', '数据库结构报告生成器.log'),
        when='midnight',
        interval=1,
        backupCount=30,  # 基础设施模块保留30天
        encoding='utf-8'
    )
    file_handler.setFormatter(console_formatter)
    logger.addHandler(file_handler)

    logger.propagate = False
    return logger

# 初始化日志器
logger = setup_logger()

# ---------- 全局配置常量 ----------
# 路径配置 (相对于项目根目录)
DB_FILE_PATH_SUFFIX = "数据库0_实体模块/股票数据.db"
REPORT_FILE_PATH_SUFFIX = "数据库0_实体模块/数据库说明.md"

# 循环控制配置
LOOP_SLEEP_SECONDS = 600  # 主循环休眠时间（10分钟）

# ---------- 项目路径初始化和配置管理 ----------
def initialize_project():
    """初始化项目路径和配置"""
    try:
        if getattr(sys, 'frozen', False):
            project_root = Path(sys.executable).parent
        else:
            project_root = Path(__file__).absolute().parent.parent

        if str(project_root) not in sys.path:
            sys.path.append(str(project_root))

        # 尝试导入配置管理
        try:
            from 公共模块.配置管理 import get_config
            config = get_config()
            db_path = config.get_db_path()
            # 移除调试日志
            return project_root, db_path
        except ImportError:
            # 使用默认路径
            db_path = project_root / DB_FILE_PATH_SUFFIX
            # 移除调试日志
            return project_root, db_path

    except Exception as e:
        logger.error(f"✗ <数据库结构报告生成器> 项目初始化失败: {str(e)}")
        sys.exit(1)

# 初始化项目
PROJECT_ROOT, DB_PATH = initialize_project()





def get_db_schema(db_path: Path) -> tuple[str, dict]:
    """
    获取数据库的完整结构信息

    Args:
        db_path: 数据库文件的路径

    Returns:
        tuple: (格式化报告字符串, 统计信息字典)

    Raises:
        sqlite3.Error: 当发生数据库相关错误时
        ValueError: 当发现无效的表名时
    """
    # 移除开始日志

    # 报告头部信息（Markdown格式）
    schema_parts = [
        "# TradeFusion数据库结构报告",
        "",
        "## 基本信息",
        "",
        f"- **数据库路径**: `{db_path.resolve()}`",
        f"- **SQLite版本**: {sqlite3.sqlite_version}",
        f"- **扫描时间**: {datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')}",
        "",
    ]

    # 统计信息
    stats = {
        'total_tables': 0,
        'total_columns': 0,
        'total_indexes': 0,
        'tables_with_pk': 0,
        'tables_with_indexes': 0
    }

    try:
        with sqlite3.connect(db_path) as conn:
            cursor = conn.cursor()

            # 获取所有表名，排除系统表
            cursor.execute("""
                SELECT name FROM sqlite_master
                WHERE type='table' AND name NOT LIKE 'sqlite_%'
                ORDER BY name
            """)
            tables = [table[0] for table in cursor.fetchall()]
            stats['total_tables'] = len(tables)

            logger.debug(f"<数据库结构报告生成器> 发现 {len(tables)} 张表")

            # 先收集所有表的基本信息用于汇总
            table_summaries = []
            field_summaries = []  # 新增：字段汇总信息
            detailed_schemas = []

            for table_name in tables:
                # 获取表结构和主键信息
                cursor.execute(f"PRAGMA table_xinfo('{table_name}')")
                columns_info = cursor.fetchall()
                stats['total_columns'] += len(columns_info)

                pk_fields = [col[1] for col in columns_info if col[5] > 0]
                if pk_fields:
                    stats['tables_with_pk'] += 1
                    pk_desc = "复合主键" if len(pk_fields) > 1 else "单字段主键"
                else:
                    pk_desc = "无主键"

                # 获取索引信息
                cursor.execute(f"""
                    SELECT name, sql FROM sqlite_master
                    WHERE type='index' AND tbl_name='{table_name}'
                    AND name NOT LIKE 'sqlite_autoindex%'
                    ORDER BY name
                """)
                indexes = cursor.fetchall()
                if indexes:
                    stats['tables_with_indexes'] += 1
                    stats['total_indexes'] += len(indexes)

                # 收集表汇总信息
                table_type = "临时表" if table_name.startswith("临时表_") else "正式表"
                table_summaries.append({
                    'name': table_name,
                    'type': table_type,
                    'columns': len(columns_info),
                    'pk_desc': pk_desc,
                    'indexes': len(indexes)
                })

                # 收集字段汇总信息
                for col in columns_info:
                    field_summaries.append({
                        'table_name': table_name,
                        'table_type': table_type,
                        'field_name': col[1],
                        'field_type': col[2],
                        'not_null': '是' if col[3] else '否',
                        'is_pk': '是' if col[5] else '否',
                        'default_value': col[4] if col[4] is not None else ''
                    })

                # 生成详细的表结构信息
                table_detail = [
                    f"### {table_name}",
                    "",
                    f"**主键策略**: {pk_desc}" + (f" ({', '.join(pk_fields)})" if pk_fields else ""),
                    "",
                    "**字段结构**:",
                    "",
                    "| 列名 | 类型 | 非空 | 主键 |",
                    "|------|------|------|------|"
                ]

                # 格式化字段信息
                for col in columns_info:
                    col_name = col[1]
                    col_type = col[2]
                    not_null = '是' if col[3] else '否'
                    is_pk = '是' if col[5] else '否'
                    table_detail.append(f"| {col_name} | {col_type} | {not_null} | {is_pk} |")

                # 格式化索引信息
                table_detail.append("")
                if indexes:
                    table_detail.append("**索引策略**:")
                    table_detail.append("")
                    for idx in indexes:
                        unique_str = '唯一索引' if 'UNIQUE' in (idx[1] or '').upper() else '普通索引'
                        table_detail.append(f"- **{idx[0]}** ({unique_str})")
                        if idx[1]:  # 如果有SQL语句
                            table_detail.append(f"  ```sql")
                            table_detail.append(f"  {idx[1]}")
                            table_detail.append(f"  ```")
                else:
                    table_detail.append("**索引策略**: 无额外索引")

                table_detail.append("")
                table_detail.append("---")
                table_detail.append("")
                detailed_schemas.extend(table_detail)

            # 生成表汇总信息（Markdown格式）
            summary_parts = [
                "## 数据库概览",
                "",
                f"- **总表数**: {stats['total_tables']}张",
                f"- **总字段数**: {stats['total_columns']}个",
                f"- **总索引数**: {stats['total_indexes']}个",
                f"- **有主键的表**: {stats['tables_with_pk']}/{stats['total_tables']}张",
                f"- **有索引的表**: {stats['tables_with_indexes']}/{stats['total_tables']}张",
                f"- **平均每表字段数**: {stats['total_columns']/stats['total_tables']:.1f}个" if stats['total_tables'] > 0 else "- **平均每表字段数**: 0个",
                "",
                "## 表汇总列表",
                "",
                "| 序号 | 表名 | 类型 | 字段数 | 主键策略 | 索引数 |",
                "|------|------|------|--------|----------|--------|"
            ]

            # 添加表汇总列表
            for i, table_info in enumerate(table_summaries, 1):
                name = table_info['name']
                summary_parts.append(
                    f"| {i} | {name} | {table_info['type']} | {table_info['columns']} | {table_info['pk_desc']} | {table_info['indexes']} |"
                )

            # 按类型分组显示
            temp_tables = [t for t in table_summaries if t['type'] == '临时表']
            formal_tables = [t for t in table_summaries if t['type'] == '正式表']

            if temp_tables or formal_tables:
                summary_parts.extend([
                    "",
                    "## 按类型分组",
                    "",
                    f"### 临时表 ({len(temp_tables)}张)",
                    "",
                ])
                if temp_tables:
                    for table in temp_tables:
                        summary_parts.append(f"- {table['name']}")
                else:
                    summary_parts.append("- 无")

                summary_parts.extend([
                    "",
                    f"### 正式表 ({len(formal_tables)}张)",
                    "",
                ])
                if formal_tables:
                    for table in formal_tables:
                        summary_parts.append(f"- {table['name']}")
                else:
                    summary_parts.append("- 无")

            # 添加简洁的字段汇总表（横向显示）
            summary_parts.extend([
                "",
                "## 字段汇总列表",
                "",
                "| 序号 | 表名 | 所有字段列表 |",
                "|------|------|-------------|"
            ])

            # 按表分组显示字段
            for i, table_info in enumerate(table_summaries, 1):
                table_name = table_info['name']
                table_fields = [f for f in field_summaries if f['table_name'] == table_name]

                # 按字段类型分组
                pk_fields = [f['field_name'] for f in table_fields if f['is_pk'] == '是']
                regular_fields = [f['field_name'] for f in table_fields if f['is_pk'] == '否']

                # 构建字段列表字符串
                field_list_parts = []
                if pk_fields:
                    field_list_parts.append(f"🔑主键:[{', '.join(pk_fields)}]")
                if regular_fields:
                    field_list_parts.append(f"📝字段:[{', '.join(regular_fields)}]")

                field_list = " | ".join(field_list_parts)

                summary_parts.append(
                    f"| {i} | {table_name} | {field_list} |"
                )

            # 添加按表分组的字段汇总
            summary_parts.extend([
                "",
                "## 按表分组的字段汇总",
                "",
            ])

            for table_info in table_summaries:
                table_name = table_info['name']
                table_fields = [f for f in field_summaries if f['table_name'] == table_name]

                # 分类字段
                pk_fields = [f['field_name'] for f in table_fields if f['is_pk'] == '是']
                regular_fields = [f['field_name'] for f in table_fields if f['is_pk'] == '否']

                summary_parts.extend([
                    f"### {table_name}",
                    "",
                    f"- **类型**: {table_info['type']}",
                    f"- **字段数**: {len(table_fields)}个",
                    f"- **主键字段**: {', '.join(pk_fields) if pk_fields else '无'}",
                    f"- **普通字段**: {', '.join(regular_fields) if regular_fields else '无'}",
                    "",
                ])

            # 组装完整报告
            schema_parts.extend(summary_parts)
            schema_parts.extend([
                "## 详细表结构",
                "",
            ])
            schema_parts.extend(detailed_schemas)

        return "\n".join(schema_parts), stats

    except sqlite3.Error as e:
        logger.error(f"✗ <数据库结构报告生成器> 数据库访问错误: {str(e)}")
        raise
    except Exception as e:
        logger.error(f"✗ <数据库结构报告生成器> 结构分析失败: {str(e)}")
        raise


def generate_db_report() -> bool:
    """
    生成并保存数据库结构报告的主流程

    Returns:
        bool: True表示成功，False表示失败
    """
    # 使用配置的数据库路径
    if not DB_PATH.exists():
        logger.warning(f"⚠️ <数据库结构报告生成器> 数据库文件不存在: {DB_PATH}")
        return False

    try:
        # 移除开始日志

        # 获取数据库结构
        report_content, stats = get_db_schema(DB_PATH)

        # 保存报告文件
        save_path = PROJECT_ROOT / REPORT_FILE_PATH_SUFFIX
        save_path.parent.mkdir(parents=True, exist_ok=True)

        with open(save_path, "w", encoding="utf-8") as f:
            f.write(report_content)

        # 被调用者后输出（三色日志格式）
        logger.info(f"\033[91m[数据库结构报告生成器]\033[0m 结构报告生成完成 - \033[93m数据库说明.md\033[0m 表:\033[92m{stats['total_tables']}张\033[0m, 字段:\033[92m{stats['total_columns']}个\033[0m, 索引:\033[92m{stats['total_indexes']}个\033[0m")

        return True

    except sqlite3.Error as e:
        logger.error(f"✗ <数据库结构报告生成器> 数据库访问失败: {str(e)}")
        return False
    except ValueError as e:
        logger.error(f"✗ <数据库结构报告生成器> 数据验证失败: {str(e)}")
        return False
    except IOError as e:
        logger.error(f"✗ <数据库结构报告生成器> 文件写入失败: {str(e)}")
        return False
    except Exception as e:
        logger.error(f"✗ <数据库结构报告生成器> 未知错误: {str(e)}")
        return False


def main():
    """
    主程序循环

    功能：
    - 定期生成数据库结构报告
    - 智能避开交易时间段
    - 异常处理和日志记录
    """
    logger.info(f"<数据库结构报告生成器> 服务启动，监控数据库: {DB_PATH}")

    consecutive_failures = 0
    max_failures = 5

    try:
        while True:
            try:
                success = generate_db_report()
                if success:
                    consecutive_failures = 0  # 重置失败计数
                    logger.debug(f"<数据库结构报告生成器> 报告生成成功，下次扫描: {(datetime.datetime.now() + datetime.timedelta(seconds=LOOP_SLEEP_SECONDS)).strftime('%H:%M:%S')}")
                else:
                    consecutive_failures += 1
                    logger.warning(f"⚠️ <数据库结构报告生成器> 报告生成失败，连续失败次数: {consecutive_failures}")

                    if consecutive_failures >= max_failures:
                        logger.error(f"✗ <数据库结构报告生成器> 连续失败 {max_failures} 次，服务暂停")
                        break

            except Exception as e:
                consecutive_failures += 1
                logger.error(f"✗ <数据库结构报告生成器> 未预期错误: {str(e)}")

                if consecutive_failures >= max_failures:
                    logger.error(f"✗ <数据库结构报告生成器> 连续异常 {max_failures} 次，服务停止")
                    break

            time.sleep(LOOP_SLEEP_SECONDS)

    except KeyboardInterrupt:
        logger.info(f"<数据库结构报告生成器> 收到停止信号，服务正常退出")
    except Exception as e:
        logger.error(f"✗ <数据库结构报告生成器> 主循环异常: {str(e)}")
    finally:
        logger.info(f"<数据库结构报告生成器> 服务已停止")


def run_once():
    """单次运行模式，用于测试和手动执行"""
    logger.info(f"<数据库结构报告生成器> 单次运行模式")

    success = generate_db_report()
    if success:
        logger.info(f"<数据库结构报告生成器> 单次运行完成")
    else:
        logger.error(f"✗ <数据库结构报告生成器> 单次运行失败")

    return success


if __name__ == "__main__":
    import argparse

    # 命令行参数解析
    parser = argparse.ArgumentParser(description='TradeFusion数据库结构报告生成器')
    parser.add_argument('--once', action='store_true', help='单次运行模式（不循环）')
    parser.add_argument('--debug', action='store_true', help='启用调试日志')
    args = parser.parse_args()

    # 设置日志级别
    if args.debug:
        logger.setLevel(logging.DEBUG)
        logger.debug(f"<数据库结构报告生成器> 调试模式已启用")

    # 选择运行模式
    if args.once:
        run_once()
    else:
        main()