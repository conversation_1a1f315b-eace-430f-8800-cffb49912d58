/**
 * TradeFusion桌面版主应用
 * 
 * 功能：应用程序主逻辑和简化的事件系统
 * 职责：应用初始化、事件管理、组件协调
 * 
 * <AUTHOR> Team
 * @version 2.0.0 (简化版)
 */

class TradeFusionApp {
    constructor() {
        this.config = null;
        this.controller = null;
        this.renderer = null;
        this.batchGenerator = null;
        this.eventListeners = new Map();
        this.isInitialized = false;
        this.eventManager = EventUtils.createEventManager();
        this.performanceMonitor = null;
        this.errorRecovery = null;
        this.moduleInterface = null;
        this.userExperience = null;

        // 初始化全局错误处理
        this.initGlobalErrorHandling();

        // 初始化高级功能
        this.initAdvancedFeatures();
    }

    /**
     * 简化的事件系统
     */
    on(event, callback) {
        if (!this.eventListeners.has(event)) {
            this.eventListeners.set(event, []);
        }
        this.eventListeners.get(event).push(callback);
    }

    emit(event, data) {
        const listeners = this.eventListeners.get(event) || [];
        listeners.forEach(callback => {
            try {
                callback(data);
            } catch (error) {
                this.handleError(error, `事件处理错误 ${event}`);
            }
        });
    }

    /**
     * 初始化全局错误处理
     */
    initGlobalErrorHandling() {
        // 捕获未处理的JavaScript错误
        window.addEventListener('error', (event) => {
            this.handleError(event.error || new Error(event.message), '全局JavaScript错误', {
                filename: event.filename,
                lineno: event.lineno,
                colno: event.colno
            });
        });

        // 捕获未处理的Promise拒绝
        window.addEventListener('unhandledrejection', (event) => {
            this.handleError(event.reason, '未处理的Promise拒绝');
            event.preventDefault(); // 阻止默认的控制台错误输出
        });

        // 捕获资源加载错误
        window.addEventListener('error', (event) => {
            if (event.target !== window) {
                this.handleError(new Error(`资源加载失败: ${event.target.src || event.target.href}`), '资源加载错误');
            }
        }, true);
    }

    /**
     * 统一错误处理
     */
    handleError(error, context = '未知错误', details = {}) {
        const errorInfo = {
            message: error.message || String(error),
            stack: error.stack,
            context: context,
            details: details,
            timestamp: new Date().toISOString(),
            userAgent: navigator.userAgent,
            url: window.location.href
        };

        // 记录错误到控制台
        console.error(`[TradeFusion] ${context}:`, error, details);

        // 显示用户友好的错误提示
        this.showErrorNotification(context, error.message || String(error));

        // 发送错误事件，供其他组件监听
        this.emit('app:error', errorInfo);

        // 在开发环境下，可以将错误发送到错误监控服务
        if (this.isDevelopment()) {
            this.reportErrorToDevelopment(errorInfo);
        }
    }

    /**
     * 显示错误通知
     */
    showErrorNotification(title, message) {
        this.showNotification(title, message, 'error');
    }

    /**
     * 检查是否为开发环境
     */
    isDevelopment() {
        return window.location.hostname === 'localhost' ||
               window.location.hostname === '127.0.0.1' ||
               window.location.protocol === 'file:';
    }

    /**
     * 开发环境错误报告
     */
    reportErrorToDevelopment(errorInfo) {
        // 在开发环境下，可以将错误信息存储到localStorage
        try {
            const errors = JSON.parse(localStorage.getItem('tradefusion_errors') || '[]');
            errors.push(errorInfo);

            // 只保留最近50个错误
            if (errors.length > 50) {
                errors.splice(0, errors.length - 50);
            }

            localStorage.setItem('tradefusion_errors', JSON.stringify(errors));
        } catch (e) {
            console.warn('[TradeFusion] 无法保存错误信息到localStorage:', e);
        }
    }

    /**
     * 初始化应用程序
     */
    async init() {
        try {
            console.log('[TradeFusion] 应用程序启动中...');
            
            // 显示加载状态
            this.showLoading();

            // 加载配置
            this.loadConfig();

            // 初始化组件
            this.initializeComponents();

            // 绑定事件
            this.bindEvents();

            // 渲染界面
            this.renderUI();

            // 隐藏加载状态
            this.hideLoading();

            this.isInitialized = true;
            console.log('[TradeFusion] 应用程序启动完成');

        } catch (error) {
            console.error('[TradeFusion] 应用程序启动失败:', error);
            this.showError('应用程序启动失败: ' + error.message);
        }
    }

    /**
     * 加载配置
     */
    loadConfig() {
        if (window.TradeFusionConfig) {
            this.config = window.TradeFusionConfig;
            console.log('[TradeFusion] 配置加载成功', this.config);
        } else {
            throw new Error('配置文件未加载');
        }
    }

    /**
     * 初始化组件
     */
    initializeComponents() {
        // 初始化模块控制器
        this.controller = new ModuleController(this, this.config);

        // 初始化模块渲染器
        this.renderer = new ModuleRenderer(this, this.config);

        // 初始化连接线渲染器
        this.connectionRenderer = new ConnectionRenderer(this, this.config);

        // 初始化批处理生成器
        this.batchGenerator = new BatchGenerator(this, this.config);

        // 初始化位置管理器
        this.positionManager = new PositionManager(this, this.config);

        console.log('[TradeFusion] 组件初始化完成');
    }

    /**
     * 绑定事件
     */
    bindEvents() {
        // 全局控制事件
        document.getElementById('start-all-btn').addEventListener('click', () => {
            this.emit('app:start-all-modules');
        });

        document.getElementById('stop-all-btn').addEventListener('click', () => {
            this.emit('app:stop-all-modules');
        });

        document.getElementById('restart-system-btn').addEventListener('click', () => {
            this.emit('app:restart-system');
        });

        // 位置管理事件
        document.getElementById('save-positions-btn').addEventListener('click', () => {
            this.emit('position:save');
        });

        document.getElementById('load-positions-btn').addEventListener('click', () => {
            this.emit('position:load');
        });

        document.getElementById('reset-positions-btn').addEventListener('click', () => {
            if (confirm('确定要重置所有模块位置吗？这将清除已保存的位置信息。')) {
                this.emit('position:reset');
            }
        });

        // 缩放控制事件
        document.getElementById('zoom-in-btn').addEventListener('click', () => {
            this.emit('canvas:zoom-in');
        });

        document.getElementById('zoom-out-btn').addEventListener('click', () => {
            this.emit('canvas:zoom-out');
        });

        document.getElementById('fit-view-btn').addEventListener('click', () => {
            this.emit('canvas:fit-view');
        });

        // 连接线管理事件
        document.getElementById('toggle-edit-mode-btn').addEventListener('click', () => {
            this.emit('connection:toggle-edit-mode');
        });

        document.getElementById('show-connections-btn').addEventListener('click', () => {
            this.toggleConnectionsVisibility();
        });

        // 应用级事件监听
        this.on('module:status-changed', (data) => {
            this.updateStats();
        });

        this.on('ui:show-message', (data) => {
            this.showMessage(data);
        });
    }

    /**
     * 渲染界面
     */
    renderUI() {
        // 更新统计信息
        this.updateStats();

        // 渲染模块节点
        this.renderModules();

        // DOM创建完成后，加载保存的位置
        if (this.positionManager) {
            setTimeout(() => {
                this.positionManager.loadSavedPositions();
            }, 100); // 给DOM一点时间完成渲染
        }

        // 更新时间戳
        document.getElementById('last-update').textContent = new Date().toLocaleTimeString();
    }

    /**
     * 渲染模块节点
     */
    renderModules() {
        const container = document.getElementById('modules-container');
        if (this.renderer) {
            this.renderer.init(container);
        }

        // 初始化连接线渲染器
        if (this.connectionRenderer) {
            this.connectionRenderer.init();
        }
    }

    /**
     * 更新统计信息
     */
    updateStats() {
        if (!this.config) return;

        const totalModules = Object.keys(this.config.modules || {}).length;
        const connections = (this.config.connections || []).length;

        // 获取实时统计
        let runningCount = 0;
        let errorCount = 0;
        
        if (this.controller) {
            const stats = this.controller.getStats();
            runningCount = stats.running;
            errorCount = stats.error;
        }

        document.getElementById('total-modules').textContent = totalModules;
        document.getElementById('running-modules').textContent = runningCount;
        document.getElementById('error-modules').textContent = errorCount;
        document.getElementById('connections-count').textContent = connections;
    }

    /**
     * 显示消息
     */
    showMessage(data) {
        // 使用自定义通知替代alert
        this.showNotification(data.title, data.message, data.type);
    }

    /**
     * 显示通知
     */
    showNotification(title, message, type = 'info') {
        // 创建通知元素
        const notification = document.createElement('div');
        notification.className = `notification notification-${type}`;
        notification.innerHTML = `
            <div class="notification-header">
                <strong>${title}</strong>
                <button class="notification-close">&times;</button>
            </div>
            <div class="notification-body">${message}</div>
        `;

        // 添加到页面
        document.body.appendChild(notification);

        // 绑定关闭事件
        notification.querySelector('.notification-close').addEventListener('click', () => {
            document.body.removeChild(notification);
        });

        // 自动关闭
        setTimeout(() => {
            if (document.body.contains(notification)) {
                document.body.removeChild(notification);
            }
        }, 8000);
    }

    /**
     * 显示错误
     */
    showError(message) {
        const errorElement = document.getElementById('error-message');
        errorElement.textContent = message;
        errorElement.style.display = 'block';
        
        setTimeout(() => {
            errorElement.style.display = 'none';
        }, 5000);
    }

    /**
     * 显示加载状态
     */
    showLoading() {
        const loadingElement = document.getElementById('loading-overlay');
        loadingElement.style.display = 'flex';
    }

    /**
     * 隐藏加载状态
     */
    hideLoading() {
        const loadingElement = document.getElementById('loading-overlay');
        loadingElement.style.display = 'none';
    }

    /**
     * 获取配置
     */
    getConfig() {
        return this.config;
    }

    /**
     * 获取组件
     */
    getController() {
        return this.controller;
    }

    getRenderer() {
        return this.renderer;
    }

    getConnectionRenderer() {
        return this.connectionRenderer;
    }

    /**
     * 切换连接线显示/隐藏
     */
    toggleConnectionsVisibility() {
        const svg = document.getElementById('flow-svg');
        if (svg) {
            const isVisible = DOMUtils.isVisible(svg);
            DOMUtils.toggle(svg);

            const button = document.getElementById('show-connections-btn');
            DOMUtils.setText(button, isVisible ? '👁️ 显示连接线' : '👁️ 隐藏连接线');

            this.showNotification(
                '连接线显示',
                isVisible ? '连接线已隐藏' : '连接线已显示',
                'info'
            );
        }
    }

    /**
     * 清理资源
     */
    cleanup() {
        try {
            // 清理用户体验
            if (this.userExperience) {
                this.userExperience.cleanup();
            }

            // 清理模块接口
            if (this.moduleInterface) {
                this.moduleInterface.cleanup();
            }

            // 清理性能监控
            if (this.performanceMonitor) {
                this.performanceMonitor.cleanup();
            }

            // 清理错误恢复
            if (this.errorRecovery) {
                this.errorRecovery.cleanup();
            }

            // 清理事件管理器
            if (this.eventManager) {
                this.eventManager.cleanup();
            }

            // 清理组件
            if (this.connectionRenderer && this.connectionRenderer.eventManager) {
                this.connectionRenderer.eventManager.cleanup();
            }

            if (this.renderer && this.renderer.eventManager) {
                this.renderer.eventManager.cleanup();
            }

            // 清理性能标记
            PerformanceUtils.clearPerformanceMarks();

            console.log('[TradeFusion] 应用资源清理完成');
        } catch (error) {
            console.error('[TradeFusion] 资源清理失败:', error);
        }
    }

    /**
     * 初始化高级功能
     */
    initAdvancedFeatures() {
        try {
            // 初始化模块接口
            if (typeof ModuleInterface !== 'undefined') {
                this.moduleInterface = new ModuleInterface(this);
                console.log('[App] 模块接口已启动');
            }

            // 初始化性能监控
            if (typeof PerformanceMonitor !== 'undefined') {
                this.performanceMonitor = new PerformanceMonitor(this);
                this.performanceMonitor.startMonitoring();
                console.log('[App] 性能监控已启动');
            }

            // 初始化错误恢复
            if (typeof ErrorRecovery !== 'undefined') {
                this.errorRecovery = new ErrorRecovery(this);
                console.log('[App] 错误恢复系统已启动');
            }

            // 初始化用户体验
            if (typeof UserExperience !== 'undefined') {
                this.userExperience = new UserExperience(this);
                console.log('[App] 用户体验增强已启动');
            }
        } catch (error) {
            console.warn('[App] 高级功能初始化失败:', error);
        }
    }

    /**
     * 获取性能报告
     */
    getPerformanceReport() {
        if (this.performanceMonitor) {
            return this.performanceMonitor.getPerformanceReport();
        }
        return null;
    }

    /**
     * 页面卸载时清理
     */
    onBeforeUnload() {
        this.cleanup();
    }

    getBatchGenerator() {
        return this.batchGenerator;
    }
}

// 全局应用实例
window.TradeFusionApp = TradeFusionApp;
