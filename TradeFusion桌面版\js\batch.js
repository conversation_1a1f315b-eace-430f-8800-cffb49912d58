/**
 * BatchGenerator - Electron版本批处理生成器
 *
 * 功能：Electron环境下的真正一键启动
 * 职责：通过IPC通信直接控制Python进程
 *
 * <AUTHOR> Team
 * @version 2.0.0 (Electron版)
 */

class BatchGenerator {
    constructor(app, config) {
        this.app = app;
        this.config = config;
    }

    /**
     * 生成启动批处理文件内容
     */
    generateStartBatch(moduleState) {
        const pythonPath = this.config.config?.pythonExecutable || 'python';
        const workingDir = this.config.config?.workingDirectory || '..';
        const filePath = moduleState.filePath;

        return `@echo off
chcp 65001 >nul
title 启动 ${moduleState.name}
echo ========================================
echo TradeFusion 模块启动器
echo 模块名称: ${moduleState.name}
echo 文件路径: ${filePath}
echo ========================================
echo.

cd /d "${workingDir}"
echo 切换到工作目录: %CD%
echo.

echo 启动模块...
${pythonPath} "${filePath}"

echo.
echo 模块已退出，按任意键关闭窗口...
pause >nul`;
    }

    /**
     * 生成停止批处理文件内容
     */
    generateStopBatch(moduleState) {
        return `@echo off
chcp 65001 >nul
title 停止 ${moduleState.name}
echo ========================================
echo TradeFusion 模块停止器
echo 模块名称: ${moduleState.name}
echo ========================================
echo.

echo 正在查找并停止相关Python进程...
tasklist | findstr python.exe
echo.

echo 停止包含 "${moduleState.filePath}" 的Python进程...
for /f "tokens=2" %%i in ('tasklist /fi "imagename eq python.exe" /fo table /nh') do (
    wmic process where "processid=%%i and commandline like '%%${moduleState.filePath}%%'" delete 2>nul
)

echo.
echo 停止操作完成，按任意键关闭窗口...
pause >nul`;
    }

    /**
     * 生成批量启动批处理文件
     */
    generateBatchStartAll() {
        const controllableModules = Object.keys(this.config.modules)
            .filter(moduleId => this.config.modules[moduleId].hasControls)
            .map(moduleId => this.config.modules[moduleId]);

        const pythonPath = this.config.config?.pythonExecutable || 'python';
        const workingDir = this.config.config?.workingDirectory || '..';

        let batchContent = `@echo off
chcp 65001 >nul
title TradeFusion 批量启动器
echo ========================================
echo TradeFusion 批量启动器
echo 将启动 ${controllableModules.length} 个模块
echo ========================================
echo.

cd /d "${workingDir}"
echo 切换到工作目录: %CD%
echo.

`;

        controllableModules.forEach((module, index) => {
            batchContent += `echo [${index + 1}/${controllableModules.length}] 启动 ${module.name}...
start "TradeFusion-${module.name}" ${pythonPath} "${module.filePath}"
timeout /t 2 /nobreak >nul
echo.

`;
        });

        batchContent += `echo 所有模块启动完成！
echo 按任意键关闭窗口...
pause >nul`;

        return batchContent;
    }

    /**
     * 生成批量停止批处理文件
     */
    generateBatchStopAll() {
        return `@echo off
chcp 65001 >nul
title TradeFusion 批量停止器
echo ========================================
echo TradeFusion 批量停止器
echo 将停止所有Python进程
echo ========================================
echo.

echo 正在查找Python进程...
tasklist | findstr python.exe
echo.

echo 停止所有Python进程...
taskkill /f /im python.exe 2>nul
echo.

echo 停止操作完成！
echo 按任意键关闭窗口...
pause >nul`;
    }

    /**
     * 执行批处理命令（Electron一键启动）
     */
    async executeBatchCommand(moduleState, action) {
        try {
            // 检查是否在Electron环境中
            if (this.isElectronEnvironment()) {
                if (action === 'start') {
                    return await this.electronStartModule(moduleState);
                } else if (action === 'stop') {
                    return await this.electronStopModule(moduleState);
                }
            } else {
                // 浏览器环境：提示用户使用Electron版本
                this.showElectronRecommendation(action);
                return { success: false, error: '请使用Electron版本以获得真正的一键启动体验' };
            }

            console.log(`[BatchGenerator] ${action === 'start' ? '启动' : '停止'}命令已执行: ${moduleState.name}`);

        } catch (error) {
            console.error(`[BatchGenerator] 执行${action === 'start' ? '启动' : '停止'}命令失败:`, error);
            throw error;
        }
    }

    /**
     * 检查是否在Electron环境中
     */
    isElectronEnvironment() {
        return typeof window !== 'undefined' &&
               window.process &&
               window.process.type === 'renderer';
    }

    /**
     * Electron环境：启动模块
     */
    async electronStartModule(moduleState) {
        try {
            const { ipcRenderer } = require('electron');

            const moduleData = {
                name: moduleState.name,
                filePath: moduleState.filePath,
                workingDirectory: this.config.config?.workingDirectory || '..',
                pythonExecutable: this.config.config?.pythonExecutable || 'python'
            };

            console.log(`[BatchGenerator] Electron启动模块:`, moduleData);

            const result = await ipcRenderer.invoke('start-python-module', moduleData);

            if (result.success) {
                // 通知应用模块启动成功
                this.app.emit('module:electron-started', {
                    moduleId: moduleState.id,
                    name: moduleState.name,
                    pid: result.pid
                });

                // 显示成功通知
                if (this.app && this.app.showNotification) {
                    this.app.showNotification(
                        '启动成功',
                        `模块 "${moduleState.name}" 已成功启动 (PID: ${result.pid})`,
                        'success'
                    );
                }

                return result;
            } else {
                throw new Error(result.error);
            }

        } catch (error) {
            console.error('[BatchGenerator] Electron启动模块失败:', error);

            // 显示错误通知
            if (this.app && this.app.showNotification) {
                this.app.showNotification(
                    '启动失败',
                    `模块 "${moduleState.name}" 启动失败: ${error.message}`,
                    'error'
                );
            }

            throw error;
        }
    }

    /**
     * Electron环境：停止模块
     */
    async electronStopModule(moduleState) {
        try {
            const { ipcRenderer } = require('electron');

            console.log(`[BatchGenerator] Electron停止模块: ${moduleState.name}`);

            const result = await ipcRenderer.invoke('stop-python-module', moduleState.name);

            if (result.success) {
                // 通知应用模块停止成功
                this.app.emit('module:electron-stopped', {
                    moduleId: moduleState.id,
                    name: moduleState.name
                });

                // 显示成功通知
                if (this.app && this.app.showNotification) {
                    this.app.showNotification(
                        '停止成功',
                        `模块 "${moduleState.name}" 已成功停止`,
                        'success'
                    );
                }

                return result;
            } else {
                throw new Error(result.error);
            }

        } catch (error) {
            console.error('[BatchGenerator] Electron停止模块失败:', error);

            // 显示错误通知
            if (this.app && this.app.showNotification) {
                this.app.showNotification(
                    '停止失败',
                    `模块 "${moduleState.name}" 停止失败: ${error.message}`,
                    'error'
                );
            }

            throw error;
        }
    }

    /**
     * 显示Electron版本推荐
     */
    showElectronRecommendation(action) {
        if (this.app && this.app.showNotification) {
            this.app.showNotification(
                '推荐使用Electron版本',
                `要实现真正的一键${action}，请使用Electron版本。双击"启动Electron版.bat"即可体验！`,
                'info'
            );
        }
    }



    /**
     * 执行启动命令
     */
    executeStartCommand(moduleState) {
        const pythonPath = this.config.config?.pythonExecutable || 'python';
        const workingDir = this.config.config?.workingDirectory || '..';
        const filePath = moduleState.filePath;

        // 生成启动命令
        const command = `cd /d "${workingDir}" && start "TradeFusion-${moduleState.name}" ${pythonPath} "${filePath}"`;

        // 创建临时批处理文件并立即执行
        this.createAndExecuteTempBatch(command, `temp_start_${moduleState.id}.bat`);
    }

    /**
     * 执行停止命令
     */
    executeStopCommand(moduleState) {
        // 生成停止命令
        const command = `taskkill /f /fi "WINDOWTITLE eq TradeFusion-${moduleState.name}"`;

        // 创建临时批处理文件并立即执行
        this.createAndExecuteTempBatch(command, `temp_stop_${moduleState.id}.bat`);
    }

    /**
     * 创建临时批处理文件并执行
     */
    createAndExecuteTempBatch(command, filename) {
        const batchContent = `@echo off
chcp 65001 >nul
${command}
del "%~f0"`;

        // 创建并下载临时批处理文件
        const blob = new Blob([batchContent], { type: 'text/plain;charset=utf-8' });
        const url = URL.createObjectURL(blob);

        const a = document.createElement('a');
        a.href = url;
        a.download = filename;
        a.style.display = 'none';

        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);

        URL.revokeObjectURL(url);

        // 显示简化的提示
        this.showSimpleExecutionTip(filename);
    }

    /**
     * 显示简化的执行提示
     */
    showSimpleExecutionTip(filename) {
        // 使用应用的通知系统
        if (this.app && this.app.showNotification) {
            this.app.showNotification(
                '一键启动',
                `临时启动文件 "${filename}" 已下载，双击即可执行。文件执行后会自动删除。`,
                'info'
            );
        }
    }

    /**
     * 启动Python模块
     */
    startPythonModule(pythonPath, workingDir, filePath, moduleName) {
        // 使用Node.js child_process (如果在Electron环境中)
        if (typeof require !== 'undefined') {
            const { spawn } = require('child_process');
            const path = require('path');

            const fullPath = path.resolve(workingDir, filePath);
            const process = spawn(pythonPath, [fullPath], {
                cwd: workingDir,
                detached: true,
                stdio: 'ignore'
            });

            process.unref(); // 让进程在后台运行
            console.log(`[BatchGenerator] Python模块已启动: ${moduleName} (PID: ${process.pid})`);
            return process.pid;
        } else {
            // 浏览器环境的fallback方案
            this.showExecutionInstructions(moduleName, 'start', filePath);
        }
    }

    /**
     * 停止Python模块
     */
    stopPythonModule(filePath, moduleName) {
        if (typeof require !== 'undefined') {
            const { exec } = require('child_process');

            // 查找并终止包含指定文件路径的Python进程
            const command = `taskkill /f /fi "WINDOWTITLE eq *${filePath}*"`;
            exec(command, (error, stdout, stderr) => {
                if (error) {
                    console.warn(`[BatchGenerator] 停止进程时出现警告: ${error.message}`);
                } else {
                    console.log(`[BatchGenerator] Python模块已停止: ${moduleName}`);
                }
            });
        } else {
            // 浏览器环境的fallback方案
            this.showExecutionInstructions(moduleName, 'stop', filePath);
        }
    }

    /**
     * 显示执行说明（浏览器环境fallback）
     */
    showExecutionInstructions(moduleName, action, filePath) {
        const actionText = action === 'start' ? '启动' : '停止';
        const message = `由于浏览器安全限制，无法直接${actionText}Python模块。\n\n请手动执行以下操作：\n1. 打开命令提示符\n2. 切换到TradeFusion目录\n3. 执行命令：python "${filePath}"`;

        alert(`${actionText}模块: ${moduleName}\n\n${message}`);
    }

    /**
     * 下载批处理文件（保留作为备用方案）
     */
    downloadBatchFile(filename, content) {
        try {
            const blob = new Blob([content], { type: 'text/plain;charset=utf-8' });
            const url = URL.createObjectURL(blob);

            const a = document.createElement('a');
            a.href = url;
            a.download = filename;
            a.style.display = 'none';

            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);

            URL.revokeObjectURL(url);

            console.log(`[BatchGenerator] 批处理文件已下载: ${filename}`);

        } catch (error) {
            console.error('[BatchGenerator] 下载批处理文件失败:', error);
            throw error;
        }
    }

    /**
     * 生成并下载启动批处理文件
     */
    downloadStartBatch(moduleState) {
        const content = this.generateStartBatch(moduleState);
        const filename = `启动_${moduleState.name}.bat`;
        this.downloadBatchFile(filename, content);
        return filename;
    }

    /**
     * 生成并下载停止批处理文件
     */
    downloadStopBatch(moduleState) {
        const content = this.generateStopBatch(moduleState);
        const filename = `停止_${moduleState.name}.bat`;
        this.downloadBatchFile(filename, content);
        return filename;
    }

    /**
     * 生成并下载批量启动批处理文件
     */
    downloadBatchStartAll() {
        const content = this.generateBatchStartAll();
        const filename = `TradeFusion_批量启动.bat`;
        this.downloadBatchFile(filename, content);
        return filename;
    }

    /**
     * 生成并下载批量停止批处理文件
     */
    downloadBatchStopAll() {
        const content = this.generateBatchStopAll();
        const filename = `TradeFusion_批量停止.bat`;
        this.downloadBatchFile(filename, content);
        return filename;
    }

    /**
     * 验证路径安全性
     */
    validatePath(filePath) {
        // 基本的路径安全验证
        const dangerousPatterns = [
            /\.\./,           // 目录遍历
            /[<>:"|?*]/,      // Windows非法字符
            /^[a-zA-Z]:\\/,   // 绝对路径（可能需要根据需求调整）
        ];

        for (const pattern of dangerousPatterns) {
            if (pattern.test(filePath)) {
                throw new Error(`不安全的文件路径: ${filePath}`);
            }
        }

        return true;
    }

    /**
     * 获取批处理文件模板
     */
    getBatchTemplate(type) {
        const templates = {
            start: this.generateStartBatch,
            stop: this.generateStopBatch,
            startAll: this.generateBatchStartAll,
            stopAll: this.generateBatchStopAll
        };

        return templates[type] || null;
    }
}

// 导出批处理生成器
window.BatchGenerator = BatchGenerator;
