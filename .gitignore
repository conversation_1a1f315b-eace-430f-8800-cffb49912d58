# Dependencies
node_modules/
*/node_modules/
frontend/node_modules/
backend/node_modules/

# Build outputs
dist/
build/
*/dist/
*/build/

# Environment variables
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# IDE files
.vscode/
.idea/
*.swp
*.swo

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Logs
logs
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*
lerna-debug.log*

# Runtime data
pids
*.pid
*.seed
*.pid.lock

# Coverage directory used by tools like istanbul
coverage/
*.lcov

# nyc test coverage
.nyc_output

# Dependency directories
jspm_packages/

# Optional npm cache directory
.npm

# Optional eslint cache
.eslintcache

# Microbundle cache
.rpt2_cache/
.rts2_cache_cjs/
.rts2_cache_es/
.rts2_cache_umd/

# Optional REPL history
.node_repl_history

# Output of 'npm pack'
*.tgz

# Yarn Integrity file
.yarn-integrity

# parcel-bundler cache (https://parceljs.org/)
.cache
.parcel-cache

# Next.js build output
.next

# Nuxt.js build / generate output
.nuxt

# Gatsby files
.cache/
public

# Storybook build outputs
.out
.storybook-out

# Temporary folders
tmp/
temp/

# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
env/
venv/
ENV/
env.bak/
venv.bak/

# Database
*.db
*.sqlite
*.sqlite3

# PostgreSQL data directory (ignore all data files)
数据库_PostgreSQL/base/
数据库_PostgreSQL/global/
数据库_PostgreSQL/pg_wal/
数据库_PostgreSQL/pg_xact/
数据库_PostgreSQL/pg_multixact/
数据库_PostgreSQL/pg_subtrans/
数据库_PostgreSQL/pg_logical/
数据库_PostgreSQL/postmaster.pid
数据库_PostgreSQL/postmaster.opts
数据库_PostgreSQL/*.conf
数据库_PostgreSQL/*.auto.conf

# Backup directories
备份_*/

# Temporary files
检查*.py
清理*.py
FileScopeMCP-*.json

# Test and debug files
test_*.py
debug_*.py
check_*.py

# MCP memory files
.cunzhi-memory/

# Environment cleanup tools (temporary)
环境清理工具/

# Migration and system tools (temporary)
*迁移*.py
*迁移*.md
启动TradeFusion_纯虚拟环境.py
公共模块/系统清理.py
数据库迁移前备份工具.py

# Data files
*.csv
*.dat
*.BLK
*.html

# SQL files (temporary structure files)
*临时表结构*.sql
*PostgreSQL*.sql

# Compiled files
*.com
*.class
*.dll
*.exe
*.o
*.so

# Packages
*.7z
*.dmg
*.gz
*.iso
*.jar
*.rar
*.tar
*.zip

# Virtual environments
venv/
ENV/
env/
.venv/
backend_env/
*_env/
