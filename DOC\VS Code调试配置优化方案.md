# VS Code调试配置优化方案

## 问题描述
在使用F5调试Python模块时，VS Code会弹出异常处理对话框，询问如何处理未捕获的异常。每个模块都会弹出此窗口，严重影响开发效率。

## 问题原因
VS Code的默认调试配置会在遇到未捕获异常时暂停执行，并弹出对话框让用户选择处理方式。

## 解决方案

### 1. 调试配置优化
在 `.vscode/launch.json` 中添加以下关键配置：

```json
{
    "version": "0.2.0",
    "configurations": [
        {
            "name": "Python: Current File",
            "type": "debugpy",
            "request": "launch",
            "program": "${file}",
            "console": "integratedTerminal",
            "cwd": "${workspaceFolder}",
            "env": {
                "PYTHONPATH": "${workspaceFolder}"
            },
            "justMyCode": false,
            "stopOnEntry": false,
            "showReturnValue": false,
            "redirectOutput": true,
            "breakOnSystemExitZero": false,
            "uncaughtExceptionHandling": "userUnhandled"
        }
    ]
}
```

### 2. 关键配置参数说明

- **`uncaughtExceptionHandling: "userUnhandled"`**: 只在用户代码中的未处理异常时暂停
- **`stopOnEntry: false`**: 不在程序入口处暂停
- **`showReturnValue: false`**: 不显示函数返回值
- **`breakOnSystemExitZero: false`**: 程序正常退出时不暂停
- **`redirectOutput: true`**: 重定向输出到集成终端

### 3. 替代方案

#### 方案A：全局设置
在VS Code设置中添加：
```json
{
    "python.debugging.stopOnEntry": false,
    "python.debugging.showReturnValue": false
}
```

#### 方案B：使用快捷键
- **Ctrl+F5**: 运行而不调试（推荐用于测试）
- **F5**: 调试运行（仅在需要断点调试时使用）

### 4. 最佳实践

1. **日常测试**: 使用 `Ctrl+F5` 运行模块
2. **问题调试**: 使用 `F5` 并设置具体断点
3. **批量测试**: 使用命令行或批处理文件
4. **异常处理**: 在代码中添加适当的try-catch块

### 5. 验证方法

配置完成后：
1. 打开任意Python模块
2. 按F5启动调试
3. 确认不再弹出异常处理对话框
4. 程序正常执行完毕

## 注意事项

- 此配置会影响所有Python文件的调试行为
- 如需调试特定异常，可临时修改配置
- 建议在代码中添加适当的异常处理逻辑
- 重要异常仍会在终端中显示错误信息

## 故障排除

如果问题仍然存在：
1. 重启VS Code
2. 检查Python扩展版本
3. 清除VS Code缓存
4. 确认launch.json语法正确
