"""
快速性能测试
"""

import time
import sys
from pathlib import Path

# 确保可以导入项目模块
project_root = Path(__file__).parent.parent
if str(project_root) not in sys.path:
    sys.path.insert(0, str(project_root))

from 公共模块.tradefusion_logger_integration import HighFrequencyModuleIntegration

print('=== 优化后性能测试 ===')
integration = HighFrequencyModuleIntegration('优化测试')

def mock_data_processing():
    data = [i * 2 for i in range(100)]
    return {'success': True, 'data_count': len(data), 'results': data}

# 测试不使用日志系统
start_time = time.time()
for i in range(1000):
    mock_data_processing()
no_logging_time = time.time() - start_time
print(f'不使用日志：{no_logging_time:.4f}秒')

# 测试使用优化后的日志系统
start_time = time.time()
for i in range(1000):
    integration.execute_with_logging(mock_data_processing)
with_logging_time = time.time() - start_time
print(f'使用日志：{with_logging_time:.4f}秒')

performance_impact = ((with_logging_time - no_logging_time) / no_logging_time) * 100
print(f'性能影响：{performance_impact:.2f}%')

if performance_impact < 5:
    print('✅ 性能影响很小，符合要求')
elif performance_impact < 10:
    print('⚠️ 性能影响适中，可以接受')
else:
    print('❌ 性能影响较大，需要进一步优化')
