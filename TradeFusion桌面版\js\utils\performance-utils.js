/**
 * 性能优化工具模块
 * 
 * 功能：提供性能优化相关的工具函数
 * 职责：性能监控、优化策略、资源管理
 * 
 * <AUTHOR> Team
 * @version 2.0.0
 */

class PerformanceUtils {
    constructor() {
        this.timers = new Map();
        this.observers = new Map();
        this.rafCallbacks = new Set();
        this.isRAFRunning = false;
    }

    /**
     * 性能计时器
     */
    static startTimer(name) {
        if (typeof performance !== 'undefined' && performance.mark) {
            performance.mark(`${name}-start`);
        }
        return Date.now();
    }

    /**
     * 结束计时并返回耗时
     */
    static endTimer(name, startTime = null) {
        const endTime = Date.now();
        
        if (typeof performance !== 'undefined' && performance.mark && performance.measure) {
            try {
                performance.mark(`${name}-end`);
                performance.measure(name, `${name}-start`, `${name}-end`);
                
                const measures = performance.getEntriesByName(name, 'measure');
                if (measures.length > 0) {
                    const duration = measures[measures.length - 1].duration;
                    console.log(`[Performance] ${name}: ${duration.toFixed(2)}ms`);
                    return duration;
                }
            } catch (error) {
                console.warn('[Performance] 性能测量失败:', error);
            }
        }
        
        if (startTime) {
            const duration = endTime - startTime;
            console.log(`[Performance] ${name}: ${duration}ms`);
            return duration;
        }
        
        return 0;
    }

    /**
     * 函数执行时间测量装饰器
     */
    static measureTime(name) {
        return function(target, propertyKey, descriptor) {
            const originalMethod = descriptor.value;
            
            descriptor.value = function(...args) {
                const startTime = PerformanceUtils.startTimer(`${name || propertyKey}`);
                const result = originalMethod.apply(this, args);
                
                if (result instanceof Promise) {
                    return result.finally(() => {
                        PerformanceUtils.endTimer(`${name || propertyKey}`, startTime);
                    });
                } else {
                    PerformanceUtils.endTimer(`${name || propertyKey}`, startTime);
                    return result;
                }
            };
            
            return descriptor;
        };
    }

    /**
     * 防抖函数（高性能版本）
     */
    static debounce(func, delay = 300, immediate = false) {
        let timeout;
        let lastCallTime;
        let lastInvokeTime = 0;
        let lastArgs;
        let lastThis;
        let result;

        function invokeFunc(time) {
            const args = lastArgs;
            const thisArg = lastThis;
            
            lastArgs = lastThis = undefined;
            lastInvokeTime = time;
            result = func.apply(thisArg, args);
            return result;
        }

        function leadingEdge(time) {
            lastInvokeTime = time;
            timeout = setTimeout(timerExpired, delay);
            return immediate ? invokeFunc(time) : result;
        }

        function remainingWait(time) {
            const timeSinceLastCall = time - lastCallTime;
            const timeSinceLastInvoke = time - lastInvokeTime;
            const timeWaiting = delay - timeSinceLastCall;
            return timeWaiting;
        }

        function shouldInvoke(time) {
            const timeSinceLastCall = time - lastCallTime;
            const timeSinceLastInvoke = time - lastInvokeTime;
            return (lastCallTime === undefined || (timeSinceLastCall >= delay) ||
                    (timeSinceLastCall < 0) || (timeSinceLastInvoke >= delay));
        }

        function timerExpired() {
            const time = Date.now();
            if (shouldInvoke(time)) {
                return trailingEdge(time);
            }
            timeout = setTimeout(timerExpired, remainingWait(time));
        }

        function trailingEdge(time) {
            timeout = undefined;
            if (lastArgs) {
                return invokeFunc(time);
            }
            lastArgs = lastThis = undefined;
            return result;
        }

        function cancel() {
            if (timeout !== undefined) {
                clearTimeout(timeout);
            }
            lastInvokeTime = 0;
            lastArgs = lastCallTime = lastThis = timeout = undefined;
        }

        function flush() {
            return timeout === undefined ? result : trailingEdge(Date.now());
        }

        function debounced(...args) {
            const time = Date.now();
            const isInvoking = shouldInvoke(time);

            lastArgs = args;
            lastThis = this;
            lastCallTime = time;

            if (isInvoking) {
                if (timeout === undefined) {
                    return leadingEdge(lastCallTime);
                }
                timeout = setTimeout(timerExpired, delay);
                return invokeFunc(lastCallTime);
            }
            if (timeout === undefined) {
                timeout = setTimeout(timerExpired, delay);
            }
            return result;
        }

        debounced.cancel = cancel;
        debounced.flush = flush;
        return debounced;
    }

    /**
     * 节流函数（高性能版本）
     */
    static throttle(func, wait = 100, options = {}) {
        let timeout;
        let previous = 0;
        let result;
        
        const { leading = true, trailing = true } = options;

        function later() {
            previous = leading === false ? 0 : Date.now();
            timeout = null;
            result = func.apply(this, arguments);
        }

        function throttled(...args) {
            const now = Date.now();
            if (!previous && leading === false) previous = now;
            
            const remaining = wait - (now - previous);
            
            if (remaining <= 0 || remaining > wait) {
                if (timeout) {
                    clearTimeout(timeout);
                    timeout = null;
                }
                previous = now;
                result = func.apply(this, args);
            } else if (!timeout && trailing !== false) {
                timeout = setTimeout(() => later.apply(this, args), remaining);
            }
            
            return result;
        }

        throttled.cancel = function() {
            clearTimeout(timeout);
            previous = 0;
            timeout = null;
        };

        return throttled;
    }

    /**
     * requestAnimationFrame 批处理
     */
    static batchRAF(callback) {
        if (!this.rafCallbacks) {
            this.rafCallbacks = new Set();
            this.isRAFRunning = false;
        }

        this.rafCallbacks.add(callback);

        if (!this.isRAFRunning) {
            this.isRAFRunning = true;
            requestAnimationFrame(() => {
                const callbacks = Array.from(this.rafCallbacks);
                this.rafCallbacks.clear();
                this.isRAFRunning = false;

                callbacks.forEach(cb => {
                    try {
                        cb();
                    } catch (error) {
                        console.error('[PerformanceUtils] RAF回调执行错误:', error);
                    }
                });
            });
        }
    }

    /**
     * 延迟执行（使用 requestIdleCallback 或 setTimeout）
     */
    static defer(callback, options = {}) {
        if (typeof requestIdleCallback !== 'undefined') {
            return requestIdleCallback(callback, options);
        } else {
            return setTimeout(callback, 0);
        }
    }

    /**
     * 内存使用监控
     */
    static getMemoryUsage() {
        if (typeof performance !== 'undefined' && performance.memory) {
            return {
                used: Math.round(performance.memory.usedJSHeapSize / 1048576), // MB
                total: Math.round(performance.memory.totalJSHeapSize / 1048576), // MB
                limit: Math.round(performance.memory.jsHeapSizeLimit / 1048576) // MB
            };
        }
        return null;
    }

    /**
     * 监控内存使用情况
     */
    static monitorMemory(interval = 5000, callback = null) {
        const monitor = () => {
            const memory = this.getMemoryUsage();
            if (memory) {
                const usage = (memory.used / memory.limit * 100).toFixed(2);
                console.log(`[Memory] 使用: ${memory.used}MB / ${memory.limit}MB (${usage}%)`);
                
                if (callback) {
                    callback(memory);
                }
                
                // 内存使用超过80%时警告
                if (memory.used / memory.limit > 0.8) {
                    console.warn('[Memory] 内存使用率过高，建议优化');
                }
            }
        };

        monitor(); // 立即执行一次
        const intervalId = setInterval(monitor, interval);
        
        // 返回停止监控的函数
        return () => clearInterval(intervalId);
    }

    /**
     * 虚拟滚动优化
     */
    static createVirtualScroller(container, itemHeight, renderItem, totalItems) {
        let scrollTop = 0;
        let containerHeight = container.clientHeight;
        
        const visibleCount = Math.ceil(containerHeight / itemHeight) + 2; // 缓冲区
        const startIndex = Math.floor(scrollTop / itemHeight);
        const endIndex = Math.min(startIndex + visibleCount, totalItems);
        
        const render = this.throttle(() => {
            const fragment = document.createDocumentFragment();
            
            // 清空容器
            container.innerHTML = '';
            
            // 创建占位空间
            const spacerTop = document.createElement('div');
            spacerTop.style.height = `${startIndex * itemHeight}px`;
            fragment.appendChild(spacerTop);
            
            // 渲染可见项
            for (let i = startIndex; i < endIndex; i++) {
                const item = renderItem(i);
                if (item) {
                    fragment.appendChild(item);
                }
            }
            
            // 创建底部占位空间
            const spacerBottom = document.createElement('div');
            spacerBottom.style.height = `${(totalItems - endIndex) * itemHeight}px`;
            fragment.appendChild(spacerBottom);
            
            container.appendChild(fragment);
        }, 16);
        
        const onScroll = (e) => {
            scrollTop = e.target.scrollTop;
            render();
        };
        
        container.addEventListener('scroll', onScroll);
        render(); // 初始渲染
        
        return {
            update: render,
            destroy: () => container.removeEventListener('scroll', onScroll)
        };
    }

    /**
     * 图片懒加载
     */
    static lazyLoadImages(selector = 'img[data-src]', options = {}) {
        const defaultOptions = {
            root: null,
            rootMargin: '50px',
            threshold: 0.1
        };
        
        const config = { ...defaultOptions, ...options };
        
        if (typeof IntersectionObserver === 'undefined') {
            // 降级处理
            const images = document.querySelectorAll(selector);
            images.forEach(img => {
                if (img.dataset.src) {
                    img.src = img.dataset.src;
                    delete img.dataset.src;
                }
            });
            return { disconnect: () => {} };
        }
        
        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    const img = entry.target;
                    if (img.dataset.src) {
                        img.src = img.dataset.src;
                        delete img.dataset.src;
                        observer.unobserve(img);
                    }
                }
            });
        }, config);
        
        const images = document.querySelectorAll(selector);
        images.forEach(img => observer.observe(img));
        
        return observer;
    }

    /**
     * 批量DOM操作优化
     */
    static batchDOMUpdates(updates) {
        return new Promise(resolve => {
            this.batchRAF(() => {
                const fragment = document.createDocumentFragment();
                let result;
                
                try {
                    result = updates(fragment);
                } catch (error) {
                    console.error('[PerformanceUtils] 批量DOM更新错误:', error);
                    resolve(null);
                    return;
                }
                
                resolve(result);
            });
        });
    }

    /**
     * 长任务分片执行
     */
    static async processInChunks(items, processor, chunkSize = 100, delay = 5) {
        const results = [];
        
        for (let i = 0; i < items.length; i += chunkSize) {
            const chunk = items.slice(i, i + chunkSize);
            const chunkResults = chunk.map(processor);
            results.push(...chunkResults);
            
            // 让出控制权，避免阻塞UI
            if (i + chunkSize < items.length) {
                await new Promise(resolve => setTimeout(resolve, delay));
            }
        }
        
        return results;
    }

    /**
     * 清理性能标记
     */
    static clearPerformanceMarks(name = null) {
        if (typeof performance !== 'undefined' && performance.clearMarks) {
            if (name) {
                performance.clearMarks(name);
                performance.clearMeasures(name);
            } else {
                performance.clearMarks();
                performance.clearMeasures();
            }
        }
    }

    /**
     * 获取性能报告
     */
    static getPerformanceReport() {
        if (typeof performance === 'undefined') return null;
        
        const navigation = performance.getEntriesByType('navigation')[0];
        const paint = performance.getEntriesByType('paint');
        const memory = this.getMemoryUsage();
        
        return {
            navigation: navigation ? {
                domContentLoaded: Math.round(navigation.domContentLoadedEventEnd - navigation.navigationStart),
                loadComplete: Math.round(navigation.loadEventEnd - navigation.navigationStart),
                domInteractive: Math.round(navigation.domInteractive - navigation.navigationStart)
            } : null,
            paint: paint.reduce((acc, entry) => {
                acc[entry.name] = Math.round(entry.startTime);
                return acc;
            }, {}),
            memory: memory,
            timestamp: Date.now()
        };
    }
}

// 创建全局实例
const performanceUtils = new PerformanceUtils();

// 导出性能工具类
window.PerformanceUtils = PerformanceUtils;
window.performanceUtils = performanceUtils;
