# TradeFusion日志系统配置文件
# 统一管理所有日志相关配置

# 基础日志配置
logging:
  # 日志级别
  level: "INFO"
  
  # 日志格式
  format: "[%(asctime)s] %(levelname)s - %(message)s"
  
  # 时间格式
  datefmt: "%H:%M:%S"
  
  # 日志文件配置
  file:
    enabled: true
    path: "logs/"
    max_size_mb: 50
    backup_count: 30
    encoding: "utf-8"
    
  # 控制台输出配置
  console:
    enabled: true
    colored: true

# TradeFusion三色标识配置
colors:
  # 模块名颜色（红色）
  module: "\033[91m"
  
  # 表名颜色（黄色）
  table: "\033[93m"
  
  # 数字颜色（绿色）
  number: "\033[92m"
  
  # 重置颜色
  reset: "\033[0m"
  
  # 错误标识
  error: "❌"

# 格式规范
format_rules:
  # 模块名格式
  module_format: "[{module_name}]"
  
  # 表名格式
  table_format: "{table_name}"
  
  # 数字格式（千分位分隔符）
  number_format: "{number:,}条"
  
  # 调用链格式
  caller_format: "(由[{caller}]调用)"

# 性能优化配置
performance:
  # 日志器缓存
  logger_cache: true
  
  # 异步写入（暂不启用）
  async_write: false
  
  # 批量写入（暂不启用）
  batch_write: false
  
  # 文件轮转策略
  rotation:
    max_size: "50MB"
    backup_count: 30
    
# 存储策略
storage:
  # 控制台输出
  console_output: true
  
  # 文件存储
  file_storage: true
  
  # 日志保留天数
  retention_days: 30
  
  # 压缩存储（暂不启用）
  compression: false

# 监控配置
monitoring:
  # 实时监控
  real_time: true
  
  # 监控间隔（秒）
  interval: 0.5
  
  # 监控目录
  log_directory: "logs"
  
  # 监控文件模式
  file_pattern: "*.log"

# 模块特定配置
modules:
  # 数据采集模块
  data_collection:
    log_level: "INFO"
    detailed_logging: false
    
  # 数据处理模块
  data_processing:
    log_level: "INFO"
    performance_logging: true
    
  # 输出模块
  output:
    log_level: "INFO"
    result_logging: true
    
  # 系统模块
  system:
    log_level: "INFO"
    debug_logging: false

# 开发环境配置
development:
  # 详细日志
  verbose: false
  
  # 调试模式
  debug: false
  
  # 性能分析
  profiling: false

# 生产环境配置
production:
  # 优化性能
  optimized: true
  
  # 错误报告
  error_reporting: true
  
  # 自动清理
  auto_cleanup: true
