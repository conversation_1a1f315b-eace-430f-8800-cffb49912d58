@echo off
chcp 65001 >nul
title TradeFusion Desktop - Electron Launcher

echo ========================================
echo TradeFusion Desktop - Electron Launcher
echo ========================================
echo.

cd /d "%~dp0"
echo Current directory: %CD%
echo.

echo Checking Node.js environment...
node --version >nul 2>&1
if errorlevel 1 (
    echo Error: Node.js not found, please install Node.js first
    echo Download: https://nodejs.org/
    pause
    exit /b 1
)

echo Node.js version:
node --version
echo.

echo Checking dependencies...
if not exist "node_modules" (
    echo First run, installing dependencies...
    npm install
    if errorlevel 1 (
        echo Error: Dependencies installation failed
        pause
        exit /b 1
    )
    echo Dependencies installation completed
    echo.
)

echo Starting TradeFusion Desktop...
npm start

echo.
echo TradeFusion Desktop has exited
pause
