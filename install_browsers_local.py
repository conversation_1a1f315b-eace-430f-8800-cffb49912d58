#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
TradeFusion项目本地浏览器安装脚本
将Playwright浏览器安装到项目本地目录，避免使用C盘
"""

import os
import sys
import subprocess
from pathlib import Path

def install_browsers_locally():
    """安装浏览器到项目本地目录"""
    
    # 获取项目根目录
    project_root = Path(__file__).parent
    browsers_dir = project_root / 'browsers'
    
    print(f"🔧 TradeFusion浏览器本地安装")
    print(f"📁 项目根目录: {project_root}")
    print(f"📁 浏览器安装目录: {browsers_dir}")
    
    # 创建浏览器目录
    browsers_dir.mkdir(exist_ok=True)
    
    # 设置环境变量，指定Playwright浏览器安装位置
    env = os.environ.copy()
    env['PLAYWRIGHT_BROWSERS_PATH'] = str(browsers_dir)
    
    print(f"🌍 设置环境变量: PLAYWRIGHT_BROWSERS_PATH={browsers_dir}")
    
    # 获取虚拟环境中的playwright命令
    venv_dir = project_root / 'venv'
    if sys.platform == 'win32':
        playwright_cmd = venv_dir / 'Scripts' / 'playwright.exe'
        python_cmd = venv_dir / 'Scripts' / 'python.exe'
    else:
        playwright_cmd = venv_dir / 'bin' / 'playwright'
        python_cmd = venv_dir / 'bin' / 'python'
    
    if not playwright_cmd.exists():
        print(f"❌ 未找到playwright命令: {playwright_cmd}")
        print("请确保已在虚拟环境中安装playwright")
        return False
    
    try:
        # 安装chromium浏览器
        print(f"📥 正在安装Chromium浏览器...")
        result = subprocess.run(
            [str(playwright_cmd), 'install', 'chromium'],
            env=env,
            capture_output=True,
            text=True,
            timeout=300  # 5分钟超时
        )
        
        if result.returncode == 0:
            print(f"✅ Chromium安装成功")
            print(f"📄 安装日志: {result.stdout}")
        else:
            print(f"❌ Chromium安装失败")
            print(f"📄 错误信息: {result.stderr}")
            return False
            
        # 检查安装结果
        print(f"📋 检查安装结果...")
        
        # 列出安装的浏览器
        list_result = subprocess.run(
            [str(python_cmd), '-c', 
             f"import os; os.environ['PLAYWRIGHT_BROWSERS_PATH']='{browsers_dir}'; "
             "from playwright.sync_api import sync_playwright; "
             "p = sync_playwright().start(); "
             "print('Chromium路径:', p.chromium.executable_path); "
             "p.stop()"],
            env=env,
            capture_output=True,
            text=True
        )
        
        if list_result.returncode == 0:
            print(f"✅ 浏览器路径验证成功:")
            print(f"📄 {list_result.stdout}")
        else:
            print(f"⚠️ 浏览器路径验证失败:")
            print(f"📄 {list_result.stderr}")
        
        # 显示目录结构
        print(f"📁 浏览器目录结构:")
        for item in browsers_dir.rglob('*'):
            if item.is_file() and item.name.endswith(('.exe', '.app')):
                print(f"   📄 {item.relative_to(project_root)}")
        
        return True
        
    except subprocess.TimeoutExpired:
        print(f"❌ 安装超时（5分钟）")
        return False
    except Exception as e:
        print(f"❌ 安装过程中出现错误: {e}")
        return False

def create_env_file():
    """创建环境变量配置文件"""
    project_root = Path(__file__).parent
    browsers_dir = project_root / 'browsers'
    
    env_file = project_root / '.env'
    with open(env_file, 'w', encoding='utf-8') as f:
        f.write(f"# TradeFusion项目环境变量配置\n")
        f.write(f"PLAYWRIGHT_BROWSERS_PATH={browsers_dir}\n")
    
    print(f"📄 已创建环境变量配置文件: {env_file}")

def main():
    """主函数"""
    print("=" * 60)
    print("🚀 TradeFusion项目本地浏览器安装工具")
    print("=" * 60)
    
    try:
        # 安装浏览器
        if install_browsers_locally():
            print(f"\n✅ 浏览器安装完成！")
            
            # 创建环境变量文件
            create_env_file()
            
            print(f"\n📋 使用说明:")
            print(f"1. 浏览器已安装到项目本地 browsers/ 目录")
            print(f"2. 环境变量配置已保存到 .env 文件")
            print(f"3. 重新运行采集模块即可使用本地浏览器")
            
        else:
            print(f"\n❌ 浏览器安装失败！")
            print(f"请检查网络连接和虚拟环境配置")
            
    except KeyboardInterrupt:
        print(f"\n⚠️ 用户中断安装")
    except Exception as e:
        print(f"\n❌ 安装过程中出现未知错误: {e}")

if __name__ == "__main__":
    main()
