# ===================== 生成DZH3人气股票板块.py =====================
# 功能：从数据库读取个股人气数据，生成大智慧3软件的BLK格式板块文件
# 输出：包含优质股票代码的自选股板块文件
# {{ AURA-X: Modify - 将SQLite连接改为PostgreSQL连接. Approval: 寸止(ID:1737734400). }}
import psycopg2
import psycopg2.extras
from pathlib import Path
import sys
import time

project_root = Path(__file__).absolute().parent.parent
if str(project_root) not in sys.path:
    sys.path.insert(0, str(project_root))

# 导入TradeFusion统一日志标准
from 公共模块.TradeFusion统一日志标准 import 获取日志器
logger = 获取日志器("生成DZH3人气板块")

def generate_block_file():
    max_date = None  # 初始化变量
    try:
        # 导入配置管理
        # {{ AURA-X: Modify - 修改为PostgreSQL连接配置. Approval: 寸止(ID:1737734400). }}
        # DZH3板块文件路径（这个路径通常是固定的，不在配置中）
        blk_path = r'E:\dzh3\USERDATA\block\自选股人气.BLK'

        # {{ AURA-X: Modify - 统一PostgreSQL密码为标准密码. Approval: 寸止(ID:1737734400). }}
        # PostgreSQL连接配置
        db_config = {
            'host': 'localhost',
            'port': 5432,
            'database': 'tradefusion',
            'user': 'postgres',
            'password': 'ymjatTUU520'
        }

        conn = psycopg2.connect(**db_config)
        cursor = conn.cursor()

        cursor.execute('SELECT MAX("日期") FROM "个股人气表"')
        max_date = cursor.fetchone()[0]

        if not max_date:
            return_msg = "板块文件生成 无有效日期 失败 (生成): 无有效日期数据"
            return f"failed: {return_msg}"

        # {{ AURA-X: Modify - 修改为PostgreSQL语法. Approval: 寸止(ID:1737734400). }}
        cursor.execute('''
            SELECT DISTINCT "股票代码"
            FROM "个股人气表"
            WHERE "日期" = %s AND "综合人气评分" > 0
            ORDER BY "股票代码"
        ''', (max_date,))

        stock_codes = cursor.fetchall()

        if not stock_codes:
            return "failed: 无合格股票数据"

        header = b'\xa5\x00Q\xff'
        code_data = []

        for code in stock_codes:
            encoded = code[0].ljust(12, '\x00').encode('ascii')[:12]
            code_data.append(encoded)

        with open(blk_path, 'wb') as f:
            f.write(header)
            for code in code_data:
                f.write(code)

        logger.记录模块执行("BLK文件生成完成", len(stock_codes))
        return "success"

    # {{ AURA-X: Modify - 修改为PostgreSQL异常处理. Approval: 寸止(ID:1737734400). }}
    except psycopg2.Error as e:
        error_msg = f"板块文件生成 {max_date or '未知日期'} 失败 (生成): 数据库错误 - {str(e)}"
        return f"failed: {error_msg}"
    except IOError as e:
        error_msg = f"板块文件生成 {max_date or '未知日期'} 失败 (生成): 文件写入错误 - {str(e)}"
        return f"failed: {error_msg}"
    except Exception as e:
        error_msg = f"板块文件生成 {max_date or '未知日期'} 失败 (生成): 系统异常 - {type(e).__name__} - {str(e)}"
        return f"failed: {error_msg}"
    finally:
        if 'conn' in locals():
            conn.close()

def main():
    try:
        return generate_block_file()
    except Exception as e:
        error_msg = f"板块文件生成 未知日期 失败 (生成): 未捕获异常 - {str(e)}"
        return f"failed: {error_msg}"

if __name__ == "__main__":
    result = main()