/**
 * 性能监控模块
 * 
 * 功能：实时监控应用性能指标
 * 职责：性能数据收集、分析、报告
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 */

class PerformanceMonitor {
    constructor(app) {
        this.app = app;
        this.metrics = {
            memory: [],
            renderTime: [],
            connectionUpdates: [],
            eventProcessing: [],
            errors: []
        };
        this.isMonitoring = false;
        this.monitorInterval = null;
        this.observers = new Map();
        
        this.initializeMonitoring();
    }

    /**
     * 初始化性能监控
     */
    initializeMonitoring() {
        // 监听应用事件
        this.app.on('module:rendered', (data) => {
            this.recordRenderTime(data.moduleId, data.duration);
        });

        this.app.on('connection:updated', (data) => {
            this.recordConnectionUpdate(data.count, data.duration);
        });

        this.app.on('app:error', (data) => {
            this.recordError(data);
        });

        // 监听页面性能事件
        this.setupPerformanceObserver();
        
        console.log('[PerformanceMonitor] 性能监控已初始化');
    }

    /**
     * 设置性能观察器
     */
    setupPerformanceObserver() {
        if (typeof PerformanceObserver !== 'undefined') {
            try {
                // 监听导航性能
                const navObserver = new PerformanceObserver((list) => {
                    const entries = list.getEntries();
                    entries.forEach(entry => {
                        if (entry.entryType === 'navigation') {
                            this.recordNavigationTiming(entry);
                        }
                    });
                });
                navObserver.observe({ entryTypes: ['navigation'] });
                this.observers.set('navigation', navObserver);

                // 监听资源加载性能
                const resourceObserver = new PerformanceObserver((list) => {
                    const entries = list.getEntries();
                    entries.forEach(entry => {
                        if (entry.entryType === 'resource') {
                            this.recordResourceTiming(entry);
                        }
                    });
                });
                resourceObserver.observe({ entryTypes: ['resource'] });
                this.observers.set('resource', resourceObserver);

                // 监听长任务
                if ('longtask' in window) {
                    const longtaskObserver = new PerformanceObserver((list) => {
                        const entries = list.getEntries();
                        entries.forEach(entry => {
                            this.recordLongTask(entry);
                        });
                    });
                    longtaskObserver.observe({ entryTypes: ['longtask'] });
                    this.observers.set('longtask', longtaskObserver);
                }
            } catch (error) {
                console.warn('[PerformanceMonitor] PerformanceObserver设置失败:', error);
            }
        }
    }

    /**
     * 开始监控
     */
    startMonitoring(interval = 5000) {
        if (this.isMonitoring) return;

        this.isMonitoring = true;
        this.monitorInterval = setInterval(() => {
            this.collectMetrics();
        }, interval);

        console.log('[PerformanceMonitor] 开始性能监控');
    }

    /**
     * 停止监控
     */
    stopMonitoring() {
        if (!this.isMonitoring) return;

        this.isMonitoring = false;
        if (this.monitorInterval) {
            clearInterval(this.monitorInterval);
            this.monitorInterval = null;
        }

        console.log('[PerformanceMonitor] 停止性能监控');
    }

    /**
     * 收集性能指标
     */
    collectMetrics() {
        const timestamp = Date.now();

        // 收集内存使用情况
        const memory = PerformanceUtils.getMemoryUsage();
        if (memory) {
            this.metrics.memory.push({
                timestamp,
                used: memory.used,
                total: memory.total,
                limit: memory.limit,
                usage: (memory.used / memory.limit * 100).toFixed(2)
            });

            // 保留最近100条记录
            if (this.metrics.memory.length > 100) {
                this.metrics.memory.shift();
            }

            // 内存使用率过高警告
            if (memory.used / memory.limit > 0.8) {
                this.recordWarning('内存使用率过高', {
                    usage: `${(memory.used / memory.limit * 100).toFixed(2)}%`,
                    used: `${memory.used}MB`,
                    limit: `${memory.limit}MB`
                });
            }
        }

        // 收集DOM节点数量
        const domNodes = document.querySelectorAll('*').length;
        this.recordMetric('domNodes', domNodes, timestamp);

        // 收集事件监听器数量
        const eventListeners = this.getEventListenerCount();
        this.recordMetric('eventListeners', eventListeners, timestamp);
    }

    /**
     * 记录渲染时间
     */
    recordRenderTime(moduleId, duration) {
        this.metrics.renderTime.push({
            timestamp: Date.now(),
            moduleId,
            duration
        });

        // 保留最近50条记录
        if (this.metrics.renderTime.length > 50) {
            this.metrics.renderTime.shift();
        }

        // 渲染时间过长警告
        if (duration > 100) {
            this.recordWarning('模块渲染时间过长', {
                moduleId,
                duration: `${duration}ms`
            });
        }
    }

    /**
     * 记录连接线更新
     */
    recordConnectionUpdate(count, duration) {
        this.metrics.connectionUpdates.push({
            timestamp: Date.now(),
            count,
            duration
        });

        // 保留最近50条记录
        if (this.metrics.connectionUpdates.length > 50) {
            this.metrics.connectionUpdates.shift();
        }

        // 连接线更新时间过长警告
        if (duration > 50) {
            this.recordWarning('连接线更新时间过长', {
                count,
                duration: `${duration}ms`
            });
        }
    }

    /**
     * 记录错误
     */
    recordError(errorData) {
        this.metrics.errors.push({
            timestamp: Date.now(),
            ...errorData
        });

        // 保留最近20条错误记录
        if (this.metrics.errors.length > 20) {
            this.metrics.errors.shift();
        }
    }

    /**
     * 记录导航性能
     */
    recordNavigationTiming(entry) {
        const timing = {
            timestamp: Date.now(),
            domContentLoaded: entry.domContentLoadedEventEnd - entry.domContentLoadedEventStart,
            loadComplete: entry.loadEventEnd - entry.loadEventStart,
            domInteractive: entry.domInteractive - entry.fetchStart,
            firstPaint: 0,
            firstContentfulPaint: 0
        };

        // 获取绘制性能
        const paintEntries = performance.getEntriesByType('paint');
        paintEntries.forEach(paint => {
            if (paint.name === 'first-paint') {
                timing.firstPaint = paint.startTime;
            } else if (paint.name === 'first-contentful-paint') {
                timing.firstContentfulPaint = paint.startTime;
            }
        });

        this.recordMetric('navigation', timing, timing.timestamp);
    }

    /**
     * 记录资源加载性能
     */
    recordResourceTiming(entry) {
        if (entry.name.includes('tradefusion') || entry.name.includes('.js') || entry.name.includes('.css')) {
            this.recordMetric('resource', {
                name: entry.name,
                duration: entry.duration,
                size: entry.transferSize || 0,
                type: entry.initiatorType
            }, Date.now());
        }
    }

    /**
     * 记录长任务
     */
    recordLongTask(entry) {
        this.recordWarning('检测到长任务', {
            duration: `${entry.duration.toFixed(2)}ms`,
            startTime: entry.startTime
        });
    }

    /**
     * 记录通用指标
     */
    recordMetric(type, data, timestamp = Date.now()) {
        if (!this.metrics[type]) {
            this.metrics[type] = [];
        }

        this.metrics[type].push({
            timestamp,
            data
        });

        // 限制记录数量
        if (this.metrics[type].length > 100) {
            this.metrics[type].shift();
        }
    }

    /**
     * 记录警告
     */
    recordWarning(message, details = {}) {
        console.warn(`[PerformanceMonitor] ${message}:`, details);
        
        this.app.emit('performance:warning', {
            message,
            details,
            timestamp: Date.now()
        });
    }

    /**
     * 获取事件监听器数量（估算）
     */
    getEventListenerCount() {
        let count = 0;
        
        // 统计应用事件监听器
        if (this.app.eventListeners) {
            this.app.eventListeners.forEach(listeners => {
                count += listeners.length;
            });
        }

        // 统计EventManager监听器
        if (this.app.eventManager) {
            count += this.app.eventManager.getListenerCount();
        }

        return count;
    }

    /**
     * 获取性能报告
     */
    getPerformanceReport() {
        const report = {
            timestamp: Date.now(),
            summary: this.generateSummary(),
            metrics: this.metrics,
            recommendations: this.generateRecommendations()
        };

        return report;
    }

    /**
     * 生成性能摘要
     */
    generateSummary() {
        const memory = this.metrics.memory;
        const renderTime = this.metrics.renderTime;
        const connectionUpdates = this.metrics.connectionUpdates;

        const summary = {
            memory: {
                current: memory.length > 0 ? memory[memory.length - 1] : null,
                average: memory.length > 0 ? 
                    (memory.reduce((sum, m) => sum + parseFloat(m.usage), 0) / memory.length).toFixed(2) : 0,
                peak: memory.length > 0 ? 
                    Math.max(...memory.map(m => parseFloat(m.usage))).toFixed(2) : 0
            },
            rendering: {
                averageTime: renderTime.length > 0 ? 
                    (renderTime.reduce((sum, r) => sum + r.duration, 0) / renderTime.length).toFixed(2) : 0,
                slowestRender: renderTime.length > 0 ? 
                    Math.max(...renderTime.map(r => r.duration)) : 0
            },
            connections: {
                averageUpdateTime: connectionUpdates.length > 0 ? 
                    (connectionUpdates.reduce((sum, c) => sum + c.duration, 0) / connectionUpdates.length).toFixed(2) : 0,
                totalUpdates: connectionUpdates.length
            },
            errors: {
                total: this.metrics.errors.length,
                recent: this.metrics.errors.filter(e => Date.now() - e.timestamp < 300000).length // 最近5分钟
            }
        };

        return summary;
    }

    /**
     * 生成优化建议
     */
    generateRecommendations() {
        const recommendations = [];
        const summary = this.generateSummary();

        // 内存优化建议
        if (parseFloat(summary.memory.average) > 70) {
            recommendations.push({
                type: 'memory',
                priority: 'high',
                message: '内存使用率较高，建议优化内存管理',
                suggestions: [
                    '清理未使用的事件监听器',
                    '减少DOM节点数量',
                    '优化大型对象的生命周期'
                ]
            });
        }

        // 渲染性能建议
        if (parseFloat(summary.rendering.averageTime) > 50) {
            recommendations.push({
                type: 'rendering',
                priority: 'medium',
                message: '模块渲染时间较长，建议优化渲染性能',
                suggestions: [
                    '使用虚拟化技术处理大量元素',
                    '减少DOM操作频率',
                    '优化CSS选择器'
                ]
            });
        }

        // 连接线性能建议
        if (parseFloat(summary.connections.averageUpdateTime) > 30) {
            recommendations.push({
                type: 'connections',
                priority: 'medium',
                message: '连接线更新时间较长，建议优化算法',
                suggestions: [
                    '实现增量更新',
                    '使用防抖优化频繁更新',
                    '优化路径计算算法'
                ]
            });
        }

        // 错误率建议
        if (summary.errors.recent > 5) {
            recommendations.push({
                type: 'stability',
                priority: 'high',
                message: '最近错误频率较高，建议检查代码稳定性',
                suggestions: [
                    '增加错误边界处理',
                    '完善输入验证',
                    '添加更多单元测试'
                ]
            });
        }

        return recommendations;
    }

    /**
     * 清理资源
     */
    cleanup() {
        this.stopMonitoring();
        
        // 清理性能观察器
        this.observers.forEach(observer => {
            try {
                observer.disconnect();
            } catch (error) {
                console.warn('[PerformanceMonitor] 清理观察器失败:', error);
            }
        });
        this.observers.clear();

        console.log('[PerformanceMonitor] 资源清理完成');
    }
}

// 导出性能监控类
window.PerformanceMonitor = PerformanceMonitor;
