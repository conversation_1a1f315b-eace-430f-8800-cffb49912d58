/**
 * ModuleController - 模块控制器（简化版）
 * 
 * 功能：专注于模块的启动、停止、状态管理
 * 职责：单一职责 - 只管理模块状态和控制逻辑
 * 
 * <AUTHOR> Team
 * @version 2.0.0 (简化版)
 */

class ModuleController {
    constructor(app, config) {
        this.app = app;
        this.config = config;
        this.moduleStates = new Map();
        
        // 初始化模块状态
        this.initializeModuleStates();
        
        // 绑定事件监听器
        this.bindEvents();
    }

    /**
     * 初始化模块状态
     */
    initializeModuleStates() {
        if (!this.config || !this.config.modules) return;

        Object.keys(this.config.modules).forEach(moduleId => {
            const moduleConfig = this.config.modules[moduleId];
            this.moduleStates.set(moduleId, {
                id: moduleId,
                name: moduleConfig.name,
                status: moduleConfig.status || 'idle',
                filePath: moduleConfig.filePath,
                hasControls: moduleConfig.hasControls,
                lastStartTime: null,
                lastStopTime: null,
                errorCount: 0
            });
        });

        console.log('[ModuleController] 模块状态初始化完成', this.moduleStates);
    }

    /**
     * 绑定事件监听器
     */
    bindEvents() {
        // 单个模块控制
        this.app.on('module:start', (data) => this.startModule(data.moduleId));
        this.app.on('module:stop', (data) => this.stopModule(data.moduleId));
        this.app.on('module:restart', (data) => this.restartModule(data.moduleId));

        // 批量模块控制
        this.app.on('app:start-all-modules', () => this.startAllModules());
        this.app.on('app:stop-all-modules', () => this.stopAllModules());
        this.app.on('app:restart-system', () => this.restartSystem());

        // Electron特有事件监听
        this.app.on('module:electron-started', (data) => {
            this.updateModuleStatus(data.moduleId, 'running');
            console.log(`[ModuleController] Electron模块启动: ${data.name} (PID: ${data.pid})`);

            // 启动模拟日志生成器
            this.startLogSimulator(data.moduleId, data.name);
        });

        this.app.on('module:electron-stopped', (data) => {
            this.updateModuleStatus(data.moduleId, 'idle');
            console.log(`[ModuleController] Electron模块停止: ${data.name}`);

            // 停止模拟日志生成器
            this.stopLogSimulator(data.moduleId);
        });
    }

    /**
     * 启动模块
     */
    async startModule(moduleId) {
        const moduleState = this.moduleStates.get(moduleId);
        if (!moduleState) {
            throw new Error(`模块 ${moduleId} 不存在`);
        }

        if (!moduleState.hasControls) {
            console.warn(`[ModuleController] 模块 ${moduleId} 不支持控制操作`);
            return;
        }

        if (moduleState.status === 'running') {
            console.warn(`[ModuleController] 模块 ${moduleId} 已在运行中`);
            return;
        }

        try {
            // 更新状态为启动中
            this.updateModuleStatus(moduleId, 'starting');

            // 清除旧日志并添加启动日志
            this.app.emit('module:clear-logs', { moduleId });
            this.app.emit('module:log', {
                moduleId,
                message: `开始启动模块: ${moduleState.name}`,
                type: 'info'
            });

            // 直接执行启动命令（支持异步）
            const result = await this.app.getBatchGenerator().executeBatchCommand(moduleState, 'start');

            // 根据实际结果更新状态
            if (result && result.success) {
                this.updateModuleStatus(moduleId, 'running');
                moduleState.lastStartTime = new Date();

                // 添加成功日志
                this.app.emit('module:log', {
                    moduleId,
                    message: `模块启动成功 (PID: ${result.pid || 'N/A'})`,
                    type: 'success'
                });

                this.app.emit('module:started', {
                    moduleId,
                    name: moduleState.name,
                    timestamp: moduleState.lastStartTime,
                    pid: result.pid
                });

                console.log(`[ModuleController] 模块 ${moduleId} 启动成功，PID: ${result.pid}`);
            } else {
                // 添加错误日志
                this.app.emit('module:log', {
                    moduleId,
                    message: `启动失败: ${result?.error || '未知错误'}`,
                    type: 'error'
                });
                throw new Error(result?.error || '启动失败');
            }

        } catch (error) {
            this.updateModuleStatus(moduleId, 'error');
            moduleState.errorCount++;
            
            this.app.emit('module:start-error', { 
                moduleId, 
                name: moduleState.name, 
                error 
            });
            
            throw error;
        }
    }

    /**
     * 停止模块
     */
    async stopModule(moduleId) {
        const moduleState = this.moduleStates.get(moduleId);
        if (!moduleState) {
            throw new Error(`模块 ${moduleId} 不存在`);
        }

        if (!moduleState.hasControls) {
            console.warn(`[ModuleController] 模块 ${moduleId} 不支持控制操作`);
            return;
        }

        if (moduleState.status === 'idle') {
            console.warn(`[ModuleController] 模块 ${moduleId} 已停止`);
            return;
        }

        try {
            // 更新状态为停止中
            this.updateModuleStatus(moduleId, 'stopping');

            // 添加停止日志
            this.app.emit('module:log', {
                moduleId,
                message: `开始停止模块: ${moduleState.name}`,
                type: 'info'
            });

            // 直接执行停止命令（支持异步）
            const result = await this.app.getBatchGenerator().executeBatchCommand(moduleState, 'stop');

            // 根据实际结果更新状态
            if (result && result.success) {
                this.updateModuleStatus(moduleId, 'idle');
                moduleState.lastStopTime = new Date();

                // 添加成功日志
                this.app.emit('module:log', {
                    moduleId,
                    message: `模块已成功停止`,
                    type: 'success'
                });

                this.app.emit('module:stopped', {
                    moduleId,
                    name: moduleState.name,
                    timestamp: moduleState.lastStopTime
                });

                console.log(`[ModuleController] 模块 ${moduleId} 停止成功`);
            } else {
                // 添加错误日志
                this.app.emit('module:log', {
                    moduleId,
                    message: `停止失败: ${result?.error || '未知错误'}`,
                    type: 'error'
                });
                throw new Error(result?.error || '停止失败');
            }

        } catch (error) {
            this.updateModuleStatus(moduleId, 'error');
            moduleState.errorCount++;
            
            this.app.emit('module:stop-error', { 
                moduleId, 
                name: moduleState.name, 
                error 
            });
            
            throw error;
        }
    }

    /**
     * 重启模块
     */
    async restartModule(moduleId) {
        await this.stopModule(moduleId);

        // 等待2秒后重新启动
        return new Promise((resolve, reject) => {
            setTimeout(async () => {
                try {
                    await this.startModule(moduleId);
                    resolve();
                } catch (error) {
                    reject(error);
                }
            }, 2000);
        });
    }

    /**
     * 启动所有模块
     */
    async startAllModules() {
        const controllableModules = Array.from(this.moduleStates.values())
            .filter(module => module.hasControls);

        for (const module of controllableModules) {
            try {
                await this.startModule(module.id);
                await new Promise(resolve => setTimeout(resolve, 500));
            } catch (error) {
                console.error(`[ModuleController] 启动模块 ${module.id} 失败:`, error);
            }
        }

        this.app.emit('app:all-modules-start-requested', {
            total: controllableModules.length
        });
    }

    /**
     * 停止所有模块
     */
    async stopAllModules() {
        const runningModules = Array.from(this.moduleStates.values())
            .filter(module => module.hasControls && module.status === 'running');

        for (const module of runningModules) {
            try {
                await this.stopModule(module.id);
                await new Promise(resolve => setTimeout(resolve, 500));
            } catch (error) {
                console.error(`[ModuleController] 停止模块 ${module.id} 失败:`, error);
            }
        }

        this.app.emit('app:all-modules-stop-requested', {
            total: runningModules.length
        });
    }

    /**
     * 重启系统
     */
    async restartSystem() {
        await this.stopAllModules();
        setTimeout(() => {
            this.startAllModules();
        }, 3000);
    }

    /**
     * 显示批处理文件使用说明
     */
    showBatchInstructions(filename, action) {
        const message = `批处理文件 "${filename}" 已下载到您的下载文件夹。\n\n请按以下步骤${action}模块：\n1. 找到下载的 ${filename} 文件\n2. 双击运行该文件\n3. 等待操作完成\n\n注意：请确保以管理员权限运行批处理文件。`;
        
        this.app.emit('ui:show-message', {
            type: 'info',
            title: `${action}模块`,
            message: message
        });
    }

    /**
     * 更新模块状态
     */
    updateModuleStatus(moduleId, status) {
        const moduleState = this.moduleStates.get(moduleId);
        if (moduleState) {
            moduleState.status = status;
            this.app.emit('module:status-changed', {
                moduleId,
                status,
                moduleState: { ...moduleState }
            });
        }
    }

    /**
     * 获取模块状态
     */
    getModuleStatus(moduleId) {
        return this.moduleStates.get(moduleId);
    }

    /**
     * 获取所有模块状态
     */
    getAllModuleStates() {
        return new Map(this.moduleStates);
    }

    /**
     * 获取统计信息
     */
    getStats() {
        const states = Array.from(this.moduleStates.values());
        return {
            total: states.length,
            running: states.filter(s => s.status === 'running').length,
            idle: states.filter(s => s.status === 'idle').length,
            error: states.filter(s => s.status === 'error').length,
            controllable: states.filter(s => s.hasControls).length
        };
    }

    /**
     * 启动日志模拟器（用于演示）
     */
    startLogSimulator(moduleId, moduleName) {
        // 清除已存在的定时器
        this.stopLogSimulator(moduleId);

        if (!this.logSimulators) {
            this.logSimulators = new Map();
        }

        // 模拟日志消息
        const logMessages = [
            { message: `连接到数据源...`, type: 'info' },
            { message: `开始处理数据文件`, type: 'info' },
            { message: `成功读取86条记录`, type: 'success' },
            { message: `数据验证通过`, type: 'success' },
            { message: `写入数据库完成`, type: 'success' },
            { message: `等待下次执行...`, type: 'info' }
        ];

        let messageIndex = 0;
        const interval = setInterval(() => {
            const moduleState = this.moduleStates.get(moduleId);
            if (!moduleState || moduleState.status !== 'running') {
                this.stopLogSimulator(moduleId);
                return;
            }

            const logData = logMessages[messageIndex % logMessages.length];
            this.app.emit('module:log', {
                moduleId,
                message: logData.message,
                type: logData.type
            });

            messageIndex++;
        }, 3000); // 每3秒一条日志

        this.logSimulators.set(moduleId, interval);

        // 立即发送第一条日志
        this.app.emit('module:log', {
            moduleId,
            message: `${moduleName} 开始运行循环模式`,
            type: 'info'
        });
    }

    /**
     * 停止日志模拟器
     */
    stopLogSimulator(moduleId) {
        if (this.logSimulators && this.logSimulators.has(moduleId)) {
            clearInterval(this.logSimulators.get(moduleId));
            this.logSimulators.delete(moduleId);
        }
    }
}

// 导出模块控制器
window.ModuleController = ModuleController;
