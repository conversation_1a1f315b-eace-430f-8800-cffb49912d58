/**
 * ConnectionRenderer - 连接线渲染器
 * 
 * 功能：管理模块间连接线的绘制、编辑和交互
 * 职责：连接线的可视化和用户交互管理
 * 
 * <AUTHOR> Team
 * @version 2.0.0
 */

class ConnectionRenderer {
    constructor(app, config) {
        this.app = app;
        this.config = config;
        this.svg = null;
        this.connections = new Map(); // 存储连接线元素
        this.selectedConnection = null;
        this.isEditMode = false;
        this.tempConnection = null; // 临时连接线（拖拽时）
        this.eventManager = EventUtils.createEventManager();

        // 连接线样式配置
        this.connectionStyles = {
            'rq-flow': { color: '#8b5cf6', width: 2, dashArray: 'none' },
            'xgb-flow': { color: '#f97316', width: 2, dashArray: 'none' },
            'database-flow': { color: '#10b981', width: 2, dashArray: 'none' },
            'data-flow': { color: '#06b6d4', width: 2, dashArray: '5,5' }
        };

        // 性能优化：防抖更新函数
        this.debouncedUpdateConnections = PerformanceUtils.debounce(
            () => this.updateAllConnections(),
            16
        );

        this.bindEvents();
    }

    /**
     * 初始化连接线渲染器
     */
    init() {
        this.svg = document.getElementById('flow-svg');
        if (!this.svg) {
            console.error('[ConnectionRenderer] SVG元素未找到');
            return;
        }
        
        this.setupSVG();
        this.render();
        console.log('[ConnectionRenderer] 连接线渲染器初始化完成');
    }

    /**
     * 设置SVG画布
     */
    setupSVG() {
        // 设置SVG尺寸和样式
        this.svg.style.position = 'absolute';
        this.svg.style.top = '0';
        this.svg.style.left = '0';
        this.svg.style.width = '100%';
        this.svg.style.height = '100%';
        this.svg.style.pointerEvents = 'none';
        this.svg.style.zIndex = '1';
        
        // 更新箭头标记样式
        this.updateArrowMarkers();
    }

    /**
     * 更新箭头标记
     */
    updateArrowMarkers() {
        const defs = this.svg.querySelector('defs');
        if (!defs) return;
        
        // 为每种连接类型创建箭头标记
        Object.keys(this.connectionStyles).forEach(type => {
            const style = this.connectionStyles[type];
            const markerId = `arrowhead-${type}`;
            
            // 检查是否已存在
            let marker = defs.querySelector(`#${markerId}`);
            if (!marker) {
                marker = document.createElementNS('http://www.w3.org/2000/svg', 'marker');
                marker.id = markerId;
                marker.setAttribute('markerWidth', '10');
                marker.setAttribute('markerHeight', '7');
                marker.setAttribute('refX', '9');
                marker.setAttribute('refY', '3.5');
                marker.setAttribute('orient', 'auto');
                
                const polygon = document.createElementNS('http://www.w3.org/2000/svg', 'polygon');
                polygon.setAttribute('points', '0 0, 10 3.5, 0 7');
                polygon.setAttribute('fill', style.color);
                
                marker.appendChild(polygon);
                defs.appendChild(marker);
            }
        });
    }

    /**
     * 绑定事件监听
     */
    bindEvents() {
        try {
            // 监听模块渲染完成
            this.app.on('ui:modules-rendered', () => {
                setTimeout(() => this.render(), 100);
            });

            // 监听模块位置变化（使用防抖优化）
            this.app.on('module:position-changed', () => {
                this.debouncedUpdateConnections();
            });

            // 监听连接线编辑模式切换
            this.app.on('connection:toggle-edit-mode', () => {
                this.toggleEditMode();
            });

            // 监听连接线添加
            this.app.on('connection:add', (data) => {
                this.addConnection(data.from, data.to, data.type, data.label);
            });

            // 监听连接线删除
            this.app.on('connection:delete', (data) => {
                this.deleteConnection(data.connectionId);
            });
        } catch (error) {
            this.app.handleError(error, 'ConnectionRenderer事件绑定失败');
        }
    }

    /**
     * 渲染所有连接线
     */
    render() {
        if (!this.svg || !this.config.connections) {
            return;
        }
        
        // 清空现有连接线
        this.clearConnections();
        
        // 渲染每条连接线
        this.config.connections.forEach((connection, index) => {
            this.renderConnection(connection, index);
        });
        
        console.log(`[ConnectionRenderer] 渲染了 ${this.config.connections.length} 条连接线`);
    }

    /**
     * 渲染单条连接线
     */
    renderConnection(connection, index) {
        const fromElement = document.getElementById(`module-${connection.from}`);
        const toElement = document.getElementById(`module-${connection.to}`);
        
        if (!fromElement || !toElement) {
            console.warn(`[ConnectionRenderer] 模块未找到: ${connection.from} -> ${connection.to}`);
            return;
        }
        
        const fromPos = this.getModuleConnectionPoint(fromElement, 'output');
        const toPos = this.getModuleConnectionPoint(toElement, 'input');
        
        const connectionGroup = this.createConnectionGroup(connection, index, fromPos, toPos);
        this.svg.appendChild(connectionGroup);
        
        // 存储连接线引用
        this.connections.set(`${connection.from}-${connection.to}-${index}`, {
            element: connectionGroup,
            config: connection,
            index: index
        });
    }

    /**
     * 创建连接线组
     */
    createConnectionGroup(connection, index, fromPos, toPos) {
        try {
            const group = DOMUtils.createSVGElement('g', {
                'class': 'connection-group',
                'data-connection-id': `${connection.from}-${connection.to}-${index}`,
                'data-type': connection.type
            });

            // 创建连接线路径
            const path = this.createConnectionPath(fromPos, toPos, connection.type);
            group.appendChild(path);

            // 创建标签（如果有）
            if (connection.label) {
                const label = this.createConnectionLabel(fromPos, toPos, connection.label);
                group.appendChild(label);
            }

            // 添加交互事件
            this.bindConnectionEvents(group, connection, index);

            return group;
        } catch (error) {
            this.app.handleError(error, 'ConnectionRenderer创建连接线组失败');
            return null;
        }
    }

    /**
     * 创建连接线路径
     */
    createConnectionPath(fromPos, toPos, type) {
        try {
            const style = this.connectionStyles[type] || this.connectionStyles['data-flow'];

            // 计算贝塞尔曲线路径
            const pathData = this.calculateBezierPath(fromPos, toPos);

            const path = DOMUtils.createSVGElement('path', {
                'd': pathData,
                'stroke': style.color,
                'stroke-width': style.width,
                'fill': 'none',
                'marker-end': `url(#arrowhead-${type})`
            });

            if (style.dashArray !== 'none') {
                path.setAttribute('stroke-dasharray', style.dashArray);
            }

            // 添加交互样式
            DOMUtils.setStyle(path, {
                cursor: 'pointer',
                transition: 'stroke-width 0.2s ease'
            });

            return path;
        } catch (error) {
            this.app.handleError(error, 'ConnectionRenderer创建连接线路径失败');
            return null;
        }
    }

    /**
     * 计算贝塞尔曲线路径
     */
    calculateBezierPath(fromPos, toPos) {
        const dx = toPos.x - fromPos.x;
        const dy = toPos.y - fromPos.y;
        
        // 控制点偏移量
        const controlOffset = Math.max(50, Math.abs(dx) * 0.3);
        
        const cp1x = fromPos.x + controlOffset;
        const cp1y = fromPos.y;
        const cp2x = toPos.x - controlOffset;
        const cp2y = toPos.y;
        
        return `M ${fromPos.x} ${fromPos.y} C ${cp1x} ${cp1y}, ${cp2x} ${cp2y}, ${toPos.x} ${toPos.y}`;
    }

    /**
     * 创建连接线标签
     */
    createConnectionLabel(fromPos, toPos, labelText) {
        const text = document.createElementNS('http://www.w3.org/2000/svg', 'text');
        
        // 计算标签位置（路径中点）
        const midX = (fromPos.x + toPos.x) / 2;
        const midY = (fromPos.y + toPos.y) / 2 - 8;
        
        text.setAttribute('x', midX);
        text.setAttribute('y', midY);
        text.setAttribute('text-anchor', 'middle');
        text.setAttribute('class', 'connection-label');
        text.style.fontSize = '11px';
        text.style.fill = '#64748b';
        text.style.fontFamily = 'system-ui, sans-serif';
        text.style.pointerEvents = 'none';
        text.style.userSelect = 'none';
        
        // 添加背景
        const rect = document.createElementNS('http://www.w3.org/2000/svg', 'rect');
        const textBBox = text.getBBox ? text.getBBox() : { width: labelText.length * 6, height: 12 };
        
        rect.setAttribute('x', midX - textBBox.width / 2 - 4);
        rect.setAttribute('y', midY - textBBox.height / 2 - 2);
        rect.setAttribute('width', textBBox.width + 8);
        rect.setAttribute('height', textBBox.height + 4);
        rect.setAttribute('fill', 'rgba(255, 255, 255, 0.9)');
        rect.setAttribute('stroke', 'rgba(203, 213, 225, 0.5)');
        rect.setAttribute('rx', '3');
        rect.style.pointerEvents = 'none';
        
        text.textContent = labelText;
        
        const labelGroup = document.createElementNS('http://www.w3.org/2000/svg', 'g');
        labelGroup.appendChild(rect);
        labelGroup.appendChild(text);
        
        return labelGroup;
    }

    /**
     * 获取模块连接点位置
     */
    getModuleConnectionPoint(moduleElement, direction) {
        try {
            return DOMUtils.getConnectionPoint(moduleElement, direction, this.svg);
        } catch (error) {
            this.app.handleError(error, 'ConnectionRenderer获取连接点位置失败');
            return { x: 0, y: 0 };
        }
    }

    /**
     * 绑定连接线事件
     */
    bindConnectionEvents(group, connection, index) {
        try {
            const path = DOMUtils.find(group, 'path');
            if (!path) return;

            // 使用EventManager管理事件
            this.eventManager.add(group, 'mouseenter', () => {
                const style = this.getConnectionStyle(connection.type);
                const hoverWidth = style.hoverWidth || (style.width + 1);
                DOMUtils.attr(path, 'stroke-width', hoverWidth);
                DOMUtils.setStyle(group, { filter: 'drop-shadow(0 2px 4px rgba(0,0,0,0.2))' });
            });

            this.eventManager.add(group, 'mouseleave', () => {
                if (group !== this.selectedConnection) {
                    const style = this.getConnectionStyle(connection.type);
                    DOMUtils.attr(path, 'stroke-width', style.width);
                    DOMUtils.setStyle(group, { filter: 'none' });
                }
            });

            // 点击选择
            this.eventManager.add(group, 'click', (e) => {
                EventUtils.stopPropagation(e);
                this.selectConnection(group);
            });
        } catch (error) {
            this.app.handleError(error, 'ConnectionRenderer绑定连接线事件失败');
        }
    }

    /**
     * 选择连接线
     */
    selectConnection(connectionGroup) {
        // 取消之前的选择
        if (this.selectedConnection) {
            this.selectedConnection.style.filter = 'none';
            const prevPath = this.selectedConnection.querySelector('path');
            const prevType = this.selectedConnection.getAttribute('data-type');
            const originalWidth = this.connectionStyles[prevType]?.width || 2;
            prevPath.setAttribute('stroke-width', originalWidth);
        }
        
        // 选择新连接线
        this.selectedConnection = connectionGroup;
        connectionGroup.style.filter = 'drop-shadow(0 4px 8px rgba(0,0,0,0.3))';
        const path = connectionGroup.querySelector('path');
        path.setAttribute('stroke-width', parseInt(path.getAttribute('stroke-width')) + 2);
        
        // 触发选择事件
        const connectionId = connectionGroup.getAttribute('data-connection-id');
        this.app.emit('connection:selected', { connectionId });
    }

    /**
     * 更新所有连接线位置
     */
    updateAllConnections() {
        this.connections.forEach((connectionData, connectionId) => {
            const connection = connectionData.config;
            const group = connectionData.element;
            
            const fromElement = document.getElementById(`module-${connection.from}`);
            const toElement = document.getElementById(`module-${connection.to}`);
            
            if (fromElement && toElement) {
                const fromPos = this.getModuleConnectionPoint(fromElement, 'output');
                const toPos = this.getModuleConnectionPoint(toElement, 'input');
                
                // 更新路径
                const path = group.querySelector('path');
                const pathData = this.calculateBezierPath(fromPos, toPos);
                path.setAttribute('d', pathData);
                
                // 更新标签位置
                const labelGroup = group.querySelector('g');
                if (labelGroup) {
                    const text = labelGroup.querySelector('text');
                    const rect = labelGroup.querySelector('rect');
                    if (text && rect) {
                        const midX = (fromPos.x + toPos.x) / 2;
                        const midY = (fromPos.y + toPos.y) / 2 - 8;
                        
                        text.setAttribute('x', midX);
                        text.setAttribute('y', midY);
                        
                        const textBBox = text.getBBox ? text.getBBox() : { width: text.textContent.length * 6, height: 12 };
                        rect.setAttribute('x', midX - textBBox.width / 2 - 4);
                        rect.setAttribute('y', midY - textBBox.height / 2 - 2);
                    }
                }
            }
        });
    }

    /**
     * 清空所有连接线
     */
    clearConnections() {
        this.connections.clear();
        this.selectedConnection = null;
        
        // 清空SVG中的连接线组
        const groups = this.svg.querySelectorAll('.connection-group');
        groups.forEach(group => group.remove());
    }

    /**
     * 切换编辑模式
     */
    toggleEditMode() {
        this.isEditMode = !this.isEditMode;
        this.svg.style.pointerEvents = this.isEditMode ? 'auto' : 'none';

        // 切换编辑模式样式
        const container = document.getElementById('canvas-viewport');
        if (container) {
            if (this.isEditMode) {
                container.classList.add('connection-edit-mode');
                this.showConnectionPoints();
            } else {
                container.classList.remove('connection-edit-mode');
                this.hideConnectionPoints();
            }
        }

        // 更新按钮状态
        const button = document.getElementById('toggle-edit-mode-btn');
        if (button) {
            button.textContent = this.isEditMode ? '✅ 编辑模式' : '✏️ 编辑模式';
            button.classList.toggle('active', this.isEditMode);
        }

        console.log(`[ConnectionRenderer] 编辑模式: ${this.isEditMode ? '开启' : '关闭'}`);
        this.app.emit('connection:edit-mode-changed', { isEditMode: this.isEditMode });
    }

    /**
     * 显示连接点
     */
    showConnectionPoints() {
        const moduleElements = document.querySelectorAll('.module-node');
        moduleElements.forEach(moduleElement => {
            this.addConnectionPoints(moduleElement);
        });
    }

    /**
     * 隐藏连接点
     */
    hideConnectionPoints() {
        const connectionPoints = document.querySelectorAll('.module-connection-point');
        connectionPoints.forEach(point => point.remove());
    }

    /**
     * 为模块添加连接点
     */
    addConnectionPoints(moduleElement) {
        const moduleId = moduleElement.id.replace('module-', '');

        // 输入连接点
        const inputPoint = document.createElement('div');
        inputPoint.className = 'module-connection-point input';
        inputPoint.dataset.moduleId = moduleId;
        inputPoint.dataset.type = 'input';
        moduleElement.appendChild(inputPoint);

        // 输出连接点
        const outputPoint = document.createElement('div');
        outputPoint.className = 'module-connection-point output';
        outputPoint.dataset.moduleId = moduleId;
        outputPoint.dataset.type = 'output';
        moduleElement.appendChild(outputPoint);

        // 绑定连接点事件
        this.bindConnectionPointEvents(inputPoint);
        this.bindConnectionPointEvents(outputPoint);
    }

    /**
     * 绑定连接点事件
     */
    bindConnectionPointEvents(connectionPoint) {
        let isConnecting = false;
        let startPoint = null;

        connectionPoint.addEventListener('mousedown', (e) => {
            e.stopPropagation();

            if (connectionPoint.dataset.type === 'output') {
                // 开始连接
                isConnecting = true;
                startPoint = connectionPoint;
                this.startTempConnection(connectionPoint);
            }
        });

        connectionPoint.addEventListener('mouseup', (e) => {
            e.stopPropagation();

            if (isConnecting && connectionPoint.dataset.type === 'input' && startPoint) {
                // 完成连接
                this.completeTempConnection(startPoint, connectionPoint);
                isConnecting = false;
                startPoint = null;
            }
        });

        connectionPoint.addEventListener('mouseenter', (e) => {
            if (isConnecting && connectionPoint.dataset.type === 'input') {
                connectionPoint.style.background = '#10b981';
            }
        });

        connectionPoint.addEventListener('mouseleave', (e) => {
            if (connectionPoint.dataset.type === 'input') {
                connectionPoint.style.background = '#3b82f6';
            }
        });
    }

    /**
     * 开始临时连接
     */
    startTempConnection(startPoint) {
        const moduleElement = startPoint.closest('.module-node');
        const startPos = this.getModuleConnectionPoint(moduleElement, 'output');

        // 创建临时连接线
        this.tempConnection = document.createElementNS('http://www.w3.org/2000/svg', 'path');
        this.tempConnection.setAttribute('class', 'temp-connection');
        this.svg.appendChild(this.tempConnection);

        // 跟踪鼠标移动
        const handleMouseMove = (e) => {
            const containerRect = this.svg.getBoundingClientRect();
            const endPos = {
                x: e.clientX - containerRect.left,
                y: e.clientY - containerRect.top
            };

            const pathData = this.calculateBezierPath(startPos, endPos);
            this.tempConnection.setAttribute('d', pathData);
        };

        const handleMouseUp = () => {
            // 清理临时连接
            if (this.tempConnection) {
                this.tempConnection.remove();
                this.tempConnection = null;
            }
            document.removeEventListener('mousemove', handleMouseMove);
            document.removeEventListener('mouseup', handleMouseUp);
        };

        document.addEventListener('mousemove', handleMouseMove);
        document.addEventListener('mouseup', handleMouseUp);
    }

    /**
     * 完成临时连接
     */
    completeTempConnection(startPoint, endPoint) {
        const fromModuleId = startPoint.dataset.moduleId;
        const toModuleId = endPoint.dataset.moduleId;

        // 检查是否已存在连接
        const existingConnection = this.config.connections.find(conn =>
            conn.from === fromModuleId && conn.to === toModuleId
        );

        if (existingConnection) {
            this.app.showNotification('连接已存在', `${fromModuleId} -> ${toModuleId} 的连接已存在`, 'warning');
            return;
        }

        // 添加新连接
        this.addConnection(fromModuleId, toModuleId, 'data-flow', '新连接');

        this.app.showNotification('连接创建成功', `已创建 ${fromModuleId} -> ${toModuleId} 的连接`, 'success');
    }

    /**
     * 添加新连接线
     */
    addConnection(fromModuleId, toModuleId, type = 'data-flow', label = '') {
        const newConnection = {
            from: fromModuleId,
            to: toModuleId,
            type: type,
            label: label
        };
        
        // 添加到配置
        this.config.connections.push(newConnection);
        
        // 渲染新连接线
        const index = this.config.connections.length - 1;
        this.renderConnection(newConnection, index);
        
        console.log(`[ConnectionRenderer] 添加连接线: ${fromModuleId} -> ${toModuleId}`);
        this.app.emit('connection:added', { connection: newConnection, index });
    }

    /**
     * 删除连接线
     */
    deleteConnection(connectionId) {
        const connectionData = this.connections.get(connectionId);
        if (connectionData) {
            // 从DOM中移除
            connectionData.element.remove();
            
            // 从配置中移除
            const index = connectionData.index;
            this.config.connections.splice(index, 1);
            
            // 从映射中移除
            this.connections.delete(connectionId);
            
            console.log(`[ConnectionRenderer] 删除连接线: ${connectionId}`);
            this.app.emit('connection:deleted', { connectionId, index });
            
            // 重新渲染以更新索引
            this.render();
        }
    }
}

// 导出连接线渲染器
window.ConnectionRenderer = ConnectionRenderer;
