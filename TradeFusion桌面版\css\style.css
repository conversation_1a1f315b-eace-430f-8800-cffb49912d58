/**
 * TradeFusion桌面版 - 统一样式文件
 * 
 * 合并了main.css、modules.css、connections.css
 * 简化版本，专注于核心功能
 * 
 * <AUTHOR> Team
 * @version 2.0.0 (简化版)
 */

/* ===== 全局样式 ===== */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}



body {
    font-family: Inter, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 25%, #cbd5e1 50%, #94a3b8 100%);
    height: 100vh;
    overflow: hidden;
    position: relative;
    font-size: 16px; /* 基础字体大小增大40% (从默认14px到16px) */
}

body::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: 
        radial-gradient(circle at 20% 80%, rgba(120, 119, 198, 0.1) 0%, transparent 50%),
        radial-gradient(circle at 80% 20%, rgba(255, 119, 198, 0.1) 0%, transparent 50%),
        radial-gradient(circle at 40% 40%, rgba(120, 219, 255, 0.1) 0%, transparent 50%);
    pointer-events: none;
}

/* ===== 主容器布局 ===== */
.app-container {
    display: flex;
    height: 100vh;
    position: relative;
    z-index: 1;
}

/* ===== 左侧控制面板 ===== */
.control-panel {
    width: 350px; /* 增加宽度以适应更大的按钮 */
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-right: 1px solid rgba(0, 0, 0, 0.1);
    box-shadow: 2px 0 10px rgba(0, 0, 0, 0.1);
    display: flex;
    flex-direction: column;
    z-index: 10;
    overflow-y: auto; /* 添加垂直滚动 */
    max-height: 100vh; /* 限制最大高度 */
}

.panel-header {
    padding: 20px;
    border-bottom: 1px solid rgba(0, 0, 0, 0.1);
    background: linear-gradient(135deg, rgba(99, 102, 241, 0.1), rgba(168, 85, 247, 0.1));
}

.panel-title {
    font-size: 25px; /* 增大40%: 18px->25px */
    font-weight: 600;
    color: #1e293b;
    margin-bottom: 8px;
    display: flex;
    align-items: center;
    gap: 8px;
}

.panel-subtitle {
    font-size: 12px;
    color: #64748b;
    font-weight: 400;
}

/* ===== 统计信息 ===== */
.stats-section {
    padding: 20px;
    border-bottom: 1px solid rgba(0, 0, 0, 0.1);
}

.stats-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 12px;
}

.stat-item {
    text-align: center;
    padding: 12px;
    background: rgba(255, 255, 255, 0.7);
    border-radius: 8px;
    border: 1px solid rgba(0, 0, 0, 0.05);
}

.stat-number {
    font-size: 34px; /* 增大40%: 24px->34px */
    font-weight: 700;
    color: #1e293b;
    margin-bottom: 4px;
}

.stat-label {
    font-size: 15px; /* 增大40%: 11px->15px */
    color: #64748b;
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

/* ===== 控制按钮 ===== */
.controls-section {
    padding: 16px; /* 减少内边距 */
    border-bottom: 1px solid rgba(0, 0, 0, 0.1);
    box-sizing: border-box; /* 确保内边距包含在宽度内 */
}

.control-buttons {
    display: flex;
    flex-direction: column;
    gap: 8px;
    width: 100%; /* 确保容器占满宽度 */
}

.control-btn {
    padding: 14px 18px; /* 适中的内边距 */
    border: none;
    border-radius: 8px;
    font-size: 16px; /* 适中的字体大小 */
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    width: 100%; /* 确保按钮占满容器宽度 */
    box-sizing: border-box; /* 包含边框和内边距在宽度内 */
    text-align: center;
    white-space: nowrap; /* 防止文字换行 */
    overflow: hidden; /* 隐藏溢出内容 */
    text-overflow: ellipsis; /* 文字溢出显示省略号 */
}

.control-btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.btn-primary {
    background: linear-gradient(135deg, #3b82f6, #2563eb);
    color: white;
    box-shadow: 0 2px 4px rgba(59, 130, 246, 0.3);
}

.btn-secondary {
    background: linear-gradient(135deg, #6b7280, #4b5563);
    color: white;
    box-shadow: 0 2px 4px rgba(107, 114, 128, 0.3);
}

.btn-danger {
    background: linear-gradient(135deg, #ef4444, #dc2626);
    color: white;
    box-shadow: 0 2px 4px rgba(239, 68, 68, 0.3);
}

.btn-success {
    background: linear-gradient(135deg, #10b981, #059669);
    color: white;
    box-shadow: 0 2px 4px rgba(16, 185, 129, 0.3);
}

.btn-info {
    background: linear-gradient(135deg, #06b6d4, #0891b2);
    color: white;
    box-shadow: 0 2px 4px rgba(6, 182, 212, 0.3);
}

.btn-warning {
    background: linear-gradient(135deg, #f59e0b, #d97706);
    color: white;
    box-shadow: 0 2px 4px rgba(245, 158, 11, 0.3);
}

/* 位置管理相关样式 */
.section-title {
    font-size: 13px;
    font-weight: 600;
    color: #374151;
    margin-bottom: 8px;
    padding-left: 4px;
}

.position-info {
    padding: 6px 8px;
    background: rgba(59, 130, 246, 0.1);
    border-radius: 4px;
    border-left: 3px solid #3b82f6;
}

.control-btn.has-changes {
    position: relative;
    animation: pulse-save 2s infinite;
}

.control-btn.has-changes::after {
    content: '';
    position: absolute;
    top: -2px;
    right: -2px;
    width: 8px;
    height: 8px;
    background: #ef4444;
    border-radius: 50%;
    border: 2px solid white;
}

@keyframes pulse-save {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.02); }
}

/* ===== 模块日志样式 ===== */
.module-logs {
    margin-top: 12px;
    border-top: 1px solid rgba(0, 0, 0, 0.1);
    padding-top: 8px;
}

.logs-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 6px;
}

.logs-title {
    font-size: 11px;
    font-weight: 600;
    color: #374151;
}

.logs-toggle {
    background: none;
    border: none;
    cursor: pointer;
    padding: 2px 4px;
    border-radius: 3px;
    font-size: 10px;
    color: #6b7280;
    transition: all 0.2s ease;
}

.logs-toggle:hover {
    background: rgba(0, 0, 0, 0.05);
    color: #374151;
}

.logs-content {
    max-height: 120px;
    overflow-y: auto;
    background: rgba(0, 0, 0, 0.02);
    border-radius: 4px;
    padding: 4px;
    font-size: 10px;
    line-height: 1.3;
}

.logs-content::-webkit-scrollbar {
    width: 4px;
}

.logs-content::-webkit-scrollbar-track {
    background: transparent;
}

.logs-content::-webkit-scrollbar-thumb {
    background: rgba(0, 0, 0, 0.2);
    border-radius: 2px;
}

.log-item {
    display: flex;
    margin-bottom: 2px;
    padding: 2px 4px;
    border-radius: 2px;
    transition: background-color 0.2s ease;
}

.log-item:last-child {
    margin-bottom: 0;
}

.log-item.info {
    background: rgba(59, 130, 246, 0.05);
    border-left: 2px solid #3b82f6;
}

.log-item.success {
    background: rgba(16, 185, 129, 0.05);
    border-left: 2px solid #10b981;
}

.log-item.warning {
    background: rgba(245, 158, 11, 0.05);
    border-left: 2px solid #f59e0b;
}

.log-item.error {
    background: rgba(239, 68, 68, 0.05);
    border-left: 2px solid #ef4444;
}

.log-item.waiting {
    background: rgba(107, 114, 128, 0.05);
    border-left: 2px solid #6b7280;
    font-style: italic;
}

.log-time {
    color: #6b7280;
    font-family: 'Courier New', monospace;
    font-size: 9px;
    min-width: 50px;
    margin-right: 6px;
}

.log-message {
    color: #374151;
    flex: 1;
    word-break: break-word;
}

.log-item.error .log-message {
    color: #dc2626;
}

.log-item.success .log-message {
    color: #059669;
}

.log-item.warning .log-message {
    color: #d97706;
}

/* ===== 主画布区域 ===== */
.main-canvas {
    flex: 1;
    position: relative;
    overflow: hidden;
}

.canvas-viewport {
    width: 100%;
    height: 100%;
    position: relative;
    transform-origin: 0 0;
    transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* ===== SVG连接线层 ===== */
#flow-svg {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
    z-index: 1;
}

/* ===== 底部控制栏 ===== */
.bottom-controls {
    position: absolute;
    bottom: 20px;
    right: 20px;
    display: flex;
    gap: 8px;
    z-index: 20;
}

.zoom-btn {
    width: 40px;
    height: 40px;
    border: none;
    border-radius: 8px;
    background: rgba(255, 255, 255, 0.9);
    backdrop-filter: blur(10px);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 18px;
    font-weight: 600;
    color: #374151;
    transition: all 0.2s ease;
}

.zoom-btn:hover {
    background: rgba(255, 255, 255, 1);
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

/* ===== 加载和错误状态 ===== */
.loading-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(255, 255, 255, 0.9);
    backdrop-filter: blur(5px);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
}

.loading-spinner {
    width: 40px;
    height: 40px;
    border: 3px solid #e5e7eb;
    border-top: 3px solid #3b82f6;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.error-message {
    position: absolute;
    top: 20px;
    left: 50%;
    transform: translateX(-50%);
    background: linear-gradient(135deg, #fef2f2, #fee2e2);
    border: 1px solid #fca5a5;
    color: #dc2626;
    padding: 12px 20px;
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(220, 38, 38, 0.15);
    z-index: 1001;
    max-width: 500px;
    text-align: center;
}

/* ===== 通知系统 ===== */
.notification {
    position: fixed;
    top: 20px;
    right: 20px;
    max-width: 400px;
    background: white;
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    z-index: 1002;
    overflow: hidden;
    animation: slideIn 0.3s ease;
}

@keyframes slideIn {
    from { transform: translateX(100%); opacity: 0; }
    to { transform: translateX(0); opacity: 1; }
}

.notification-header {
    padding: 12px 16px;
    background: #f8fafc;
    border-bottom: 1px solid #e2e8f0;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.notification-close {
    background: none;
    border: none;
    font-size: 18px;
    cursor: pointer;
    color: #64748b;
}

.notification-body {
    padding: 16px;
    color: #374151;
    line-height: 1.5;
}

.notification-info {
    border-left: 4px solid #3b82f6;
}

.notification-error {
    border-left: 4px solid #ef4444;
}

.notification-success {
    border-left: 4px solid #10b981;
}

/* ===== 模块节点样式 ===== */
.module-node {
    position: absolute;
    width: 200px;
    min-height: 120px;
    background: linear-gradient(145deg, rgba(255, 255, 255, 0.95), rgba(248, 250, 252, 0.9));
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 16px;
    box-shadow:
        0 8px 32px rgba(0, 0, 0, 0.1),
        0 2px 8px rgba(0, 0, 0, 0.05),
        inset 0 1px 0 rgba(255, 255, 255, 0.3);
    cursor: move;
    transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1),
                box-shadow 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    z-index: 5;
    padding: 16px;
    display: flex;
    flex-direction: column;
    user-select: none;
    will-change: transform;
    overflow: hidden;
}

/* 模块类型特定样式 */
.module-node.data-collection {
    background: linear-gradient(145deg, rgba(0, 217, 255, 0.1), rgba(0, 180, 216, 0.05));
    border: 1px solid rgba(0, 217, 255, 0.3);
    box-shadow:
        0 8px 32px rgba(0, 217, 255, 0.15),
        0 2px 8px rgba(0, 180, 216, 0.1),
        inset 0 1px 0 rgba(255, 255, 255, 0.3);
}

.module-node.data-processing {
    background: linear-gradient(145deg, rgba(255, 107, 53, 0.1), rgba(247, 127, 0, 0.05));
    border: 1px solid rgba(255, 107, 53, 0.3);
    box-shadow:
        0 8px 32px rgba(255, 107, 53, 0.15),
        0 2px 8px rgba(247, 127, 0, 0.1),
        inset 0 1px 0 rgba(255, 255, 255, 0.3);
}

.module-node.analysis {
    background: linear-gradient(145deg, rgba(67, 97, 238, 0.1), rgba(63, 55, 201, 0.05));
    border: 1px solid rgba(67, 97, 238, 0.3);
    box-shadow:
        0 8px 32px rgba(67, 97, 238, 0.15),
        0 2px 8px rgba(63, 55, 201, 0.1),
        inset 0 1px 0 rgba(255, 255, 255, 0.3);
}

.module-node.scheduler {
    background: linear-gradient(145deg, rgba(139, 92, 246, 0.1), rgba(124, 58, 237, 0.05));
    border: 1px solid rgba(139, 92, 246, 0.3);
    box-shadow:
        0 8px 32px rgba(139, 92, 246, 0.15),
        0 2px 8px rgba(124, 58, 237, 0.1),
        inset 0 1px 0 rgba(255, 255, 255, 0.3);
}

.module-node.output {
    background: linear-gradient(145deg, rgba(34, 197, 94, 0.1), rgba(22, 163, 74, 0.05));
    border: 1px solid rgba(34, 197, 94, 0.3);
    box-shadow:
        0 8px 32px rgba(34, 197, 94, 0.15),
        0 2px 8px rgba(22, 163, 74, 0.1),
        inset 0 1px 0 rgba(255, 255, 255, 0.3);
}

.module-node.data-hub {
    background: linear-gradient(145deg, rgba(245, 158, 11, 0.1), rgba(217, 119, 6, 0.05));
    border: 1px solid rgba(245, 158, 11, 0.3);
    box-shadow:
        0 8px 32px rgba(245, 158, 11, 0.15),
        0 2px 8px rgba(217, 119, 6, 0.1),
        inset 0 1px 0 rgba(255, 255, 255, 0.3);
}

/* 旧的数据库样式已移除，使用新的现代化设计 */

.module-node:hover {
    transform: translateY(-4px) scale(1.02);
    box-shadow:
        0 12px 48px rgba(0, 0, 0, 0.15),
        0 4px 16px rgba(0, 0, 0, 0.1),
        inset 0 1px 0 rgba(255, 255, 255, 0.4);
    border-color: rgba(255, 255, 255, 0.3);
}

.module-node.dragging {
    transform: rotate(2deg) scale(1.05);
    box-shadow: 0 12px 32px rgba(0, 0, 0, 0.2);
    z-index: 100;
}

/* 性能优化：拖拽时禁用复杂效果 */
.module-node.dragging-optimized {
    transition: none !important;
    backdrop-filter: none !important;
    /* 简化阴影效果 */
    box-shadow: 0 6px 12px rgba(0, 0, 0, 0.15) !important; /* 对应缩小 */
    /* 保持基本的视觉反馈 */
    transform: scale(1.02) !important;
}

/* 旧的数据库特殊颜色样式已移除 */

/* 旧的数据库信息显示样式已移除 */

/* 旧的info样式已移除 */

.module-header {
    display: flex;
    align-items: center;
    margin-bottom: 8px;
}

/* 已删除模块图标样式 - 不再需要图标 */

.module-title {
    font-size: 20px; /* 增大40%: 14px->20px */
    font-weight: 600;
    color: #1e293b;
    line-height: 1.2;
    flex: 1;
}

.module-description {
    font-size: 17px; /* 增大40%: 12px->17px */
    color: #64748b;
    line-height: 1.4;
    margin-bottom: 12px;
    flex: 1;
}

.module-status {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 8px;
    gap: 8px;
}

.status-indicator {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    margin-right: 6px;
}

.status-text {
    font-size: 15px; /* 增大40%: 11px->15px */
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    display: flex;
    align-items: center;
}

.status-text.running {
    color: #059669;
    background: linear-gradient(135deg, rgba(34, 197, 94, 0.15), rgba(22, 163, 74, 0.1));
    padding: 6px 12px;
    border-radius: 8px;
    border: 1px solid rgba(34, 197, 94, 0.3);
    box-shadow: 0 2px 8px rgba(34, 197, 94, 0.1);
}

.status-text.active {
    color: #0891b2;
    background: linear-gradient(135deg, rgba(6, 182, 212, 0.15), rgba(8, 145, 178, 0.1));
    padding: 6px 12px;
    border-radius: 8px;
    border: 1px solid rgba(6, 182, 212, 0.3);
    box-shadow: 0 2px 8px rgba(6, 182, 212, 0.1);
}

.status-text.error {
    color: #dc2626;
    background: linear-gradient(135deg, rgba(248, 113, 113, 0.15), rgba(239, 68, 68, 0.1));
    padding: 6px 12px;
    border-radius: 8px;
    border: 1px solid rgba(248, 113, 113, 0.3);
    box-shadow: 0 2px 8px rgba(248, 113, 113, 0.1);
}

.status-text.idle {
    color: #6b7280;
    background: linear-gradient(135deg, rgba(107, 114, 128, 0.15), rgba(156, 163, 175, 0.1));
    padding: 6px 12px;
    border-radius: 8px;
    border: 1px solid rgba(156, 163, 175, 0.3);
    box-shadow: 0 2px 8px rgba(156, 163, 175, 0.1);
}

/* 状态区域内的按钮组 */
.module-status .module-btn {
    flex: 0 0 auto;
    margin-left: 4px;
}

.module-status .module-btn:first-of-type {
    margin-left: 8px;
}

.module-controls {
    display: flex;
    gap: 4px;
    margin-top: 0;
    margin-left: auto;
}

.module-btn {
    flex: 1;
    padding: 8px 12px;
    border: none;
    border-radius: 8px;
    font-size: 14px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    text-transform: uppercase;
    letter-spacing: 0.5px;
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.9), rgba(248, 250, 252, 0.8));
    backdrop-filter: blur(10px);
    box-shadow:
        0 2px 8px rgba(0, 0, 0, 0.1),
        inset 0 1px 0 rgba(255, 255, 255, 0.5);
    border: 1px solid rgba(255, 255, 255, 0.3);
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

/* 切换按钮状态样式 */
.btn-toggle.running {
    background: linear-gradient(135deg, #f87171, #ef4444);
    color: white;
    box-shadow: 0 2px 4px rgba(248, 113, 113, 0.3);
}

.btn-toggle.running:hover {
    background: linear-gradient(135deg, #ef4444, #dc2626);
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(248, 113, 113, 0.4);
}

.btn-toggle.stopped {
    background: linear-gradient(135deg, #10b981, #059669);
    color: white;
    box-shadow: 0 2px 4px rgba(16, 185, 129, 0.3);
}

.btn-toggle.stopped:hover {
    background: linear-gradient(135deg, #059669, #047857);
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(16, 185, 129, 0.4);
}

.btn-start {
    background: linear-gradient(135deg, #22C55E, #16A34A);
    color: white;
    box-shadow:
        0 4px 12px rgba(34, 197, 94, 0.3),
        inset 0 1px 0 rgba(255, 255, 255, 0.2);
    border: 1px solid rgba(34, 197, 94, 0.4);
}

.btn-start:hover {
    background: linear-gradient(135deg, #16A34A, #15803D);
    transform: translateY(-2px) scale(1.05);
    box-shadow:
        0 6px 20px rgba(34, 197, 94, 0.4),
        inset 0 1px 0 rgba(255, 255, 255, 0.3);
}

.btn-stop {
    background: linear-gradient(135deg, #f87171, #ef4444);
    color: white;
    box-shadow: 0 2px 4px rgba(248, 113, 113, 0.3);
}

.btn-stop:hover {
    background: linear-gradient(135deg, #ef4444, #dc2626);
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(248, 113, 113, 0.4);
}

.btn-config {
    background: linear-gradient(135deg, #8B5CF6, #7C3AED);
    color: white;
    box-shadow:
        0 4px 12px rgba(139, 92, 246, 0.3),
        inset 0 1px 0 rgba(255, 255, 255, 0.2);
    border: 1px solid rgba(139, 92, 246, 0.4);
}

.btn-config:hover {
    background: linear-gradient(135deg, #7C3AED, #6D28D9);
    transform: translateY(-2px) scale(1.05);
    box-shadow:
        0 6px 20px rgba(139, 92, 246, 0.4),
        inset 0 1px 0 rgba(255, 255, 255, 0.3);
}

.btn-status {
    background: linear-gradient(135deg, #6b7280, #4b5563);
    color: white;
    box-shadow: 0 2px 4px rgba(107, 114, 128, 0.3);
    cursor: default;
}

.btn-status:disabled {
    opacity: 0.8;
    cursor: default;
}

/* 已删除重复的模块图标样式 - 不再需要图标 */

/* ===== 响应式设计 ===== */
@media (max-width: 768px) {
    .control-panel {
        width: 250px;
    }

    .stats-grid {
        grid-template-columns: 1fr;
    }

    .panel-title {
        font-size: 16px;
    }

    .module-node {
        width: 200px;
        min-height: 70px;
        padding: 12px;
    }

    /* 已删除响应式图标样式 - 不再需要图标 */

    .module-title {
        font-size: 13px;
    }

    .module-description {
        font-size: 11px;
    }
}

/* ===== 控制面板响应式设计 ===== */
@media (max-width: 1200px) {
    .control-panel {
        width: 320px;
    }

    .control-btn {
        font-size: 14px;
        padding: 12px 16px;
    }

    .panel-title {
        font-size: 22px;
    }

    .stat-number {
        font-size: 28px;
    }
}

@media (max-width: 900px) {
    .control-panel {
        width: 280px;
    }

    .control-btn {
        font-size: 13px;
        padding: 10px 14px;
    }

    .panel-title {
        font-size: 20px;
    }

    .controls-section {
        padding: 12px;
    }
}

/* 确保控制面板在小屏幕上可滚动 */
@media (max-height: 600px) {
    .control-panel {
        overflow-y: auto;
    }
}

/* ===== 简洁的数据库模块 ===== */
.module-node[data-type="database"] {
    border-radius: 12px;
    background: #f8fafc;
    border: 1px solid #e2e8f0;
    position: relative;
    overflow: hidden;
    min-width: 160px;
    min-height: 120px;
    max-width: 400px;
    max-height: 300px;
}

/* 移除所有装饰元素 */

/* 美观的数据库模块内容 */
.module-node[data-type="database"] .module-header {
    background: transparent;
    border: none;
    color: #334155;
    padding: 24px 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    height: 100%;
    box-sizing: border-box;
    position: relative;
    z-index: 3;
}

.module-node[data-type="database"] .module-title {
    font-weight: 600;
    font-size: 15px;
    text-align: center;
    color: #1e293b;
    line-height: 1.4;
    word-break: break-word;
    margin: 0;
    text-shadow: 0 1px 0 rgba(255, 255, 255, 0.8);
}

.module-node[data-type="database"] .module-description {
    display: none; /* 隐藏描述，只显示表名称 */
}

.module-node[data-type="database"] .module-status {
    background: rgba(255, 255, 255, 0.2);
    color: white;
    border: 1px solid rgba(255, 255, 255, 0.3);
}

.module-node[data-type="database"] .module-logs {
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    color: white;
}

/* 数据库模块的调整大小功能已合并到主样式中 */

/* 隐藏拖拽手柄 */
.module-node[data-type="database"] .resize-handle {
    position: absolute;
    background: transparent;
    border: none;
    z-index: 10;
}

.module-node[data-type="database"] .resize-handle.right {
    top: 0;
    right: 0;
    width: 8px;
    height: 100%;
    cursor: e-resize;
}

.module-node[data-type="database"] .resize-handle.bottom {
    bottom: 0;
    left: 0;
    width: 100%;
    height: 8px;
    cursor: s-resize;
}

.module-node[data-type="database"] .resize-handle.corner {
    bottom: 0;
    right: 0;
    width: 12px;
    height: 12px;
    cursor: se-resize;
    background: transparent;
    border: none;
}

/* 移除调整大小反馈 */

/* ===== 连接线样式 ===== */
#flow-svg {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
    z-index: 1;
    overflow: visible;
}

.connection-group {
    cursor: pointer;
    transition: all 0.2s ease;
}

.connection-group:hover {
    filter: drop-shadow(0 2px 4px rgba(0,0,0,0.2));
}

.connection-group.selected {
    filter: drop-shadow(0 4px 8px rgba(0,0,0,0.3));
}

.connection-label {
    font-family: Inter, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    font-size: 11px;
    fill: #64748b;
    text-anchor: middle;
    pointer-events: none;
    user-select: none;
}

/* 连接线类型样式 */
.connection-group[data-type="rq-flow"] path {
    stroke: #8b5cf6;
    stroke-width: 2;
}

.connection-group[data-type="xgb-flow"] path {
    stroke: #f97316;
    stroke-width: 2;
}

.connection-group[data-type="database-flow"] path {
    stroke: #10b981;
    stroke-width: 2;
}

.connection-group[data-type="data-flow"] path {
    stroke: #06b6d4;
    stroke-width: 2;
    stroke-dasharray: 5,5;
}

/* 编辑模式样式 */
.connection-edit-mode #flow-svg {
    pointer-events: auto;
}

.connection-edit-mode .connection-group {
    pointer-events: auto;
}

.connection-edit-mode .connection-group:hover path {
    stroke-width: 3;
    filter: drop-shadow(0 2px 4px rgba(0,0,0,0.3));
}

/* 连接点样式 */
.module-connection-point {
    position: absolute;
    width: 8px;
    height: 8px;
    background: #3b82f6;
    border: 2px solid #ffffff;
    border-radius: 50%;
    transform: translate(-50%, -50%);
    opacity: 0;
    transition: opacity 0.2s ease;
    z-index: 10;
    pointer-events: auto;
    cursor: crosshair;
}

.connection-edit-mode .module-connection-point {
    opacity: 1;
}

.module-connection-point:hover {
    background: #1d4ed8;
    transform: translate(-50%, -50%) scale(1.2);
}

.module-connection-point.input {
    left: 0;
    top: 50%;
}

.module-connection-point.output {
    right: 0;
    top: 50%;
    left: auto;
    transform: translate(50%, -50%);
}

/* 临时连接线样式 */
.temp-connection {
    stroke: #94a3b8;
    stroke-width: 2;
    stroke-dasharray: 3,3;
    fill: none;
    pointer-events: none;
}

/* 连接线控制按钮样式 */
.connection-controls {
    position: absolute;
    background: rgba(255, 255, 255, 0.95);
    border: 1px solid #e2e8f0;
    border-radius: 8px;
    padding: 8px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    z-index: 1000;
    display: none;
}

.connection-controls.show {
    display: block;
}

.connection-control-btn {
    background: #f8fafc;
    border: 1px solid #e2e8f0;
    border-radius: 4px;
    padding: 4px 8px;
    margin: 2px;
    font-size: 11px;
    cursor: pointer;
    transition: all 0.2s ease;
}

.connection-control-btn:hover {
    background: #e2e8f0;
    border-color: #cbd5e1;
}

.connection-control-btn.delete {
    background: #fef2f2;
    border-color: #fecaca;
    color: #dc2626;
}

.connection-control-btn.delete:hover {
    background: #fee2e2;
    border-color: #fca5a5;
}
