# TradeFusion桌面版 - 工作流程规范

> **目标**：避免历史遗留问题，建立标准化的开发和测试流程  
> **创建时间**：2025-01-10  
> **适用范围**：所有TradeFusion桌面版的开发、测试、维护工作  

## 🚨 **核心问题总结**

### **历史遗留问题案例**
- **问题**：浏览器保持旧版本alert()对话框状态，污染新版本测试
- **根源**：缺乏环境状态管理，版本文件混乱
- **教训**：环境因素比代码问题更容易被忽视，但影响更大

## 📋 **标准工作流程**

### **1. 环境管理流程**

#### **1.1 工作目录结构标准**
```
TradeFusion桌面版/
├── index.html              # 当前生产版本（唯一入口）
├── config.js               # 当前配置文件
├── js/                     # 当前JS模块
├── css/                    # 当前样式文件
├── docs/                   # 所有文档
├── archive/                # 历史版本存档
│   ├── v1.0/              # 版本1.0存档
│   └── v2.0/              # 版本2.0存档
└── 打开桌面版.bat          # 启动脚本
```

#### **1.2 文件命名规范**
- **生产文件**：使用标准名称（index.html, config.js）
- **备份文件**：移动到archive/目录，按版本组织
- **测试文件**：使用test_前缀，测试完成后删除
- **临时文件**：使用temp_前缀，及时清理

#### **1.3 版本管理规则**
- **主版本升级**：创建新的archive/vX.X/目录存档
- **小版本修改**：在当前版本基础上修改，保留一个备份
- **实验性修改**：使用test_前缀，不影响生产版本

### **2. 测试前准备流程（SOP）**

#### **2.1 环境检查清单**
- [ ] 确认当前工作目录只有一个index.html
- [ ] 确认配置文件是否为最新版本
- [ ] 检查是否有冗余的备份文件
- [ ] 验证所有依赖文件路径正确

#### **2.2 浏览器状态清理**
- [ ] 关闭所有浏览器窗口和标签页
- [ ] 清除浏览器缓存（Ctrl+Shift+Delete）
- [ ] 清除浏览器应用数据（如果需要）
- [ ] 重新启动浏览器

#### **2.3 版本确认机制**
- [ ] 检查HTML文件的title标签确认版本
- [ ] 检查控制台输出确认加载的文件
- [ ] 验证功能特性确认版本正确性

### **3. 问题诊断流程（SOP）**

#### **3.1 问题分类决策树**
```
发现问题
├── 是否为新功能/新代码？
│   ├── 是 → 检查代码逻辑
│   └── 否 → 继续下一步
├── 是否在不同环境下表现不同？
│   ├── 是 → 环境问题
│   └── 否 → 继续下一步
├── 是否有历史版本文件存在？
│   ├── 是 → 历史遗留问题
│   └── 否 → 代码问题
```

#### **3.2 环境因素排查优先级**
1. **浏览器状态**：对话框、缓存、会话状态
2. **文件版本**：确认加载的是正确版本
3. **路径引用**：检查相对路径和绝对路径
4. **配置文件**：确认使用的配置文件
5. **依赖文件**：检查CSS、JS文件是否正确

#### **3.3 历史遗留问题检查**
- [ ] 检查工作目录中的旧版本文件
- [ ] 检查浏览器是否保持旧状态
- [ ] 检查配置文件是否有冲突
- [ ] 检查模块文件是否有冗余

### **4. 代码修改流程（SOP）**

#### **4.1 修改前准备**
- [ ] 确认问题确实在代码中（完成问题诊断）
- [ ] 备份当前工作版本到archive/
- [ ] 记录修改原因和预期效果
- [ ] 制定回滚计划

#### **4.2 修改过程**
- [ ] 只修改必要的文件
- [ ] 保持修改的最小化
- [ ] 添加适当的注释说明
- [ ] 更新相关文档

#### **4.3 修改后验证**
- [ ] 清理浏览器状态
- [ ] 重新测试修改的功能
- [ ] 验证未修改的功能正常
- [ ] 更新版本标记

### **5. 预防措施**

#### **5.1 定期清理**
- **每周**：清理临时文件和测试文件
- **每月**：整理archive/目录
- **每次重大修改前**：执行完整的环境清理

#### **5.2 标准检查点**
- **开始工作前**：检查工作目录状态
- **测试前**：执行测试准备流程
- **遇到问题时**：执行问题诊断流程
- **修改代码前**：确认问题根源

#### **5.3 文档维护**
- **实时更新**：工作流程规范文档
- **记录问题**：历史遗留问题案例
- **总结经验**：成功的解决方案

## ⚠️ **重要提醒**

### **核心原则**
1. **环境优先**：先排查环境问题，再考虑代码问题
2. **状态隔离**：确保测试环境的纯净性
3. **版本单一**：工作目录中只保留当前版本
4. **流程标准**：严格按照SOP执行

### **常见陷阱**
- ❌ 遇到问题直接修改代码
- ❌ 忽视浏览器状态污染
- ❌ 多版本文件共存
- ❌ 跳过环境检查步骤

### **成功标志**
- ✅ 问题能够快速定位根源
- ✅ 修改能够一次性解决问题
- ✅ 测试结果稳定可重现
- ✅ 工作目录保持整洁

---

**📝 使用说明**：
- 每次开始工作前，先阅读本规范
- 遇到问题时，严格按照诊断流程执行
- 定期回顾和更新本规范内容
- 将成功案例和失败教训记录到本文档
