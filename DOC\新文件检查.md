# 新文件检查规范

> **⚠️ 核心原则**
> 模块已稳定运行，**仅检查迁移适配问题**：路径、依赖、引用。
> **不检查**：业务逻辑、算法正确性、功能完整性。

## 检查内容

### ✅ 检查项

- **路径处理**：文件位置、导入路径、配置路径、日志路径统一
- **依赖完整性**：缺失模块识别和安装
- **引用更新**：模块间调用关系修复
- **文档同步**：项目结构文档更新

## 操作步骤

### 1. 路径处理

- **文件位置**：确认目标存放路径，清理缓存文件
- **更新文档**：在项目结构.md中添加文件标记
- **配置路径**：修正配置文件路径和环境变量
- **日志路径**：统一到logs/目录，修正硬编码路径

### 2. 依赖处理

- **识别依赖**：检查import语句和第三方库
- **安装依赖**：`pip install <模块名>`，验证导入成功

### 3. 引用更新

- **检查变更表**：查看文件名称变更表.md中的上下游影响
- **修改引用**：更新所有相关文件中的导入和调用
- **文档记录**：更新变更表记录

## 验证清单

**路径处理**：

- [ ] 文件位置正确，项目结构文档已更新
- [ ] 配置路径已修正，日志路径统一到logs/目录
- [ ] 硬编码路径已修正为相对路径

**依赖处理**：

- [ ] 依赖已安装并能成功导入

**引用更新**：

- [ ] 检查变更表，更新相关引用
- [ ] 变更记录已更新
