#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
TradeFusion选股宝完整流程测试脚本
功能：测试选股宝从抓取到数据库导入的完整流程
作者：TradeFusion团队
创建时间：2025-07-12
"""

import sys
import os
import time
import logging
from pathlib import Path

# 添加项目根目录到路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def 测试选股宝完整流程():
    """测试选股宝完整流程：抓取 → 清洗 → 数据库导入"""
    try:
        logger.info("开始测试选股宝完整流程...")
        
        # 模拟抓取数据（避免实际网络请求）
        from 数据库1_基础表模块.人气临时表管理 import 获取临时表管理器
        
        # 准备模拟的抓取数据
        mock_raw_data = """测试板块,+3.5%
理由,这是一个测试板块的消息内容
股票名称
平安银行
000001.SZ
123.45
+5.67%
8.9%
100亿
这是平安银行的测试解读内容
万科A
000002.SZ
56.78
+2.34%
4.5%
200亿
这是万科A的测试解读内容
五粮液
000858.SZ
180.50
+1.23%
2.1%
300亿
这是五粮液的测试解读内容"""
        
        # 1. 模拟抓取步骤：写入原始数据
        logger.info("步骤1: 模拟选股宝数据抓取...")
        temp_manager = 获取临时表管理器()
        temp_manager.写入选股宝原始数据(mock_raw_data)
        temp_manager.close()
        logger.info("✅ 抓取步骤完成")
        
        # 2. 数据清洗步骤
        logger.info("步骤2: 选股宝数据清洗...")
        from 数据3_网络采集_bk.选股宝清洗 import run_cleaner_from_temp_table
        cleaned_count = run_cleaner_from_temp_table()
        
        if cleaned_count is None or cleaned_count == 0:
            raise RuntimeError("数据清洗失败或无数据")
        
        logger.info(f"✅ 清洗步骤完成，处理了{cleaned_count}条记录")
        
        # 3. 数据库导入步骤
        logger.info("步骤3: 数据库导入...")
        from 数据3_网络采集_bk.个股解读_板块信息_关联表 import main_from_temp_table
        import_result = main_from_temp_table()
        
        if not import_result:
            raise RuntimeError("数据库导入失败")
        
        logger.info("✅ 数据库导入步骤完成")
        
        # 4. 验证数据
        logger.info("步骤4: 验证导入的数据...")
        import sqlite3
        from pathlib import Path
        
        db_path = Path.cwd() / "数据库0_实体模块/股票数据.db"
        conn = sqlite3.connect(str(db_path))
        
        # 检查个股解读表
        cursor = conn.execute("SELECT COUNT(*) FROM 个股解读表")
        stock_count = cursor.fetchone()[0]
        
        # 检查板块信息表
        cursor = conn.execute("SELECT COUNT(*) FROM 板块信息表")
        sector_count = cursor.fetchone()[0]
        
        # 检查关联表
        cursor = conn.execute("SELECT COUNT(*) FROM 个股板块关联表")
        relation_count = cursor.fetchone()[0]
        
        conn.close()
        
        logger.info(f"数据验证结果:")
        logger.info(f"  个股解读表: {stock_count} 条记录")
        logger.info(f"  板块信息表: {sector_count} 条记录")
        logger.info(f"  个股板块关联表: {relation_count} 条记录")
        
        # 验证数据合理性
        assert stock_count > 0, "个股解读表无数据"
        assert sector_count > 0, "板块信息表无数据"
        assert relation_count > 0, "个股板块关联表无数据"
        
        logger.info("✅ 数据验证通过")
        
        logger.info("🎉 选股宝完整流程测试成功！")
        return True
        
    except Exception as e:
        logger.error(f"❌ 选股宝完整流程测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def 测试性能对比():
    """测试临时表方案与CSV方案的性能对比"""
    try:
        logger.info("开始性能对比测试...")
        
        # 准备测试数据
        test_data = []
        for i in range(50):
            date = 20250712
            code = f"{i:06d}"
            sector = f"测试板块{i%5}"
            change = f"+{i%3}.{i%10}%"
            message = f"测试消息{i}"
            description = f"测试解读{i}"
            test_data.append((date, code, sector, change, message, description))
        
        # 测试临时表方案性能
        from 数据库1_基础表模块.人气临时表管理 import 获取临时表管理器
        
        start_time = time.time()
        
        # 写入 → 读取 → 处理 → 标记
        manager = 获取临时表管理器()
        manager.写入选股宝清洗数据(test_data)
        data = manager.读取选股宝清洗待处理数据()
        manager.标记选股宝清洗数据已处理()
        manager.close()
        
        temp_table_time = time.time() - start_time
        
        logger.info(f"性能对比结果:")
        logger.info(f"  临时表方案处理50条数据: {temp_table_time:.3f}秒")
        
        # 性能要求
        assert temp_table_time < 0.5, f"临时表方案性能不达标: {temp_table_time:.3f}秒"
        
        logger.info("✅ 性能对比测试通过")
        return True
        
    except Exception as e:
        logger.error(f"❌ 性能对比测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    logger.info("="*60)
    logger.info("TradeFusion选股宝完整流程测试")
    logger.info("="*60)
    
    test_results = []
    
    # 执行各项测试
    tests = [
        ("选股宝完整流程", 测试选股宝完整流程),
        ("性能对比", 测试性能对比)
    ]
    
    for test_name, test_func in tests:
        logger.info(f"\n开始执行: {test_name}")
        result = test_func()
        test_results.append((test_name, result))
        
        if result:
            logger.info(f"✅ {test_name} 测试通过")
        else:
            logger.error(f"❌ {test_name} 测试失败")
    
    # 汇总测试结果
    logger.info("\n" + "="*60)
    logger.info("测试结果汇总")
    logger.info("="*60)
    
    passed = 0
    total = len(test_results)
    
    for test_name, result in test_results:
        status = "✅ 通过" if result else "❌ 失败"
        logger.info(f"{test_name}: {status}")
        if result:
            passed += 1
    
    logger.info(f"\n总计: {passed}/{total} 项测试通过")
    
    if passed == total:
        logger.info("🎉 所有测试通过！选股宝临时表方案完全可用。")
        return True
    else:
        logger.error("⚠️ 部分测试失败，请检查问题后重新测试。")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
