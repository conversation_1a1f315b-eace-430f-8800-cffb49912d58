/**
 * DOM工具模块
 * 
 * 功能：提供通用的DOM操作工具函数
 * 职责：统一DOM操作，减少代码重复
 * 
 * <AUTHOR> Team
 * @version 2.0.0
 */

class DOMUtils {
    /**
     * 创建SVG元素
     */
    static createSVGElement(tagName, attributes = {}) {
        const element = document.createElementNS('http://www.w3.org/2000/svg', tagName);
        Object.entries(attributes).forEach(([key, value]) => {
            element.setAttribute(key, value);
        });
        return element;
    }

    /**
     * 创建HTML元素
     */
    static createElement(tagName, attributes = {}, textContent = '') {
        const element = document.createElement(tagName);
        Object.entries(attributes).forEach(([key, value]) => {
            if (key === 'className') {
                element.className = value;
            } else if (key === 'dataset') {
                Object.entries(value).forEach(([dataKey, dataValue]) => {
                    element.dataset[dataKey] = dataValue;
                });
            } else {
                element.setAttribute(key, value);
            }
        });
        if (textContent) {
            element.textContent = textContent;
        }
        return element;
    }

    /**
     * 获取元素的绝对位置
     */
    static getElementPosition(element, relativeTo = null) {
        const rect = element.getBoundingClientRect();
        const relativeRect = relativeTo ? relativeTo.getBoundingClientRect() : { left: 0, top: 0 };
        
        return {
            x: rect.left - relativeRect.left,
            y: rect.top - relativeRect.top,
            width: rect.width,
            height: rect.height,
            centerX: rect.left - relativeRect.left + rect.width / 2,
            centerY: rect.top - relativeRect.top + rect.height / 2
        };
    }

    /**
     * 获取元素的连接点位置
     */
    static getConnectionPoint(element, direction, relativeTo = null) {
        const pos = this.getElementPosition(element, relativeTo);
        
        switch (direction) {
            case 'output':
            case 'right':
                return { x: pos.x + pos.width, y: pos.centerY };
            case 'input':
            case 'left':
                return { x: pos.x, y: pos.centerY };
            case 'top':
                return { x: pos.centerX, y: pos.y };
            case 'bottom':
                return { x: pos.centerX, y: pos.y + pos.height };
            default:
                return { x: pos.centerX, y: pos.centerY };
        }
    }

    /**
     * 添加CSS类
     */
    static addClass(element, className) {
        if (element && className) {
            element.classList.add(className);
        }
    }

    /**
     * 移除CSS类
     */
    static removeClass(element, className) {
        if (element && className) {
            element.classList.remove(className);
        }
    }

    /**
     * 切换CSS类
     */
    static toggleClass(element, className, force = null) {
        if (element && className) {
            return element.classList.toggle(className, force);
        }
        return false;
    }

    /**
     * 检查元素是否包含CSS类
     */
    static hasClass(element, className) {
        return element && className && element.classList.contains(className);
    }

    /**
     * 设置元素样式
     */
    static setStyle(element, styles) {
        if (element && styles) {
            Object.entries(styles).forEach(([property, value]) => {
                element.style[property] = value;
            });
        }
    }

    /**
     * 获取元素样式
     */
    static getStyle(element, property) {
        if (element && property) {
            return window.getComputedStyle(element)[property];
        }
        return null;
    }

    /**
     * 查找最近的父元素
     */
    static closest(element, selector) {
        if (!element || !selector) return null;
        return element.closest(selector);
    }

    /**
     * 查找子元素
     */
    static find(element, selector) {
        if (!element || !selector) return null;
        return element.querySelector(selector);
    }

    /**
     * 查找所有子元素
     */
    static findAll(element, selector) {
        if (!element || !selector) return [];
        return Array.from(element.querySelectorAll(selector));
    }

    /**
     * 移除元素
     */
    static remove(element) {
        if (element && element.parentNode) {
            element.parentNode.removeChild(element);
        }
    }

    /**
     * 清空元素内容
     */
    static empty(element) {
        if (element) {
            element.innerHTML = '';
        }
    }

    /**
     * 显示元素
     */
    static show(element, display = 'block') {
        if (element) {
            element.style.display = display;
        }
    }

    /**
     * 隐藏元素
     */
    static hide(element) {
        if (element) {
            element.style.display = 'none';
        }
    }

    /**
     * 切换元素显示/隐藏
     */
    static toggle(element, display = 'block') {
        if (element) {
            const isHidden = element.style.display === 'none' || 
                           window.getComputedStyle(element).display === 'none';
            element.style.display = isHidden ? display : 'none';
            return !isHidden;
        }
        return false;
    }

    /**
     * 检查元素是否可见
     */
    static isVisible(element) {
        if (!element) return false;
        const style = window.getComputedStyle(element);
        return style.display !== 'none' && 
               style.visibility !== 'hidden' && 
               style.opacity !== '0';
    }

    /**
     * 获取元素的文本内容
     */
    static getText(element) {
        return element ? element.textContent || element.innerText || '' : '';
    }

    /**
     * 设置元素的文本内容
     */
    static setText(element, text) {
        if (element) {
            element.textContent = text;
        }
    }

    /**
     * 获取元素的HTML内容
     */
    static getHTML(element) {
        return element ? element.innerHTML : '';
    }

    /**
     * 设置元素的HTML内容
     */
    static setHTML(element, html) {
        if (element) {
            element.innerHTML = html;
        }
    }

    /**
     * 获取或设置元素属性
     */
    static attr(element, name, value = undefined) {
        if (!element || !name) return null;
        
        if (value === undefined) {
            return element.getAttribute(name);
        } else {
            element.setAttribute(name, value);
            return element;
        }
    }

    /**
     * 移除元素属性
     */
    static removeAttr(element, name) {
        if (element && name) {
            element.removeAttribute(name);
        }
    }

    /**
     * 获取或设置元素数据属性
     */
    static data(element, key, value = undefined) {
        if (!element || !key) return null;
        
        if (value === undefined) {
            return element.dataset[key];
        } else {
            element.dataset[key] = value;
            return element;
        }
    }

    /**
     * 在元素后插入新元素
     */
    static insertAfter(newElement, targetElement) {
        if (newElement && targetElement && targetElement.parentNode) {
            targetElement.parentNode.insertBefore(newElement, targetElement.nextSibling);
        }
    }

    /**
     * 在元素前插入新元素
     */
    static insertBefore(newElement, targetElement) {
        if (newElement && targetElement && targetElement.parentNode) {
            targetElement.parentNode.insertBefore(newElement, targetElement);
        }
    }

    /**
     * 将元素添加到父元素
     */
    static append(parent, child) {
        if (parent && child) {
            parent.appendChild(child);
        }
    }

    /**
     * 将元素添加到父元素开头
     */
    static prepend(parent, child) {
        if (parent && child) {
            parent.insertBefore(child, parent.firstChild);
        }
    }

    /**
     * 检查元素是否包含另一个元素
     */
    static contains(parent, child) {
        if (!parent || !child) return false;
        return parent.contains(child);
    }

    /**
     * 获取元素的索引位置
     */
    static index(element) {
        if (!element || !element.parentNode) return -1;
        return Array.from(element.parentNode.children).indexOf(element);
    }

    /**
     * 克隆元素
     */
    static clone(element, deep = true) {
        return element ? element.cloneNode(deep) : null;
    }
}

// 导出DOM工具类
window.DOMUtils = DOMUtils;
