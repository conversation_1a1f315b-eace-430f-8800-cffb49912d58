# TradeFusion业务模块执行序列列表

## 📋 模块执行顺序（按数据流依赖关系排序）

### 🔵 第1批：数据采集层（并行执行）
1. **A1: 采集_本地数据.py**
   - 执行频率：每12秒执行
   - 输出表：个股连板高度表
   - 下游调用：B3(板块涨停表.py)

2. **A2: 人气_东财采集.py**
   - 执行频率：每10分钟执行
   - 输出表：临时表_东财人气
   - 下游调用：B2(个股人气表.py)

3. **A3: 人气_同花采集.py**
   - 执行频率：每5分钟执行
   - 输出表：临时表_同花人气
   - 下游调用：B2(个股人气表.py)

4. **A4: 选股宝抓取.py**
   - 执行频率：手动执行
   - 输出表：临时表_选股宝原始
   - 下游调用：B1(选股宝清洗.py)

### 🟣 第2批：数据处理层（依赖第1批）
5. **B1: 选股宝清洗.py**
   - 触发条件：由A4调用
   - 输出表：临时表_选股宝清洗
   - 下游调用：C1(个股解读_板块信息_关联表.py)

6. **B2: 个股人气表.py**
   - 触发条件：由A2+A3调用
   - 输出表：个股人气表
   - 下游调用：D1(综合人气190.py) + D2(生成DZH3人气板块.py)

7. **B3: 板块涨停表.py**
   - 触发条件：由A1调用
   - 输出表：板块涨停表
   - 下游调用：C2(A3_所属板块评分表.py)

### 🟢 第3批：数据加工层第一阶段（依赖第2批）
8. **C1: 个股解读_板块信息_关联表.py**
   - 触发条件：由B1调用
   - 输出表：个股解读表 + 板块信息表 + 个股板块关联表
   - 下游调用：D5(选股宝_大智慧str.py)

9. **C2: A3_所属板块评分表.py**
   - 触发条件：由B3调用
   - 输出表：所属板块评分表
   - 下游调用：C3(板块精选.py) + D4(A4_所属板块强度.py)

### 🟢 第4批：数据加工层第二阶段（依赖C2）
10. **C3: 板块精选.py**
    - 触发条件：由C2调用
    - 输出表：板块精选表
    - 下游调用：C4(个股接力表.py)

### 🟢 第5批：数据加工层第三阶段（依赖C3）
11. **C4: 个股接力表.py**
    - 触发条件：由C3调用
    - 输出表：个股接力表
    - 下游调用：D3(接力.py)

### 🟠 第6批：输出应用层第一阶段（依赖各自上游）
12. **D1: 综合人气190.py**
    - 触发条件：由B2调用
    - 输出文件：190人气.dat

13. **D2: 生成DZH3人气板块.py**
    - 触发条件：由B2调用
    - 输出文件：自选股人气.BLK

14. **D4: A4_所属板块强度.py**
    - 触发条件：由C2调用
    - 输出文件：所属板块强度.dat

15. **D5: 选股宝_大智慧str.py**
    - 触发条件：由C1调用
    - 输出文件：选股宝STR文件

### 🟠 第7批：输出应用层第二阶段（依赖C4）
16. **D3: 接力.py**
    - 触发条件：由C4调用
    - 输出文件：接力.dat

## 🔄 主要数据流路径

### 路径1：选股宝数据流
```
A4 → B1 → C1 → D5
```

### 路径2：人气数据流
```
A2 + A3 → B2 → D1 + D2
```

### 路径3：板块涨停数据流
```
A1 → B3 → C2 → C3 → C4 → D3
              ↓
             D4
```

## 📊 统计信息
- **总模块数**：16个
- **数据采集层**：4个模块
- **数据处理层**：3个模块  
- **数据加工层**：4个模块
- **输出应用层**：5个模块
- **主要数据流路径**：3条
- **最长执行链**：7个模块（A1→B3→C2→C3→C4→D3）
