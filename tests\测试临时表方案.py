#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
TradeFusion临时表方案测试脚本
功能：测试临时表数据流通方案的完整性和可靠性
作者：TradeFusion团队
创建时间：2025-07-12
"""

import sys
import os
import time
import logging
from pathlib import Path

# 添加项目根目录到路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def 测试临时表管理器():
    """测试临时表管理器的基本功能"""
    try:
        logger.info("开始测试临时表管理器...")
        
        from 数据2_网络采集.人气临时表管理 import 获取临时表管理器
        
        # 创建管理器实例
        manager = 获取临时表管理器()
        
        # 测试数据
        test_dcf_data = [
            ("000001", "平安银行", 1),
            ("000002", "万科A", 2),
            ("000858", "五粮液", 3)
        ]
        
        test_ths_data = [
            ("000001", "平安银行", 5),
            ("000002", "万科A", 8),
            ("600036", "招商银行", 10)
        ]
        
        # 测试写入数据
        logger.info("测试写入东财数据...")
        dcf_count = manager.写入东财数据(test_dcf_data)
        assert dcf_count == len(test_dcf_data), "东财数据写入数量不匹配"
        
        logger.info("测试写入同花数据...")
        ths_count = manager.写入同花数据(test_ths_data)
        assert ths_count == len(test_ths_data), "同花数据写入数量不匹配"
        
        # 测试读取数据
        logger.info("测试读取待处理数据...")
        dcf_pending = manager.读取东财待处理数据()
        ths_pending = manager.读取同花待处理数据()
        
        assert len(dcf_pending) == len(test_dcf_data), "东财待处理数据数量不匹配"
        assert len(ths_pending) == len(test_ths_data), "同花待处理数据数量不匹配"
        
        # 测试状态查询
        logger.info("测试状态查询...")
        status = manager.获取临时表状态()
        logger.info(f"临时表状态: {status}")
        
        # 测试标记已处理
        logger.info("测试标记数据已处理...")
        manager.标记东财数据已处理()
        manager.标记同花数据已处理()
        
        # 验证标记结果
        dcf_pending_after = manager.读取东财待处理数据()
        ths_pending_after = manager.读取同花待处理数据()
        
        assert len(dcf_pending_after) == 0, "东财数据标记失败"
        assert len(ths_pending_after) == 0, "同花数据标记失败"
        
        manager.close()
        logger.info("✅ 临时表管理器测试通过")
        return True
        
    except Exception as e:
        logger.error(f"❌ 临时表管理器测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def 测试个股人气表集成():
    """测试个股人气表与临时表的集成"""
    try:
        logger.info("开始测试个股人气表集成...")
        
        from 数据2_网络采集.人气临时表管理 import 获取临时表管理器
        from 数据2_网络采集.个股人气表 import PopularityDatabase
        from 公共模块.交易日期 import get_trading_date
        
        # 准备测试数据
        test_data = [
            ("000001", "平安银行", 1),
            ("000002", "万科A", 2),
            ("000858", "五粮液", 3)
        ]
        
        # 写入临时表
        manager = 获取临时表管理器()
        manager.写入东财数据(test_data)
        manager.close()
        
        # 测试从临时表更新
        db = PopularityDatabase()
        trading_date = get_trading_date()
        
        logger.info("测试从临时表更新东财数据...")
        count = db.从临时表更新东财人气(trading_date)
        assert count == len(test_data), "更新数据数量不匹配"
        
        logger.info("✅ 个股人气表集成测试通过")
        return True
        
    except Exception as e:
        logger.error(f"❌ 个股人气表集成测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def 测试数据流调度器():
    """测试数据流调度器（模拟测试）"""
    try:
        logger.info("开始测试数据流调度器...")
        
        from QDLC.人气数据流调度器 import 人气数据流调度器
        
        # 创建调度器实例
        scheduler = 人气数据流调度器()
        
        # 测试初始化
        results = scheduler.获取执行结果()
        assert '东财采集' in results, "调度器初始化失败"
        assert '同花采集' in results, "调度器初始化失败"
        assert '数据处理' in results, "调度器初始化失败"
        
        logger.info("✅ 数据流调度器测试通过")
        return True
        
    except Exception as e:
        logger.error(f"❌ 数据流调度器测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def 测试性能基准():
    """测试临时表方案的性能"""
    try:
        logger.info("开始性能基准测试...")
        
        from 数据库1_基础表模块.人气临时表管理 import 获取临时表管理器
        
        # 生成大量测试数据
        large_test_data = []
        for i in range(1000):
            code = f"{i:06d}"
            name = f"测试股票{i}"
            rank = i + 1
            large_test_data.append((code, name, rank))
        
        manager = 获取临时表管理器()
        
        # 测试写入性能
        start_time = time.time()
        manager.写入东财数据(large_test_data)
        write_time = time.time() - start_time
        
        # 测试读取性能
        start_time = time.time()
        data = manager.读取东财待处理数据()
        read_time = time.time() - start_time
        
        # 测试更新性能
        start_time = time.time()
        manager.标记东财数据已处理()
        update_time = time.time() - start_time
        
        manager.close()
        
        logger.info(f"性能测试结果:")
        logger.info(f"  写入1000条数据: {write_time:.3f}秒")
        logger.info(f"  读取1000条数据: {read_time:.3f}秒")
        logger.info(f"  更新1000条数据: {update_time:.3f}秒")
        
        # 性能要求：每个操作应在1秒内完成
        assert write_time < 1.0, f"写入性能不达标: {write_time:.3f}秒"
        assert read_time < 1.0, f"读取性能不达标: {read_time:.3f}秒"
        assert update_time < 1.0, f"更新性能不达标: {update_time:.3f}秒"
        
        logger.info("✅ 性能基准测试通过")
        return True
        
    except Exception as e:
        logger.error(f"❌ 性能基准测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    logger.info("="*60)
    logger.info("TradeFusion临时表方案完整性测试")
    logger.info("="*60)
    
    test_results = []
    
    # 执行各项测试
    tests = [
        ("临时表管理器", 测试临时表管理器),
        ("个股人气表集成", 测试个股人气表集成),
        ("数据流调度器", 测试数据流调度器),
        ("性能基准", 测试性能基准)
    ]
    
    for test_name, test_func in tests:
        logger.info(f"\n开始执行: {test_name}")
        result = test_func()
        test_results.append((test_name, result))
        
        if result:
            logger.info(f"✅ {test_name} 测试通过")
        else:
            logger.error(f"❌ {test_name} 测试失败")
    
    # 汇总测试结果
    logger.info("\n" + "="*60)
    logger.info("测试结果汇总")
    logger.info("="*60)
    
    passed = 0
    total = len(test_results)
    
    for test_name, result in test_results:
        status = "✅ 通过" if result else "❌ 失败"
        logger.info(f"{test_name}: {status}")
        if result:
            passed += 1
    
    logger.info(f"\n总计: {passed}/{total} 项测试通过")
    
    if passed == total:
        logger.info("🎉 所有测试通过！临时表方案可以投入使用。")
        return True
    else:
        logger.error("⚠️ 部分测试失败，请检查问题后重新测试。")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
