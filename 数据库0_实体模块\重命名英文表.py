#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
TradeFusion数据库表重命名脚本
将英文表名 dat_processing_list 重命名为 DAT文件处理记录表
同时重命名字段：date → 日期, stock_code → 股票代码
"""

import sqlite3
import os
from pathlib import Path

def rename_table_and_fields():
    """重命名表和字段"""
    # 获取数据库路径
    current_file = Path(__file__).resolve()
    project_root = current_file.parent.parent
    db_path = project_root / "数据库0_实体模块" / "股票数据.db"
    
    if not db_path.exists():
        print(f"❌ 数据库文件不存在: {db_path}")
        return False
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 检查旧表是否存在
        cursor.execute("""
            SELECT name FROM sqlite_master 
            WHERE type='table' AND name='dat_processing_list'
        """)
        
        if not cursor.fetchone():
            print("ℹ️  旧表 dat_processing_list 不存在，无需重命名")
            return True
        
        print("🔄 开始重命名表和字段...")
        
        # 步骤1：创建新表结构
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS DAT文件处理记录表 (
                日期 INTEGER,
                股票代码 TEXT,
                PRIMARY KEY (日期, 股票代码)
            )
        """)
        print("✅ 创建新表结构完成")
        
        # 步骤2：迁移数据
        cursor.execute("""
            INSERT OR IGNORE INTO DAT文件处理记录表 (日期, 股票代码)
            SELECT date, stock_code FROM dat_processing_list
        """)
        migrated_count = cursor.rowcount
        print(f"✅ 数据迁移完成，迁移了 {migrated_count} 条记录")
        
        # 步骤3：删除旧表
        cursor.execute("DROP TABLE dat_processing_list")
        print("✅ 删除旧表完成")
        
        # 提交更改
        conn.commit()
        print("🎉 表重命名成功！")
        
        # 验证新表
        cursor.execute("SELECT COUNT(*) FROM DAT文件处理记录表")
        count = cursor.fetchone()[0]
        print(f"📊 新表记录数: {count}")
        
        return True
        
    except Exception as e:
        print(f"❌ 重命名失败: {str(e)}")
        if conn:
            conn.rollback()
        return False
        
    finally:
        if conn:
            conn.close()

def main():
    """主函数"""
    print("=" * 60)
    print("TradeFusion数据库表重命名工具")
    print("将 dat_processing_list 重命名为 DAT文件处理记录表")
    print("=" * 60)
    
    success = rename_table_and_fields()
    
    if success:
        print("\n🎯 重命名完成！")
        print("📝 相关模块已同步更新：")
        print("   - 数据库写大智慧/A4_所属板块强度.py")
        print("   - 数据库写大智慧/接力.py")
        print("\n⚠️  请重新运行相关模块以验证功能正常")
    else:
        print("\n❌ 重命名失败，请检查错误信息")
    
    return success

if __name__ == "__main__":
    main()
