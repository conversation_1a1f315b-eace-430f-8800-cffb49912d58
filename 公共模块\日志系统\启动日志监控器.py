#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
日志监控器启动脚本
提供便捷的日志监控器启动方式
"""

import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

# 导入并启动日志监控器
if __name__ == "__main__":
    # 切换到项目根目录
    os.chdir(project_root)
    
    # 导入日志监控器
    from 公共模块.日志系统.工具.日志监控器 import LogMonitor
    
    print("🚀 启动TradeFusion日志监控器...")
    print(f"📁 项目根目录: {project_root}")
    print("=" * 60)
    
    # 创建并启动监控器
    monitor = LogMonitor()
    monitor.monitor()
