/**
 * TradeFusion桌面版 - Electron主进程
 * 
 * 功能：创建桌面应用窗口，提供系统级API访问
 * 
 * <AUTHOR> Team
 * @version 2.0.0 (Electron版)
 */

const { app, BrowserWindow, ipcMain, dialog, shell, globalShortcut } = require('electron');
const { spawn, exec } = require('child_process');
const path = require('path');
const fs = require('fs');

// 保持对窗口对象的全局引用
let mainWindow;
let runningProcesses = new Map(); // 存储运行中的进程

// 禁用硬件加速以解决GPU问题
app.disableHardwareAcceleration();

/**
 * 创建主窗口
 */
function createWindow() {
    // 创建浏览器窗口
    mainWindow = new BrowserWindow({
        width: 1400,
        height: 900,
        minWidth: 1200,
        minHeight: 700,
        webPreferences: {
            nodeIntegration: true,
            contextIsolation: false,
            enableRemoteModule: true
        },
        icon: path.join(__dirname, 'assets/icon.png'),
        title: 'TradeFusion 桌面版 v2.0',
        show: false // 先隐藏，加载完成后显示
    });

    // 加载应用的HTML文件
    mainWindow.loadFile('index.html');

    // 窗口准备好后显示
    mainWindow.once('ready-to-show', () => {
        mainWindow.show();

        // 开发模式下打开开发者工具
        if (process.argv.includes('--dev')) {
            mainWindow.webContents.openDevTools();
        }

        // 注册快捷键
        registerShortcuts();
    });

    // 当窗口被关闭时
    mainWindow.on('closed', () => {
        // 清理所有运行中的进程
        cleanupAllProcesses();
        mainWindow = null;
    });

    // 阻止新窗口打开
    mainWindow.webContents.setWindowOpenHandler(() => {
        return { action: 'deny' };
    });
}

/**
 * 注册快捷键
 */
function registerShortcuts() {
    // 刷新页面 - Ctrl+R 或 F5
    globalShortcut.register('CommandOrControl+R', () => {
        if (mainWindow && mainWindow.webContents) {
            mainWindow.webContents.reload();
        }
    });

    globalShortcut.register('F5', () => {
        if (mainWindow && mainWindow.webContents) {
            mainWindow.webContents.reload();
        }
    });

    // 开发者工具 - Ctrl+Shift+I 或 F12
    globalShortcut.register('CommandOrControl+Shift+I', () => {
        if (mainWindow && mainWindow.webContents) {
            mainWindow.webContents.toggleDevTools();
        }
    });

    globalShortcut.register('F12', () => {
        if (mainWindow && mainWindow.webContents) {
            mainWindow.webContents.toggleDevTools();
        }
    });
}

/**
 * 应用准备就绪
 */
app.whenReady().then(() => {
    createWindow();

    app.on('activate', () => {
        if (BrowserWindow.getAllWindows().length === 0) {
            createWindow();
        }
    });
});

/**
 * 所有窗口关闭时退出应用
 */
app.on('window-all-closed', () => {
    // 清理快捷键
    globalShortcut.unregisterAll();

    if (process.platform !== 'darwin') {
        app.quit();
    }
});

/**
 * IPC通信：启动Python模块
 */
ipcMain.handle('start-python-module', async (event, moduleData) => {
    try {
        const { name, filePath, workingDirectory, pythonExecutable } = moduleData;
        
        console.log(`[Main] 启动模块: ${name}`);
        console.log(`[Main] 文件路径: ${filePath}`);
        console.log(`[Main] 工作目录: ${workingDirectory}`);

        // 构造完整路径
        const fullPath = path.resolve(workingDirectory, filePath);
        
        // 检查文件是否存在
        if (!fs.existsSync(fullPath)) {
            throw new Error(`Python文件不存在: ${fullPath}`);
        }

        // 启动Python进程
        const pythonProcess = spawn(pythonExecutable || 'python', [fullPath], {
            cwd: workingDirectory,
            detached: false,
            stdio: ['ignore', 'pipe', 'pipe']
        });

        // 存储进程引用
        runningProcesses.set(name, {
            process: pythonProcess,
            pid: pythonProcess.pid,
            startTime: new Date(),
            filePath: filePath
        });

        console.log(`[Main] 模块 ${name} 已启动，PID: ${pythonProcess.pid}`);

        // 监听进程输出
        pythonProcess.stdout.on('data', (data) => {
            console.log(`[${name}] ${data.toString()}`);
        });

        pythonProcess.stderr.on('data', (data) => {
            console.error(`[${name}] ERROR: ${data.toString()}`);
        });

        // 监听进程退出
        pythonProcess.on('close', (code) => {
            console.log(`[Main] 模块 ${name} 已退出，退出码: ${code}`);
            runningProcesses.delete(name);
            
            // 通知渲染进程
            if (mainWindow) {
                mainWindow.webContents.send('module-stopped', { name, code });
            }
        });

        pythonProcess.on('error', (error) => {
            console.error(`[Main] 启动模块 ${name} 失败:`, error);
            runningProcesses.delete(name);
            throw error;
        });

        return {
            success: true,
            pid: pythonProcess.pid,
            message: `模块 ${name} 启动成功`
        };

    } catch (error) {
        console.error('[Main] 启动Python模块失败:', error);
        return {
            success: false,
            error: error.message
        };
    }
});

/**
 * IPC通信：停止Python模块
 */
ipcMain.handle('stop-python-module', async (event, moduleName) => {
    try {
        console.log(`[Main] 停止模块: ${moduleName}`);

        const processInfo = runningProcesses.get(moduleName);
        if (!processInfo) {
            return {
                success: false,
                error: `模块 ${moduleName} 未在运行中`
            };
        }

        // 终止进程
        processInfo.process.kill('SIGTERM');
        
        // 如果进程没有响应，强制终止
        setTimeout(() => {
            if (runningProcesses.has(moduleName)) {
                processInfo.process.kill('SIGKILL');
                runningProcesses.delete(moduleName);
            }
        }, 5000);

        console.log(`[Main] 模块 ${moduleName} 停止命令已发送`);

        return {
            success: true,
            message: `模块 ${moduleName} 停止成功`
        };

    } catch (error) {
        console.error('[Main] 停止Python模块失败:', error);
        return {
            success: false,
            error: error.message
        };
    }
});

/**
 * IPC通信：获取运行中的进程列表
 */
ipcMain.handle('get-running-processes', async () => {
    const processes = {};
    for (const [name, info] of runningProcesses) {
        processes[name] = {
            pid: info.pid,
            startTime: info.startTime,
            filePath: info.filePath
        };
    }
    return processes;
});

/**
 * IPC通信：显示文件夹
 */
ipcMain.handle('show-folder', async (event, folderPath) => {
    try {
        await shell.openPath(folderPath);
        return { success: true };
    } catch (error) {
        return { success: false, error: error.message };
    }
});

/**
 * 清理所有运行中的进程
 */
function cleanupAllProcesses() {
    console.log('[Main] 清理所有运行中的进程...');
    for (const [name, info] of runningProcesses) {
        try {
            info.process.kill('SIGTERM');
            console.log(`[Main] 已终止进程: ${name} (PID: ${info.pid})`);
        } catch (error) {
            console.error(`[Main] 终止进程 ${name} 失败:`, error);
        }
    }
    runningProcesses.clear();
}

/**
 * 应用退出前清理
 */
app.on('before-quit', () => {
    cleanupAllProcesses();
});
