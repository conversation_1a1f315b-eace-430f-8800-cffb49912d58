#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
TradeFusion选股宝临时表方案测试脚本
功能：测试选股宝数据流的临时表方案完整性和可靠性
作者：TradeFusion团队
创建时间：2025-07-12
"""

import sys
import os
import time
import logging
from pathlib import Path

# 添加项目根目录到路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def 测试选股宝临时表管理器():
    """测试选股宝临时表管理器的基本功能"""
    try:
        logger.info("开始测试选股宝临时表管理器...")
        
        from 数据库1_基础表模块.人气临时表管理 import 获取临时表管理器
        
        # 创建管理器实例
        manager = 获取临时表管理器()
        
        # 测试原始数据
        test_raw_data = """股票名称
000001
平安银行
123.45
+5.67%
8.9%
理由,板块消息测试
板块A,+3.2%
股票名称
000002
万科A
56.78
+2.34%
4.5%"""
        
        # 测试清洗数据
        test_cleaned_data = [
            (20250712, "000001", "板块A", "+3.2%", "板块消息测试", "个股解读测试1"),
            (20250712, "000002", "板块B", "+1.5%", "板块消息测试2", "个股解读测试2"),
            (20250712, "000858", "板块C", "+2.8%", "板块消息测试3", "个股解读测试3")
        ]
        
        # 测试写入原始数据
        logger.info("测试写入选股宝原始数据...")
        raw_count = manager.写入选股宝原始数据(test_raw_data)
        assert raw_count == 1, "原始数据写入数量不匹配"
        
        # 测试读取原始数据
        logger.info("测试读取选股宝原始数据...")
        raw_pending = manager.读取选股宝原始待处理数据()
        assert raw_pending is not None, "原始数据读取失败"
        assert len(raw_pending) > 0, "原始数据为空"
        
        # 测试写入清洗数据
        logger.info("测试写入选股宝清洗数据...")
        cleaned_count = manager.写入选股宝清洗数据(test_cleaned_data)
        assert cleaned_count == len(test_cleaned_data), "清洗数据写入数量不匹配"
        
        # 测试读取清洗数据
        logger.info("测试读取选股宝清洗数据...")
        cleaned_pending = manager.读取选股宝清洗待处理数据()
        assert len(cleaned_pending) == len(test_cleaned_data), "清洗数据读取数量不匹配"
        
        # 测试状态查询
        logger.info("测试状态查询...")
        status = manager.获取临时表状态()
        logger.info(f"临时表状态: {status}")
        assert '选股宝原始' in status, "状态查询缺少选股宝原始"
        assert '选股宝清洗' in status, "状态查询缺少选股宝清洗"
        
        # 测试标记已处理
        logger.info("测试标记数据已处理...")
        manager.标记选股宝原始数据已处理()
        manager.标记选股宝清洗数据已处理()
        
        # 验证标记结果
        raw_pending_after = manager.读取选股宝原始待处理数据()
        cleaned_pending_after = manager.读取选股宝清洗待处理数据()
        
        assert raw_pending_after is None, "原始数据标记失败"
        assert len(cleaned_pending_after) == 0, "清洗数据标记失败"
        
        manager.close()
        logger.info("✅ 选股宝临时表管理器测试通过")
        return True
        
    except Exception as e:
        logger.error(f"❌ 选股宝临时表管理器测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def 测试选股宝数据流集成():
    """测试选股宝完整数据流"""
    try:
        logger.info("开始测试选股宝数据流集成...")
        
        from 数据库1_基础表模块.人气临时表管理 import 获取临时表管理器
        from 数据3_网络采集_bk.选股宝清洗 import DataCleaner
        from 数据3_网络采集_bk.个股解读_板块信息_关联表 import main_from_temp_table
        
        # 准备测试数据（符合选股宝实际格式）
        test_raw_data = """测试板块,+2.5%
理由,测试板块消息
股票名称
平安银行
000001.SZ
123.45
+5.67%
8.9%
100亿
这是测试解读内容
万科A
000002.SZ
56.78
+2.34%
4.5%
200亿
这是另一个测试解读"""
        
        # 1. 写入原始数据到临时表
        manager = 获取临时表管理器()
        manager.写入选股宝原始数据(test_raw_data)
        manager.close()
        
        # 2. 测试清洗流程
        logger.info("测试选股宝清洗流程...")
        cleaner = DataCleaner("", "")
        cleaned_count = cleaner.clean_data_from_temp_table()
        assert cleaned_count is not None and cleaned_count > 0, "清洗流程失败"
        
        # 3. 测试数据库导入流程
        logger.info("测试数据库导入流程...")
        import_result = main_from_temp_table()
        assert import_result == True, "数据库导入失败"
        
        logger.info("✅ 选股宝数据流集成测试通过")
        return True
        
    except Exception as e:
        logger.error(f"❌ 选股宝数据流集成测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def 测试选股宝性能基准():
    """测试选股宝临时表方案的性能"""
    try:
        logger.info("开始选股宝性能基准测试...")
        
        from 数据库1_基础表模块.人气临时表管理 import 获取临时表管理器
        
        # 生成大量测试数据
        large_test_data = []
        for i in range(100):
            date = 20250712
            code = f"{i:06d}"
            sector = f"测试板块{i%10}"
            change = f"+{i%5}.{i%10}%"
            message = f"测试消息{i}"
            description = f"测试解读{i}"
            large_test_data.append((date, code, sector, change, message, description))
        
        manager = 获取临时表管理器()
        
        # 测试写入性能
        start_time = time.time()
        manager.写入选股宝清洗数据(large_test_data)
        write_time = time.time() - start_time
        
        # 测试读取性能
        start_time = time.time()
        data = manager.读取选股宝清洗待处理数据()
        read_time = time.time() - start_time
        
        # 测试更新性能
        start_time = time.time()
        manager.标记选股宝清洗数据已处理()
        update_time = time.time() - start_time
        
        manager.close()
        
        logger.info(f"选股宝性能测试结果:")
        logger.info(f"  写入100条数据: {write_time:.3f}秒")
        logger.info(f"  读取100条数据: {read_time:.3f}秒")
        logger.info(f"  更新100条数据: {update_time:.3f}秒")
        
        # 性能要求：每个操作应在1秒内完成
        assert write_time < 1.0, f"写入性能不达标: {write_time:.3f}秒"
        assert read_time < 1.0, f"读取性能不达标: {read_time:.3f}秒"
        assert update_time < 1.0, f"更新性能不达标: {update_time:.3f}秒"
        
        logger.info("✅ 选股宝性能基准测试通过")
        return True
        
    except Exception as e:
        logger.error(f"❌ 选股宝性能基准测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    logger.info("="*60)
    logger.info("TradeFusion选股宝临时表方案完整性测试")
    logger.info("="*60)
    
    test_results = []
    
    # 执行各项测试
    tests = [
        ("选股宝临时表管理器", 测试选股宝临时表管理器),
        ("选股宝数据流集成", 测试选股宝数据流集成),
        ("选股宝性能基准", 测试选股宝性能基准)
    ]
    
    for test_name, test_func in tests:
        logger.info(f"\n开始执行: {test_name}")
        result = test_func()
        test_results.append((test_name, result))
        
        if result:
            logger.info(f"✅ {test_name} 测试通过")
        else:
            logger.error(f"❌ {test_name} 测试失败")
    
    # 汇总测试结果
    logger.info("\n" + "="*60)
    logger.info("测试结果汇总")
    logger.info("="*60)
    
    passed = 0
    total = len(test_results)
    
    for test_name, result in test_results:
        status = "✅ 通过" if result else "❌ 失败"
        logger.info(f"{test_name}: {status}")
        if result:
            passed += 1
    
    logger.info(f"\n总计: {passed}/{total} 项测试通过")
    
    if passed == total:
        logger.info("🎉 所有测试通过！选股宝临时表方案可以投入使用。")
        return True
    else:
        logger.error("⚠️ 部分测试失败，请检查问题后重新测试。")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
