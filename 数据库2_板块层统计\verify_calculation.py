#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
验证板块精选计算的正确性
"""

import sqlite3
import os

def calculate_dazhihui_sma(scores, n=5, m=1):
    """计算大智慧SMA算法"""
    if not scores or len(scores) == 0:
        return 0.0
    
    # 第一天初始值
    sma = scores[0]
    
    # 递归计算后续值
    for i in range(1, len(scores)):
        x = scores[i]
        sma = (m * x + (n - m) * sma) / n
    
    return round(sma, 2)

def main():
    # 连接数据库
    import sys
    from pathlib import Path

    # 添加项目根目录到路径
    current_file = Path(__file__).resolve()
    project_root = current_file.parent.parent
    if str(project_root) not in sys.path:
        sys.path.insert(0, str(project_root))

    try:
        from 公共模块.配置管理 import get_config
        config = get_config()
        db_path = str(config.get_db_path())
    except ImportError:
        db_path = str(project_root / "数据库0_实体模块/股票数据.db")
    conn = sqlite3.connect(db_path)
    cursor = conn.cursor()

    # 获取最近5个交易日期
    cursor.execute('''
        SELECT DISTINCT 日期 
        FROM [板块涨停表] 
        WHERE 板块评分 > 0 
        ORDER BY 日期 DESC 
        LIMIT 5
    ''')
    dates = [str(row[0]) for row in cursor.fetchall()]

    # 构建日期条件
    date_placeholders = ','.join(['?' for _ in dates])

    cursor.execute(f'''
        SELECT 日期, 板块名称, 板块评分
        FROM [板块涨停表]
        WHERE 日期 IN ({date_placeholders})
        AND 板块评分 > 0
        ORDER BY 板块名称, 日期 DESC
    ''', dates)

    # 按板块名称分组
    sector_data = {}
    for row in cursor.fetchall():
        sector_name = row[1]
        if sector_name not in sector_data:
            sector_data[sector_name] = []
        
        sector_data[sector_name].append({
            '日期': row[0],
            '板块名称': row[1],
            '板块评分': float(row[2])
        })

    pass

    sma_sectors = []
    for sector_name, daily_data in sector_data.items():
        if len(daily_data) == 0:
            continue
        
        # 提取评分列表（已按日期倒序排列）
        scores = [item['板块评分'] for item in daily_data]
        
        # 计算SMA
        sma_score = calculate_dazhihui_sma(scores, 5, 1)
        
        # 获取最新评分
        latest_score = scores[0] if scores else 0.0
        
        sma_sectors.append({
            'sector_name': sector_name,
            'sma_score': sma_score,
            'latest_score': latest_score,
            'daily_scores': scores
        })
        
        pass

    # 按SMA值降序排列
    sma_sorted = sorted(sma_sectors, key=lambda x: x['sma_score'], reverse=True)

    # 按最新日期评分降序排列
    latest_sorted = sorted(sma_sectors, key=lambda x: x['latest_score'], reverse=True)

    pass
    for i, sector in enumerate(sma_sorted[:4], 1):
        pass

    pass
    for i, sector in enumerate(latest_sorted[:3], 1):
        pass

    # 条件1: SMA值前四名的板块名称
    top_sma_names = set([sector['sector_name'] for sector in sma_sorted[:4]])

    # 条件2: 最新日期评分前三名的板块名称
    top_latest_names = set([sector['sector_name'] for sector in latest_sorted[:3]])

    # 计算交集：同时满足两个条件的板块
    intersection_names = top_sma_names & top_latest_names

    pass

    # 详细展示精选板块
    pass
    for sector in sma_sectors:
        if sector['sector_name'] in intersection_names:
            sma_rank = next(i+1 for i, s in enumerate(sma_sorted) if s['sector_name'] == sector['sector_name'])
            latest_rank = next(i+1 for i, s in enumerate(latest_sorted) if s['sector_name'] == sector['sector_name'])

            pass

    conn.close()

if __name__ == "__main__":
    main()
