{"modules": {"rq-scheduler": {"name": "RQ.py调度中心", "type": "scheduler", "status": "idle", "description": "人气数据流程调度器，60秒循环", "position": {"x": 100, "y": 50}, "hasControls": true, "filePath": "QDLC/RQ.py"}, "xgb-scheduler": {"name": "XGB_JL.py调度中心", "type": "scheduler", "status": "idle", "description": "选股宝接力流程调度器，60秒循环", "position": {"x": 1200, "y": 50}, "hasControls": true, "filePath": "QDLC/XGB_JL.py"}, "popularity-dongcai": {"name": "人气_东财采集", "type": "data-collection", "status": "idle", "description": "东方财富人气数据网络采集", "position": {"x": 50, "y": 200}, "hasControls": true, "filePath": "数据1_采集模块/人气_东财采集.py"}, "popularity-tonghua": {"name": "人气_同花采集", "type": "data-collection", "status": "idle", "description": "同花顺人气数据网络采集+股票代码查询", "position": {"x": 200, "y": 200}, "hasControls": true, "filePath": "数据1_采集模块/人气_同花采集.py"}, "xgb-crawl": {"name": "选股宝抓取", "type": "data-collection", "status": "idle", "description": "选股宝原始股票数据网络采集", "position": {"x": 1100, "y": 200}, "hasControls": true, "filePath": "数据1_采集模块/选股宝抓取.py"}, "lbgd-collect": {"name": "采集_本地数据", "type": "data-collection", "status": "running", "description": "连板高度DAT数据本地文件解析", "position": {"x": 1300, "y": 200}, "hasControls": true, "filePath": "数据1_采集模块/采集_本地数据.py"}, "xgb-clean": {"name": "选股宝清洗", "type": "data-processing", "status": "idle", "description": "数据清洗处理，CSV→CSV", "position": {"x": 1100, "y": 350}, "hasControls": true, "filePath": "数据2_清洗模块/选股宝清洗.py"}, "stock-sector-import": {"name": "个股解读_板块信息_关联表", "type": "data-hub", "status": "idle", "description": "📊一个模块写入3张表📊 - 数据枢纽模块", "position": {"x": 1100, "y": 500}, "hasControls": true, "filePath": "数据库1_基础表模块/个股解读_板块信息_关联表.py"}, "sector-ztting": {"name": "板块涨停表", "type": "analysis", "status": "idle", "description": "板块涨停统计，只保留前7名", "position": {"x": 700, "y": 800}, "hasControls": true, "filePath": "数据库2_板块层统计/板块涨停表.py"}, "sector-select": {"name": "板块精选", "type": "analysis", "status": "idle", "description": "精选板块，SMA算法+双重筛选", "position": {"x": 700, "y": 1050}, "hasControls": true, "filePath": "数据库2_板块层统计/板块精选.py"}, "stock-relay": {"name": "个股接力表", "type": "analysis", "status": "idle", "description": "📊读取4张表📊 - 复杂接力算法", "position": {"x": 400, "y": 1300}, "hasControls": true, "filePath": "数据库3_个股所属板块层统计/个股接力表.py"}, "sector-score": {"name": "A3_所属板块评分表", "type": "analysis", "status": "idle", "description": "板块强度评分计算", "position": {"x": 1000, "y": 800}, "hasControls": true, "filePath": "数据库3_个股所属板块层统计/A3_所属板块评分表.py"}, "popularity-190": {"name": "综合人气190", "type": "output", "status": "idle", "description": "读取人气数据生成DAT文件", "position": {"x": 50, "y": 500}, "hasControls": true, "filePath": "数据库写大智慧/综合人气190.py"}, "popularity-blk": {"name": "生成DZH3人气股票板块", "type": "output", "status": "idle", "description": "生成人气板块BLK文件", "position": {"x": 200, "y": 500}, "hasControls": true, "filePath": "数据库写大智慧/生成DZH3人气股票板块.py"}, "str-output": {"name": "选股宝_大智慧str", "type": "output", "status": "idle", "description": "📊读取3张表📊 - STR文件生成", "position": {"x": 900, "y": 1600}, "hasControls": true, "filePath": "数据库写大智慧/选股宝_大智慧str.py"}, "ggqd-output": {"name": "A4_所属板块强度", "type": "output", "status": "idle", "description": "每只股票一个DAT文件", "position": {"x": 1000, "y": 1600}, "hasControls": true, "filePath": "数据库写大智慧/A4_所属板块强度.py"}, "jl-output": {"name": "接力", "type": "output", "status": "idle", "description": "每只股票一个DAT文件", "position": {"x": 400, "y": 1600}, "hasControls": true, "filePath": "数据库写大智慧/接力.py"}, "db-popularity": {"name": "个股人气表", "type": "database", "status": "active", "description": "数据库表：个股人气表", "position": {"x": 125, "y": 350}, "hasControls": false}, "db-height": {"name": "个股连板高度表", "type": "database", "status": "active", "description": "数据库表：个股连板高度表", "position": {"x": 1300, "y": 350}, "hasControls": false}, "db-stock-info": {"name": "个股解读表", "type": "database", "status": "active", "description": "数据库表：个股解读表", "position": {"x": 900, "y": 650}, "hasControls": false}, "db-sector-info": {"name": "板块信息表", "type": "database", "status": "active", "description": "数据库表：板块信息表", "position": {"x": 1100, "y": 650}, "hasControls": false}, "db-relation": {"name": "个股板块关联表", "type": "database", "status": "active", "description": "数据库表：个股板块关联表", "position": {"x": 1300, "y": 650}, "hasControls": false}, "db-sector-ztting": {"name": "板块涨停表", "type": "database", "status": "active", "description": "数据库表：板块涨停表", "position": {"x": 700, "y": 950}, "hasControls": false}, "db-sector-select": {"name": "板块精选表", "type": "database", "status": "active", "description": "数据库表：板块精选表", "position": {"x": 700, "y": 1200}, "hasControls": false}, "db-stock-relay": {"name": "个股接力表", "type": "database", "status": "active", "description": "数据库表：个股接力表", "position": {"x": 400, "y": 1450}, "hasControls": false}, "db-sector-score": {"name": "所属板块评分表", "type": "database", "status": "active", "description": "数据库表：所属板块评分表", "position": {"x": 1000, "y": 950}, "hasControls": false}}, "connections": [{"from": "rq-scheduler", "to": "popularity-dong<PERSON>i", "type": "rq-flow", "label": "启动采集"}, {"from": "rq-scheduler", "to": "popularity-tonghua", "type": "rq-flow", "label": "启动采集"}, {"from": "rq-scheduler", "to": "popularity-190", "type": "rq-flow", "label": "启动输出"}, {"from": "rq-scheduler", "to": "popularity-blk", "type": "rq-flow", "label": "启动输出"}, {"from": "popularity-dong<PERSON>i", "to": "db-popularity", "type": "database-flow", "label": "东财人气排名"}, {"from": "popularity-tonghua", "to": "db-popularity", "type": "database-flow", "label": "同花人气排名"}, {"from": "db-popularity", "to": "popularity-190", "type": "database-flow", "label": "读取人气数据"}, {"from": "db-popularity", "to": "popularity-blk", "type": "database-flow", "label": "读取人气数据"}, {"from": "xgb-scheduler", "to": "xgb-crawl", "type": "xgb-flow", "label": "T1:数据抓取"}, {"from": "xgb-scheduler", "to": "xgb-clean", "type": "xgb-flow", "label": "T2:数据清洗"}, {"from": "xgb-scheduler", "to": "stock-sector-import", "type": "xgb-flow", "label": "T3:数据库导入"}, {"from": "xgb-scheduler", "to": "str-output", "type": "xgb-flow", "label": "T4:STR文件生成"}, {"from": "xgb-scheduler", "to": "sector-ztting", "type": "xgb-flow", "label": "T5:板块涨停统计"}, {"from": "xgb-scheduler", "to": "sector-select", "type": "xgb-flow", "label": "T6:板块精选计算"}, {"from": "xgb-scheduler", "to": "stock-relay", "type": "xgb-flow", "label": "T7:个股接力处理"}, {"from": "xgb-scheduler", "to": "lbgd-collect", "type": "xgb-flow", "label": "T8:连板高度处理"}, {"from": "xgb-scheduler", "to": "sector-score", "type": "xgb-flow", "label": "T9:个股强度计算"}, {"from": "xgb-scheduler", "to": "ggqd-output", "type": "xgb-flow", "label": "T10:DAT文件生成"}, {"from": "xgb-scheduler", "to": "jl-output", "type": "xgb-flow", "label": "T11:接力DAT生成"}, {"from": "xgb-crawl", "to": "xgb-clean", "type": "data-flow", "label": "bsj_xgb_ie.csv"}, {"from": "xgb-clean", "to": "stock-sector-import", "type": "data-flow", "label": "csj_xgb_qx.csv"}, {"from": "stock-sector-import", "to": "db-stock-info", "type": "database-flow", "label": "个股解读表"}, {"from": "stock-sector-import", "to": "db-sector-info", "type": "database-flow", "label": "板块信息表"}, {"from": "stock-sector-import", "to": "db-relation", "type": "database-flow", "label": "个股板块关联表"}, {"from": "lbgd-collect", "to": "db-height", "type": "database-flow", "label": "连板高度"}, {"from": "db-relation", "to": "sector-ztting", "type": "database-flow", "label": "关联数据"}, {"from": "db-height", "to": "sector-ztting", "type": "database-flow", "label": "连板数据"}, {"from": "sector-ztting", "to": "db-sector-ztting", "type": "database-flow", "label": "涨停统计"}, {"from": "db-sector-ztting", "to": "sector-select", "type": "database-flow", "label": "涨停数据"}, {"from": "sector-select", "to": "db-sector-select", "type": "database-flow", "label": "精选结果"}, {"from": "db-sector-select", "to": "stock-relay", "type": "database-flow", "label": "读取:板块精选表"}, {"from": "db-relation", "to": "stock-relay", "type": "database-flow", "label": "读取:个股板块关联表"}, {"from": "db-popularity", "to": "stock-relay", "type": "database-flow", "label": "读取:个股人气表"}, {"from": "db-sector-ztting", "to": "stock-relay", "type": "database-flow", "label": "读取:板块涨停表"}, {"from": "stock-relay", "to": "db-stock-relay", "type": "database-flow", "label": "写入:个股接力表"}, {"from": "db-relation", "to": "sector-score", "type": "database-flow", "label": "关联数据"}, {"from": "db-sector-ztting", "to": "sector-score", "type": "database-flow", "label": "涨停数据"}, {"from": "sector-score", "to": "db-sector-score", "type": "database-flow", "label": "评分结果"}, {"from": "db-relation", "to": "str-output", "type": "database-flow", "label": "读取:个股板块关联表"}, {"from": "db-sector-info", "to": "str-output", "type": "database-flow", "label": "读取:板块信息表"}, {"from": "db-stock-info", "to": "str-output", "type": "database-flow", "label": "读取:个股解读表"}, {"from": "db-sector-score", "to": "ggqd-output", "type": "database-flow", "label": "评分数据"}, {"from": "db-stock-relay", "to": "jl-output", "type": "database-flow", "label": "接力数据"}], "config": {"projectRoot": "..", "pythonExecutable": "python", "workingDirectory": "..", "databasePath": "../数据库0_实体模块/股票数据.db"}}