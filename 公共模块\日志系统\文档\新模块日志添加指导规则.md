# TradeFusion新模块日志添加指导规则

## 📋 规则概述

**目的**: 为TradeFusion项目中的每个新模块提供统一的日志添加指导  
**适用范围**: 所有新开发的业务模块、工具模块、系统模块  
**强制性**: 所有模块必须遵循此规则，无例外  

## 🎯 核心指导原则

### 原则一：统一标准
- 所有模块必须使用`TradeFusion统一日志标准.py`
- 严格遵循三色标识系统：🔴模块名 🟡表名 🟢数字
- 每个模块只输出一行关键日志

### 原则二：调用链追踪
- 被调用模块必须标明调用者：`(由[调用方]调用)`
- 独立运行模块不显示调用关系
- 调用链信息必须准确反映实际调用关系

### 原则三：信息精简
- 避免冗余日志输出
- 只记录关键操作结果
- 数字必须使用千分位分隔符

## 📝 必须遵循的实施步骤

### 步骤1：导入统一日志标准
```python
from 公共模块.TradeFusion统一日志标准 import 获取日志器

# 在模块开头获取日志器
logger = 获取日志器("你的模块名")
```

### 步骤2：确定模块类型和日志格式

#### A. 数据处理模块
```python
# 格式：[模块名] 操作描述 - 表名+数字条 (调用信息)
logger.记录模块执行("数据处理完成", 数据量, 调用方)

# 示例
logger.记录模块执行("数据清洗完成", 1234, "数据采集模块")
```

#### B. 数据库操作模块
```python
# 格式：[模块名] 数据库导入完成 - 表名1+数字条|表名2+数字条
logger.记录数据库操作([("表名1", 100), ("表名2", 200)], 调用方)

# 示例
logger.记录数据库操作([("个股解读表", 77), ("板块信息表", 12)], "选股宝清洗")
```

#### C. 文件输出模块
```python
# 格式：[模块名] 文件生成完成 - 生成数字个文件
logger.记录模块执行("文件生成完成", 文件数量, 调用方)

# 示例
logger.记录模块执行("DAT文件生成完成", 3, "数据处理模块")
```

#### D. 独立运行模块
```python
# 格式：[模块名] 操作描述 - 处理数字条记录 (无调用信息)
logger.记录模块执行("数据采集完成", 数据量)

# 示例
logger.记录模块执行("本地数据采集完成", 5678)
```

### 步骤3：添加错误处理日志
```python
try:
    # 业务逻辑
    pass
except Exception as e:
    logger.记录错误("操作失败", e)
    raise
```

## 🔍 模块分类指导

### 数据采集层模块
**特征**: 从外部获取原始数据  
**日志要求**:
- 必须记录采集的数据量
- 独立运行时不显示调用关系
- 格式：`[模块名] 数据采集完成 - 采集X,XXX条原始数据`

**代码模板**:
```python
from 公共模块.TradeFusion统一日志标准 import 获取日志器

logger = 获取日志器("你的采集模块名")

def main():
    try:
        # 数据采集逻辑
        data = collect_data()
        
        # 记录采集结果
        logger.记录模块执行("数据采集完成", len(data))
        
    except Exception as e:
        logger.记录错误("数据采集失败", e)
        raise
```

### 数据处理层模块
**特征**: 处理和转换数据  
**日志要求**:
- 必须显示调用关系
- 记录处理前后的数据量
- 格式：`[模块名] 数据处理完成 - 处理X,XXX条数据 (由[调用方]调用)`

**代码模板**:
```python
from 公共模块.TradeFusion统一日志标准 import 获取日志器

logger = 获取日志器("你的处理模块名")

def process_data(caller_name="未知调用方"):
    try:
        # 数据处理逻辑
        processed_data = process_logic()
        
        # 记录处理结果
        logger.记录模块执行("数据处理完成", len(processed_data), caller_name)
        
        return processed_data
        
    except Exception as e:
        logger.记录错误("数据处理失败", e)
        raise
```

### 数据库操作模块
**特征**: 向数据库写入多张表  
**日志要求**:
- 必须显示所有操作的表和数据量
- 使用管道符分隔多个表
- 格式：`[模块名] 数据库导入完成 - 表1+X条|表2+Y条|表3+Z条 (由[调用方]调用)`

**代码模板**:
```python
from 公共模块.TradeFusion统一日志标准 import 获取日志器

logger = 获取日志器("你的数据库模块名")

def save_to_database(data, caller_name="未知调用方"):
    try:
        # 数据库操作逻辑
        table_operations = []
        
        # 保存到表1
        count1 = save_to_table1(data)
        table_operations.append(("表1名称", count1))
        
        # 保存到表2
        count2 = save_to_table2(data)
        table_operations.append(("表2名称", count2))
        
        # 记录数据库操作结果
        logger.记录数据库操作(table_operations, caller_name)
        
    except Exception as e:
        logger.记录错误("数据库操作失败", e)
        raise
```

### 输出层模块
**特征**: 生成最终文件或报告  
**日志要求**:
- 记录生成的文件数量或输出结果
- 显示调用关系
- 格式：`[模块名] 文件生成完成 - 生成X个文件 (由[调用方]调用)`

**代码模板**:
```python
from 公共模块.TradeFusion统一日志标准 import 获取日志器

logger = 获取日志器("你的输出模块名")

def generate_output(data, caller_name="未知调用方"):
    try:
        # 文件生成逻辑
        files_generated = generate_files(data)
        
        # 记录生成结果
        logger.记录模块执行("文件生成完成", len(files_generated), caller_name)
        
        return files_generated
        
    except Exception as e:
        logger.记录错误("文件生成失败", e)
        raise
```

## ✅ 质量检查清单

### 开发阶段检查
- [ ] 已导入`TradeFusion统一日志标准`
- [ ] 已使用`获取日志器`获取日志器实例
- [ ] 日志格式符合三色标识系统
- [ ] 数字使用千分位分隔符
- [ ] 调用关系信息准确

### 测试阶段检查
- [ ] 日志输出格式正确
- [ ] 颜色显示正常
- [ ] 数据量统计准确
- [ ] 调用链信息完整
- [ ] 错误日志正常工作

### 部署阶段检查
- [ ] 日志文件正常生成
- [ ] 日志轮转功能正常
- [ ] 性能影响可接受
- [ ] 与现有模块日志格式一致

## 🚫 常见错误和避免方法

### 错误1：不使用统一日志标准
```python
# ❌ 错误做法
import logging
logger = logging.getLogger(__name__)
logger.info("处理完成")

# ✅ 正确做法
from 公共模块.TradeFusion统一日志标准 import 获取日志器
logger = 获取日志器("模块名")
logger.记录模块执行("处理完成", 数据量, 调用方)
```

### 错误2：日志格式不统一
```python
# ❌ 错误做法
logger.info("数据处理完成，共处理1234条记录")

# ✅ 正确做法
logger.记录模块执行("数据处理完成", 1234, "调用方模块")
```

### 错误3：缺少调用关系信息
```python
# ❌ 错误做法（被调用模块）
logger.记录模块执行("数据清洗完成", 500)

# ✅ 正确做法（被调用模块）
logger.记录模块执行("数据清洗完成", 500, "数据采集模块")
```

### 错误4：数字格式不规范
```python
# ❌ 错误做法
logger.info(f"处理了{count}条记录")  # 12345条

# ✅ 正确做法
logger.记录模块执行("处理完成", count)  # 12,345条
```

## 📞 支持和帮助

### 遇到问题时的处理流程
1. **查看规范文档**: `TradeFusion日志系统规范.md`
2. **参考现有模块**: 查看已实施模块的代码示例
3. **使用代码模板**: 根据模块类型选择对应模板
4. **测试验证**: 确保日志输出格式正确

### 联系方式
- **技术问题**: 联系系统开发者
- **规范问题**: 联系项目架构师
- **实施问题**: 查看现有模块实现

## 🎯 强制执行

### 代码审查要求
- 所有新模块代码必须经过日志规范审查
- 不符合规范的代码不允许合并
- 定期检查现有模块的合规性

### 质量保证
- 自动化测试包含日志格式检查
- 持续集成流程包含日志规范验证
- 定期生成日志质量报告

---

## 📝 总结

这份指导规则是**每个模块添加日志时必须遵循的权威指南**。它不是建议，而是**强制性的技术规范**。

**关键要点**:
1. **必须使用统一日志标准** - 无例外
2. **严格遵循格式规范** - 三色标识系统
3. **准确显示调用关系** - 便于问题追踪
4. **保持信息精简** - 每模块一行关键日志

遵循这些规则，确保TradeFusion项目的日志系统保持一致性、可读性和可维护性。

---

**文档创建时间**: 2025-07-23  
**文档版本**: v1.0  
**规则状态**: 强制执行 ⚠️
