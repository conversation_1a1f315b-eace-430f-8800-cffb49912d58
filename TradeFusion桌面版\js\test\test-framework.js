/**
 * 轻量级浏览器内测试框架
 * 
 * 功能：为TradeFusion提供单元测试和集成测试能力
 * 职责：测试用例管理、断言、报告生成
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 */

class TestFramework {
    constructor() {
        this.tests = [];
        this.suites = new Map();
        this.results = {
            total: 0,
            passed: 0,
            failed: 0,
            skipped: 0,
            errors: []
        };
        this.currentSuite = null;
        this.isRunning = false;
    }

    /**
     * 创建测试套件
     */
    describe(suiteName, callback) {
        const suite = {
            name: suiteName,
            tests: [],
            beforeEach: null,
            afterEach: null,
            beforeAll: null,
            afterAll: null
        };
        
        this.suites.set(suiteName, suite);
        this.currentSuite = suite;
        
        try {
            callback();
        } catch (error) {
            console.error(`[TestFramework] 测试套件 ${suiteName} 初始化失败:`, error);
        }
        
        this.currentSuite = null;
    }

    /**
     * 定义测试用例
     */
    it(testName, callback, options = {}) {
        if (!this.currentSuite) {
            throw new Error('测试用例必须在describe块内定义');
        }

        const test = {
            name: testName,
            callback: callback,
            suite: this.currentSuite.name,
            skip: options.skip || false,
            timeout: options.timeout || 5000,
            result: null,
            error: null,
            duration: 0
        };

        this.currentSuite.tests.push(test);
        this.tests.push(test);
    }

    /**
     * 跳过测试用例
     */
    xit(testName, callback) {
        this.it(testName, callback, { skip: true });
    }

    /**
     * 设置前置钩子
     */
    beforeEach(callback) {
        if (this.currentSuite) {
            this.currentSuite.beforeEach = callback;
        }
    }

    afterEach(callback) {
        if (this.currentSuite) {
            this.currentSuite.afterEach = callback;
        }
    }

    beforeAll(callback) {
        if (this.currentSuite) {
            this.currentSuite.beforeAll = callback;
        }
    }

    afterAll(callback) {
        if (this.currentSuite) {
            this.currentSuite.afterAll = callback;
        }
    }

    /**
     * 断言工具
     */
    expect(actual) {
        return new Assertion(actual);
    }

    /**
     * 运行所有测试
     */
    async runTests() {
        if (this.isRunning) {
            console.warn('[TestFramework] 测试正在运行中...');
            return;
        }

        this.isRunning = true;
        this.resetResults();
        
        console.log('[TestFramework] 开始运行测试...');
        const startTime = performance.now();

        for (const [suiteName, suite] of this.suites) {
            await this.runSuite(suite);
        }

        const endTime = performance.now();
        const duration = endTime - startTime;

        this.isRunning = false;
        this.generateReport(duration);
        
        return this.results;
    }

    /**
     * 运行测试套件
     */
    async runSuite(suite) {
        console.log(`[TestFramework] 运行测试套件: ${suite.name}`);

        // 运行beforeAll
        if (suite.beforeAll) {
            try {
                await suite.beforeAll();
            } catch (error) {
                console.error(`[TestFramework] beforeAll失败:`, error);
            }
        }

        // 运行测试用例
        for (const test of suite.tests) {
            await this.runTest(test, suite);
        }

        // 运行afterAll
        if (suite.afterAll) {
            try {
                await suite.afterAll();
            } catch (error) {
                console.error(`[TestFramework] afterAll失败:`, error);
            }
        }
    }

    /**
     * 运行单个测试
     */
    async runTest(test, suite) {
        this.results.total++;

        if (test.skip) {
            test.result = 'skipped';
            this.results.skipped++;
            console.log(`⏭️  ${test.suite} > ${test.name} (跳过)`);
            return;
        }

        const startTime = performance.now();

        try {
            // 运行beforeEach
            if (suite.beforeEach) {
                await suite.beforeEach();
            }

            // 运行测试用例
            await Promise.race([
                test.callback(),
                new Promise((_, reject) => 
                    setTimeout(() => reject(new Error('测试超时')), test.timeout)
                )
            ]);

            // 运行afterEach
            if (suite.afterEach) {
                await suite.afterEach();
            }

            test.result = 'passed';
            test.duration = performance.now() - startTime;
            this.results.passed++;
            
            console.log(`✅ ${test.suite} > ${test.name} (${test.duration.toFixed(2)}ms)`);

        } catch (error) {
            test.result = 'failed';
            test.error = error;
            test.duration = performance.now() - startTime;
            this.results.failed++;
            this.results.errors.push({
                test: `${test.suite} > ${test.name}`,
                error: error.message,
                stack: error.stack
            });

            console.error(`❌ ${test.suite} > ${test.name}:`, error.message);
        }
    }

    /**
     * 重置测试结果
     */
    resetResults() {
        this.results = {
            total: 0,
            passed: 0,
            failed: 0,
            skipped: 0,
            errors: []
        };
    }

    /**
     * 生成测试报告
     */
    generateReport(duration) {
        const { total, passed, failed, skipped } = this.results;
        const passRate = total > 0 ? ((passed / total) * 100).toFixed(2) : 0;

        console.log('\n📊 测试报告');
        console.log('='.repeat(50));
        console.log(`总计: ${total} | 通过: ${passed} | 失败: ${failed} | 跳过: ${skipped}`);
        console.log(`通过率: ${passRate}% | 耗时: ${duration.toFixed(2)}ms`);
        
        if (failed > 0) {
            console.log('\n❌ 失败的测试:');
            this.results.errors.forEach(error => {
                console.log(`  ${error.test}: ${error.error}`);
            });
        }

        console.log('='.repeat(50));

        // 触发测试完成事件
        if (typeof window !== 'undefined') {
            window.dispatchEvent(new CustomEvent('test:complete', {
                detail: { results: this.results, duration }
            }));
        }
    }

    /**
     * 获取测试覆盖率（简化版）
     */
    getCoverage() {
        // 简化的覆盖率计算
        const modules = ['app', 'renderer', 'controller', 'connection-renderer', 'position-manager'];
        const testedModules = [...new Set(this.tests.map(test => test.suite.toLowerCase()))];
        const coverage = (testedModules.length / modules.length * 100).toFixed(2);
        
        return {
            modules: modules.length,
            tested: testedModules.length,
            coverage: `${coverage}%`
        };
    }
}

/**
 * 断言类
 */
class Assertion {
    constructor(actual) {
        this.actual = actual;
    }

    toBe(expected) {
        if (this.actual !== expected) {
            throw new Error(`期望 ${this.actual} 等于 ${expected}`);
        }
        return this;
    }

    toEqual(expected) {
        if (JSON.stringify(this.actual) !== JSON.stringify(expected)) {
            throw new Error(`期望 ${JSON.stringify(this.actual)} 深度等于 ${JSON.stringify(expected)}`);
        }
        return this;
    }

    toBeTruthy() {
        if (!this.actual) {
            throw new Error(`期望 ${this.actual} 为真值`);
        }
        return this;
    }

    toBeFalsy() {
        if (this.actual) {
            throw new Error(`期望 ${this.actual} 为假值`);
        }
        return this;
    }

    toBeNull() {
        if (this.actual !== null) {
            throw new Error(`期望 ${this.actual} 为 null`);
        }
        return this;
    }

    toBeUndefined() {
        if (this.actual !== undefined) {
            throw new Error(`期望 ${this.actual} 为 undefined`);
        }
        return this;
    }

    toContain(expected) {
        if (!this.actual.includes(expected)) {
            throw new Error(`期望 ${this.actual} 包含 ${expected}`);
        }
        return this;
    }

    toThrow(expectedError) {
        try {
            this.actual();
            throw new Error('期望函数抛出异常，但没有抛出');
        } catch (error) {
            if (expectedError && !error.message.includes(expectedError)) {
                throw new Error(`期望抛出包含 "${expectedError}" 的异常，实际抛出: ${error.message}`);
            }
        }
        return this;
    }

    toBeGreaterThan(expected) {
        if (this.actual <= expected) {
            throw new Error(`期望 ${this.actual} 大于 ${expected}`);
        }
        return this;
    }

    toBeGreaterThanOrEqual(expected) {
        if (this.actual < expected) {
            throw new Error(`期望 ${this.actual} 大于等于 ${expected}`);
        }
        return this;
    }

    toBeLessThan(expected) {
        if (this.actual >= expected) {
            throw new Error(`期望 ${this.actual} 小于 ${expected}`);
        }
        return this;
    }

    toBeLessThanOrEqual(expected) {
        if (this.actual > expected) {
            throw new Error(`期望 ${this.actual} 小于等于 ${expected}`);
        }
        return this;
    }
}

// 创建全局测试实例
const testFramework = new TestFramework();

// 导出全局函数
window.describe = testFramework.describe.bind(testFramework);
window.it = testFramework.it.bind(testFramework);
window.xit = testFramework.xit.bind(testFramework);
window.beforeEach = testFramework.beforeEach.bind(testFramework);
window.afterEach = testFramework.afterEach.bind(testFramework);
window.beforeAll = testFramework.beforeAll.bind(testFramework);
window.afterAll = testFramework.afterAll.bind(testFramework);
window.expect = testFramework.expect.bind(testFramework);
window.runTests = testFramework.runTests.bind(testFramework);

// 导出测试框架
window.TestFramework = TestFramework;
