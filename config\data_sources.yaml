# TradeFusion数据源配置文件
# 支持环境变量覆盖：使用 ${ENV_VAR_NAME:default_value} 语法

# 数据源配置
data_sources:
  # 大智慧数据基础路径（可通过环境变量DZH_DATA_PATH覆盖）
  dzh_base_path: "${DZH_DATA_PATH:E:/dzh2/USERDATA/SelfData}"
  
  # 数据目录映射
  directories:
    lbgd: "10连板高度"      # 连板高度数据
    ztdp: "12涨停评分"      # 涨停评分数据
    ztsj: "11涨停时间"      # 涨停时间数据
    yzb: "13一字板"         # 一字板数据
    tzb: "14T字板"          # T字板数据
    hjhs: "15黄金换手"      # 黄金换手数据
    cjje: "22成交金额"      # 成交金额数据

# 数据处理配置
processing:
  # 文件时效性检查（小时）
  file_timeout_hours: 12
  
  # 批处理配置
  batch_size: 1000
  
  # 时区设置
  timezone: "Asia/Shanghai"
  
  # 交易时间配置
  trading_time:
    market_open_hour: 9
    market_open_minute: 16

# {{ AURA-X: Modify - 修改为PostgreSQL数据库配置. Approval: 寸止(ID:1737734400). }}
# {{ AURA-X: Modify - 移除psycopg2不支持的字段，避免连接错误. Approval: 寸止(ID:1737734400). }}
# 数据库配置
database:
  # PostgreSQL数据库配置（仅包含连接参数）
  host: "localhost"
  port: 5432
  database: "tradefusion"
  user: "postgres"
  password: "ymjatTUU520"

# 数据库元信息（非连接参数）
database_meta:
  type: "postgresql"
  data_path: "数据库_PostgreSQL"

# 日志配置已迁移到：公共模块/日志系统/配置/logging.yaml
# 请使用新的统一日志配置文件
