# TradeFusion项目结构

> **重要说明**：本文档为TradeFusion项目的核心结构文档，需要实时动态维护更新。所有目录变更、文件增删都应同步更新此文档。

## 项目概述

TradeFusion是一个股票交易推荐系统，采用独立执行模式的数据流架构，实现从数据采集到推荐输出的完整业务流程。项目集成了环境清理工具、桌面版可视化界面和完善的模块管理系统。

## 项目结构原则

### 核心设计理念
- **数据流驱动**：目录结构严格按照数据处理流程排序
- **独立执行架构**：采用模块独立执行模式，支持桌面版统一管理
- **编号体系**：数据处理和数据库模块采用编号命名（数据1、数据2、数据库0-5）
- **动态维护**：结构文档与实际目录保持实时同步
- **层级清晰**：最大深度不超过4级

### 命名规范
- **数据处理模块**：数据1_本地采集、数据2_网络采集（编号+功能+数据源）
- **数据库模块**：数据库0_实体、数据库1_基础表等（编号+层级）
- **业务功能模块**：使用中文命名，便于业务理解
- **技术基础设施**：使用英文命名，符合技术规范

## 项目目录结构

### 数据流架构
```
数据1_本地采集 → 数据2_网络采集 → 数据3_网络采集_bk → 数据库0_实体模块 → 数据库1_基础表模块 → 数据库2_板块层统计 → 数据库3_个股所属板块层统计 → 数据库4_策略数据源 → 数据库5_策略成果数据 → 数据库写大智慧
```

### 六层数据库架构
- **数据库0_实体模块**：数据库连接、表结构定义、临时表管理
- **数据库1_基础表模块**：个股人气表等基础数据表
- **数据库2_板块层统计**：板块层统计数据（经统计处理后生成）
- **数据库3_个股所属板块层统计**：个股所属板块层统计数据
- **数据库4_策略数据源**：策略数据源模块，为交易策略提供数据支持
- **数据库5_策略成果数据**：策略执行结果和成果展示数据

### 完整目录结构
```
TradeFusion/
├── 启动TradeFusion.py      # 系统启动器（主启动脚本，已优化虚拟环境支持）
├── clear_today_data.py     # 当日数据清理脚本
├── 检查数据库状态.py       # 数据库状态检查工具
├── 检查今日数据.py         # 今日数据检查工具
├── 检查所有表状态.py       # 所有表状态检查工具
├── 日志监控器.py           # 日志监控工具
├── 数据库同步分析工具.py   # 数据库同步分析工具
├── 数据库同步执行工具.py   # 数据库同步执行工具
├── 数据库性能优化.py       # 数据库性能优化工具
├── requirements.txt        # Python依赖配置
├── README.md               # 项目说明文档

├── 环境清理工具/           # 环境清理工具集
│   ├── TradeFusion环境清理器.py # 统一环境清理器
│   ├── 交易日期判断.py     # 交易日期判断模块
│   ├── 数据库清理模块.py   # 数据库清理功能
│   ├── 数据库状态检查.py   # 数据库状态检查
│   └── 触发器事件清理.py   # 触发器事件清理

├── 备份_20250722_104731/   # 历史备份目录（包含字段级触发器系统）
├── 备份_20250722_105334/   # 历史备份目录（包含字段级触发器系统）

├── 数据1_本地采集/         # 本地数据采集
│   ├── 采集_本地数据.py    # 本地数据采集模块（DAT文件解析）
│   └── 基础涨停数据表.py   # 个股连板高度数据库操作类

├── 数据2_网络采集/         # 网络数据采集
│   ├── 浏览器管理器.py     # 浏览器管理工具类
│   ├── 个股人气表.py       # 个股人气数据管理
│   ├── 人气_东财采集.py    # 东方财富人气数据采集
│   ├── 人气_同花采集.py    # 同花顺人气数据采集
│   └── 人气临时表管理.py   # 临时表管理器

├── 数据3_网络采集_bk/      # 选股宝模块
│   ├── 选股宝抓取.py       # 选股宝数据采集
│   ├── 选股宝清洗.py       # 选股宝数据清洗处理
│   └── 个股解读_板块信息_关联表.py # 个股解读、板块信息、关联表数据库操作

├── 数据库0_实体模块/       # 数据库实体层
│   ├── 股票数据.db         # SQLite数据库文件
│   ├── 人气临时表结构.sql  # 临时表结构定义
│   ├── 优化索引.sql        # 数据库索引优化脚本
│   ├── 数据库结构报告生成器.py # 数据库结构报告生成器
│   ├── 数据库说明.md       # 数据库结构说明文档
│   ├── 执行索引优化.py     # 索引优化执行器
│   ├── 添加时间戳字段.py   # 时间戳字段添加工具
│   ├── 更新临时表引用.py   # 临时表引用更新工具
│   ├── 重命名临时表.py     # 临时表重命名工具
│   ├── 重命名英文表.py     # 英文表重命名工具
│   └── db_ready.flag       # 数据库初始化标志

├── 数据库1_基础表模块/     # 第一层数据库（空目录）
├── 数据库2_板块层统计/     # 第二层数据库
│   ├── 板块涨停表.py       # 板块涨停数据统计
│   ├── 板块精选.py         # 板块精选算法
│   ├── 个股板块关联表同步清理.py # 关联表数据清理
│   ├── 清理板块涨停表.py   # 板块涨停表清理
│   └── verify_calculation.py # 计算验证工具

├── 数据库3_个股所属板块层统计/ # 第三层数据库
│   ├── A3_所属板块评分表.py # 所属板块评分统计
│   ├── 个股接力表.py       # 个股接力数据分析
│   ├── 个股接力表计算.log  # 接力表计算日志
│   └── 接力值计算明细.log  # 接力值计算明细日志

├── 数据库4_策略数据源/     # 第四层数据库（空目录）
├── 数据库5_策略成果数据/   # 策略成果数据（空目录）
├── 数据库_1/               # 备用数据库目录1（空目录）
├── 数据库_2/               # 备用数据库目录2（空目录）
├── 数据库_3/               # 备用数据库目录3（空目录）

├── 数据库写大智慧/         # 大智慧数据输出
│   ├── A4_所属板块强度.py  # 所属板块强度DAT文件生成
│   ├── A4_所属板块强度.md  # 所属板块强度说明文档
│   ├── 接力.py             # 接力数据处理
│   ├── 查看DAT接力值.py    # DAT接力值查看工具
│   ├── 查询数据库接力值.py # 数据库接力值查询
│   ├── 查询最新接力数据.py # 最新接力数据查询
│   ├── 比较接力值差异.py   # 接力值差异比较
│   ├── 生成DZH3人气股票板块.py # DZH3人气板块文件生成
│   ├── 综合人气190.py      # 综合人气190数据处理
│   └── 选股宝_大智慧str.py # 选股宝STR文件生成
├── 公共模块/               # 公共支撑模块
│   ├── TradeFusion统一日志标准.py # TradeFusion统一日志标准
│   ├── 交易日期.py         # 交易日期计算工具
│   ├── 加代码前缀.py       # 股票代码前缀处理工具
│   ├── 日志系统性能分析器.py # 日志系统性能分析器
│   ├── 环境自动配置器.py   # 环境自动配置器（自动创建虚拟环境、安装依赖、配置IDE）
│   └── 配置管理.py         # 配置管理模块

├── 公用/                   # 公用工具目录（空目录）

├── TradeFusion桌面版/      # 桌面版数据流可视化界面（Electron版v2.0）
│   ├── index.html          # 主界面文件
│   ├── main.js             # Electron主进程文件
│   ├── package.json        # Electron项目配置
│   ├── config.js           # 模块配置文件（外置配置）
│   ├── README.md           # 桌面版使用说明（v2.0版本）
│   ├── 启动.bat            # 浏览器版启动脚本
│   ├── 启动Electron版.bat  # Electron版启动脚本
│   ├── 功能验证.html       # 功能验证页面
│   ├── flow-diagram.html   # 数据流程图页面
│   ├── flow-diagram-optimized.html # 优化版数据流程图
│   ├── test-runner.html    # 测试运行器
│   ├── js/                 # JavaScript模块目录
│   │   ├── app.js          # 主应用程序
│   │   ├── renderer.js     # 模块渲染器
│   │   ├── controller.js   # 模块控制器
│   │   └── batch.js        # 批处理生成器
│   ├── css/                # 样式文件目录
│   │   └── style.css       # 统一样式文件
│   ├── assets/             # 资源文件目录
│   ├── docs/               # 文档目录
│   ├── archive/            # 历史版本存档目录
│   └── node_modules/       # Electron依赖包目录

├── config/                 # 配置文件目录
│   └── data_sources.yaml  # 数据源配置文件

├── drivers/                # 驱动文件目录（空目录）
├── logs/                   # 日志文件目录
├── venv/                   # Python虚拟环境目录
│   ├── Include/            # 头文件目录
│   ├── Lib/                # 库文件目录
│   ├── Scripts/            # 脚本目录
│   └── pyvenv.cfg          # 虚拟环境配置

├── tests/                  # 测试文件目录
│   ├── test_tradefusion_logger.py # 日志系统功能测试
│   ├── test_performance.py # 日志系统性能测试
│   ├── test_exception_handling.py # 日志系统异常处理测试
│   ├── quick_performance_test.py # 快速性能测试
│   ├── 测试临时表方案.py   # 临时表方案完整性测试
│   ├── 测试选股宝临时表方案.py # 选股宝临时表方案测试
│   └── 测试选股宝完整流程.py # 选股宝完整流程测试

├── DOC/                    # 项目文档目录
│   ├── 项目结构.md         # 项目结构文档（本文档）
│   ├── AURA 协议.md        # AURA协议文档
│   ├── TradeFusion当前项目结构.md # 当前项目结构分析
│   ├── TradeFusion数据流依赖关系分析.md # 数据流依赖关系分析
│   ├── TradeFusion个股人气表移动修正报告.md # 个股人气表移动修正报告
│   ├── TradeFusion日志系统规范.md # 日志系统规范文档
│   ├── TradeFusion桌面版业务逻辑分析报告.md # 桌面版业务逻辑分析
│   ├── VS Code调试配置优化方案.md # VS Code调试配置优化方案
│   ├── 中文文件名批处理问题解决方案.md # 中文编码问题解决方案
│   ├── 文件名称变更表.md   # 文件变更记录
│   ├── 新文件检查.md       # 新文件检查规范
│   └── 数据流思维导图/     # 数据流思维导图目录

├── __pycache__/            # Python缓存目录
└── README.md               # 项目说明文档
```

## 目录维护规范

### 文件管理要求
1. **新增文件**：必须同步更新本文档中对应目录的说明
2. **删除文件**：必须从本文档中移除相关说明
3. **重命名文件**：必须同步更新本文档中的文件名
4. **目录变更**：任何目录结构调整都必须先更新本文档

### 维护责任
- **开发人员**：负责代码文件的增删改时同步更新文档
- **项目管理员**：负责定期检查文档与实际目录的一致性
- **AUGMENT AI**：协助维护文档的实时更新和结构优化

### 更新频率
- **实时更新**：文件增删改时立即更新
- **定期检查**：每周检查一次文档与实际目录的一致性
- **版本同步**：每次项目版本发布前必须确保文档准确性

## TradeFusion系统架构

### 系统概述
TradeFusion项目采用独立执行模式的数据流处理架构，通过桌面版可视化界面实现模块的统一管理和监控。

### 核心特点
- **独立执行模式**：每个模块可独立运行，无复杂依赖关系
- **桌面版管理**：通过Electron桌面应用统一管理所有模块
- **环境清理工具**：完善的环境清理和维护工具集
- **三层数据架构**：数据采集层 → 数据加工层 → 应用输出层

### 执行架构
```
数据源 → 数据采集模块 → 数据库存储 → 数据处理模块 → 输出模块 → 终端应用
```

1. **数据采集**：本地DAT文件和网络数据采集
2. **数据存储**：SQLite数据库统一存储
3. **数据处理**：板块统计、个股分析等算法处理
4. **数据输出**：生成大智慧等终端软件所需格式

### 启动方式
```bash
# 启动主系统
python 启动TradeFusion.py

# 启动桌面版管理界面
启动Electron版.bat

# 环境清理
python 环境清理工具/TradeFusion环境清理器.py
```

## TradeFusion环境清理工具

### 工具概述
TradeFusion环境清理工具集提供完整的环境清理功能，确保测试和开发环境的干净状态。

### 核心组件
- **TradeFusion环境清理器.py**：统一环境清理器，整合所有清理功能
- **数据库清理模块.py**：数据库数据清理功能
- **数据库状态检查.py**：数据库状态检查工具
- **交易日期判断.py**：交易日期判断模块
- **触发器事件清理.py**：触发器事件清理功能

### 清理流程
1. **进程清理**：结束所有Python进程，避免文件锁定
2. **日志清理**：清空logs目录下的所有日志文件
3. **数据库清理**：清理当日数据，包括基础表、统计表、临时表
4. **触发器清理**：清理积压的触发器事件
5. **状态验证**：验证清理结果，确保环境干净

### 使用方法
```bash
# 完整环境清理
python 环境清理工具/TradeFusion环境清理器.py

# 仅清理当日数据
python clear_today_data.py

# 检查数据库状态
python 检查数据库状态.py
```

## TradeFusion桌面版详细说明

### 架构特点（Electron版v2.0）
- **Electron桌面应用**：真正的桌面应用，无浏览器依赖
- **系统级权限**：可以直接控制系统进程，启动/停止Python脚本
- **数据流可视化**：26个模块的思维导图式展示
- **模块管理**：支持Python模块的启动/停止控制
- **进程管理**：实时监控Python进程状态，自动清理退出进程
- **位置持久化**：模块卡片位置保存和恢复功能
- **实时日志**：模块运行日志实时显示

### 核心文件说明
- **index.html**：主界面文件，包含所有可视化功能
- **main.js**：Electron主进程文件，处理系统级操作
- **package.json**：Electron项目配置文件
- **config.js**：模块配置文件（外置配置）
- **启动Electron版.bat**：Electron版一键启动脚本
- **启动.bat**：浏览器版启动脚本（兼容性保留）
- **README.md**：使用说明文档（v2.0版本）

### 技术特点
- **真正的一键启动**：点击启动按钮，Python模块直接启动
- **桌面应用体验**：独立窗口，专业界面
- **完善的错误处理**：完善的错误提示和异常处理机制
- **功能完整**：包含完整的26个模块管理功能
- **双版本支持**：同时支持Electron版和浏览器版

### 版本特性（v2.0.0）
1. ✅ **Electron升级**：从浏览器版升级为真正的桌面应用
2. ✅ **一键启动**：真正实现Python模块的一键启动功能
3. ✅ **进程管理**：实时监控和管理Python进程状态
4. ✅ **架构简化**：从15个文件简化为8个核心文件
5. ✅ **目录整理**：从7层目录简化为3层清晰结构
6. ✅ **文档整理**：所有文档整理到docs/目录
7. ✅ **配置管理**：恢复外部配置文件，避免CORS问题
8. ✅ **双版本兼容**：保持浏览器版兼容性的同时提供Electron版

### 启动方式
```bash
# Electron版启动（推荐）
启动Electron版.bat

# 浏览器版启动（兼容性）
启动.bat

# 直接启动
npm start
```

## 使用说明

本文档作为项目结构的权威参考，所有团队成员和AI助手都应：
1. 优先参考本文档了解项目结构
2. 严格按照本文档的规范进行文件操作
3. 及时维护文档内容的准确性
4. 遵循既定的命名规范和目录层级
5. 使用MCP工具进行项目结构分析和维护

## MCP工具使用说明

### 项目结构分析工具链
- **`codebase-retrieval`**：分析项目代码结构和模块依赖关系
- **`view`**：查看目录和文件结构
- **`list_tables_TradeFusion_Database`**：获取数据库表结构信息
- **`记忆`**：加载和管理项目规则与偏好

### 更新维护流程
1. 使用`codebase-retrieval`分析项目变更
2. 使用`view`确认目录结构
3. 使用`记忆`查询相关规则
4. 更新项目结构文档
5. 通过`寸止`确认更新计划
