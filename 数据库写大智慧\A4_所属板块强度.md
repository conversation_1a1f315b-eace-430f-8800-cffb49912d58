# A4_所属板块强度.py 说明文档

## 1. 脚本功能简介

本脚本用于将数据库中"所属板块评分表"的板块强度数据，按大智慧（DZH）专用DAT格式写入指定目录（E:/dzh2/USERDATA/SelfData/所属板块强度）。每个股票代码对应一个DAT文件，文件内容为每日板块强度分数，便于大智慧等行情软件读取和展示。

## 2. 输入输出说明

- **输入**：
  - SQLite数据库：E:/YMJATTUU/数据库/股票数据.db
  - 数据表：所属板块评分表（字段：日期、股票代码、所属板块评分）
- **输出**：
  - 目录：E:/dzh2/USERDATA/SelfData/所属板块强度/
  - 每只股票一个DAT文件，文件名为"股票代码.dat"，内容为二进制格式（时间戳+板块强度分数）

## 3. 详细逻辑流程

### 步骤一：获取当前交易日
- 通过公共模块.交易日期.get_trading_date() 获取当前交易日，转换为日期对象
- 生成北京时间8点的UTC时间戳，作为当日数据的唯一标识

### 步骤二：连接数据库并准备名单表
- 连接数据库，创建临时名单表 dat_processing_list（如不存在）
- 清理名单表中过期数据（只保留当日）

### 步骤三：读取当日板块强度数据
- 查询"所属板块评分表"中，当前交易日且所属板块评分不为空的所有股票（股票代码、分数）

### 步骤四：写入/更新DAT文件
- 对每只股票：
  - 生成对应的DAT文件名（股票代码.dat）
  - 若文件已存在且有当日记录，则覆盖分数
  - 否则追加新记录（时间戳+分数）
  - 更新名单表，记录已处理股票

### 步骤五：清零未处理股票
- 查询名单表，找出名单表中有但本次未处理的股票（即今日无有效分数的股票）
- 对这些股票的DAT文件写入当日时间戳+0.0，实现分数清零

### 步骤六：提交并关闭数据库
- 提交所有更改，关闭数据库连接
- 输出处理结果（成功/失败、日期、处理条数）

## 4. 依赖环境

- Python 3.7+
- 依赖包：pytz、sqlite3、struct、datetime、pathlib
- 项目自带模块：公共模块.交易日期
- 运行环境需有对E:/dzh2/USERDATA/SelfData/所属板块强度和数据库目录的读写权限

## 5. 常见问题与注意事项

- **目录不存在**：请确保E:/dzh2/USERDATA/SelfData/所属板块强度目录存在，否则需手动创建
- **数据库路径**：如数据库路径或表结构有变动，请同步修改脚本相关参数
- **时间戳问题**：脚本采用北京时间8点的UTC时间戳，确保与大智慧数据格式兼容
- **异常处理**：脚本对文件和数据库操作均有异常捕获，遇到错误会跳过该股票并继续处理
- **名单表清理**：脚本自动清理过期名单表数据，避免历史数据干扰

## 6. 使用方法与输出示例

### 命令行直接运行

```bash
python A4_所属板块强度.py
```

### 运行结果示例

```
板块强度DAT文件更新 2025-05-21 成功 (更新/清零: 120 条)
```
或
```
板块强度DAT文件更新 获取交易日失败: ... 失败
```

### 集成到调度脚本

可在主调度脚本（如qd/RQ.py）中通过os.system或subprocess调用本脚本，实现自动化批量处理。

## 7. 与其他脚本的配合说明

本脚本依赖"所属板块评分表"数据，建议先运行相关评分生成脚本，确保数据为最新，再运行本脚本完成DAT文件写入。

---
如需自定义输出目录、数据库路径等参数，请直接修改脚本开头相关变量。 