/**
 * PositionManager - 模块位置管理器
 * 
 * 功能：保存和恢复模块位置，支持本地存储
 * 职责：模块位置的持久化管理
 * 
 * <AUTHOR> Team
 * @version 2.0.0 (Electron版)
 */

class PositionManager {
    constructor(app, config) {
        this.app = app;
        this.config = config;
        this.storageKey = 'tradefusion-module-positions';
        this.hasUnsavedChanges = false;

        this.bindEvents();
        // 不在构造函数中加载位置，等待DOM创建完成后再加载
        this.loadSavedPositionsToConfig();
    }

    /**
     * 绑定事件监听
     */
    bindEvents() {
        // 监听模块位置变化
        this.app.on('module:position-changed', (data) => {
            this.updateModulePosition(data.moduleId, data.position);
        });

        // 监听保存请求
        this.app.on('position:save', () => {
            this.savePositions();
        });

        // 监听重置请求
        this.app.on('position:reset', () => {
            this.resetPositions();
        });

        // 监听加载请求
        this.app.on('position:load', () => {
            this.loadSavedPositions();
        });

        // 键盘快捷键：Ctrl+S 保存位置
        document.addEventListener('keydown', (e) => {
            if (e.ctrlKey && e.key === 's') {
                e.preventDefault();
                this.savePositions();
            }
        });
    }

    /**
     * 更新模块位置（内存中）
     */
    updateModulePosition(moduleId, position) {
        if (this.config.modules[moduleId]) {
            this.config.modules[moduleId].position = { ...position };
            this.hasUnsavedChanges = true;
            
            // 更新保存按钮状态
            this.updateSaveButtonState();
            
            console.log(`[PositionManager] 模块 ${moduleId} 位置更新: (${position.x}, ${position.y})`);
        }
    }

    /**
     * 保存位置到本地存储
     */
    savePositions() {
        try {
            const positions = {};
            
            // 收集所有模块的当前位置
            Object.keys(this.config.modules).forEach(moduleId => {
                const moduleConfig = this.config.modules[moduleId];
                positions[moduleId] = {
                    x: moduleConfig.position.x,
                    y: moduleConfig.position.y
                };
            });

            // 保存到localStorage
            localStorage.setItem(this.storageKey, JSON.stringify({
                positions: positions,
                timestamp: new Date().toISOString(),
                version: '2.0.0'
            }));

            this.hasUnsavedChanges = false;
            this.updateSaveButtonState();

            // 显示保存成功通知
            this.app.showNotification(
                '位置保存成功', 
                `已保存 ${Object.keys(positions).length} 个模块的位置信息`, 
                'success'
            );

            console.log(`[PositionManager] 位置保存成功，共 ${Object.keys(positions).length} 个模块`);

        } catch (error) {
            console.error('[PositionManager] 位置保存失败:', error);
            this.app.showNotification(
                '位置保存失败', 
                `保存失败: ${error.message}`, 
                'error'
            );
        }
    }

    /**
     * 仅加载位置数据到配置中（不更新DOM）
     */
    loadSavedPositionsToConfig() {
        try {
            const savedData = localStorage.getItem(this.storageKey);

            if (!savedData) {
                console.log('[PositionManager] 没有找到保存的位置信息');
                return false;
            }

            const data = JSON.parse(savedData);
            const positions = data.positions;

            if (!positions) {
                console.log('[PositionManager] 保存的数据格式无效');
                return false;
            }

            let loadedCount = 0;

            // 只更新配置中的位置，不更新DOM
            Object.keys(positions).forEach(moduleId => {
                if (this.config.modules[moduleId]) {
                    const savedPosition = positions[moduleId];
                    this.config.modules[moduleId].position = {
                        x: savedPosition.x,
                        y: savedPosition.y
                    };
                    loadedCount++;
                }
            });

            console.log(`[PositionManager] 位置配置加载成功，共 ${loadedCount} 个模块`);
            return true;

        } catch (error) {
            console.error('[PositionManager] 位置配置加载失败:', error);
            return false;
        }
    }

    /**
     * 从本地存储加载位置（包含DOM更新）
     */
    loadSavedPositions() {
        try {
            const savedData = localStorage.getItem(this.storageKey);
            
            if (!savedData) {
                console.log('[PositionManager] 没有找到保存的位置信息');
                return false;
            }

            const data = JSON.parse(savedData);
            const positions = data.positions;
            
            if (!positions) {
                console.log('[PositionManager] 保存的数据格式无效');
                return false;
            }

            let loadedCount = 0;

            // 应用保存的位置
            Object.keys(positions).forEach(moduleId => {
                if (this.config.modules[moduleId]) {
                    const savedPosition = positions[moduleId];
                    this.config.modules[moduleId].position = {
                        x: savedPosition.x,
                        y: savedPosition.y
                    };
                    
                    // 更新DOM元素位置
                    const moduleElement = document.getElementById(`module-${moduleId}`);
                    if (moduleElement) {
                        moduleElement.style.left = `${savedPosition.x}px`;
                        moduleElement.style.top = `${savedPosition.y}px`;
                    }
                    
                    loadedCount++;
                }
            });

            this.hasUnsavedChanges = false;
            this.updateSaveButtonState();

            // 显示加载成功通知
            this.app.showNotification(
                '位置加载成功', 
                `已恢复 ${loadedCount} 个模块的位置信息`, 
                'success'
            );

            console.log(`[PositionManager] 位置加载成功，共 ${loadedCount} 个模块`);
            return true;

        } catch (error) {
            console.error('[PositionManager] 位置加载失败:', error);
            this.app.showNotification(
                '位置加载失败', 
                `加载失败: ${error.message}`, 
                'error'
            );
            return false;
        }
    }

    /**
     * 重置位置到默认值
     */
    resetPositions() {
        try {
            // 清除本地存储
            localStorage.removeItem(this.storageKey);

            // 重新加载默认配置
            // 这里需要重新加载原始配置，暂时使用固定位置
            const defaultPositions = this.getDefaultPositions();
            
            Object.keys(defaultPositions).forEach(moduleId => {
                if (this.config.modules[moduleId]) {
                    this.config.modules[moduleId].position = defaultPositions[moduleId];
                    
                    // 更新DOM元素位置
                    const moduleElement = document.getElementById(`module-${moduleId}`);
                    if (moduleElement) {
                        moduleElement.style.left = `${defaultPositions[moduleId].x}px`;
                        moduleElement.style.top = `${defaultPositions[moduleId].y}px`;
                    }
                }
            });

            this.hasUnsavedChanges = false;
            this.updateSaveButtonState();

            this.app.showNotification(
                '位置重置成功', 
                '所有模块位置已重置为默认值', 
                'success'
            );

            console.log('[PositionManager] 位置重置成功');

        } catch (error) {
            console.error('[PositionManager] 位置重置失败:', error);
            this.app.showNotification(
                '位置重置失败', 
                `重置失败: ${error.message}`, 
                'error'
            );
        }
    }

    /**
     * 获取默认位置配置
     */
    getDefaultPositions() {
        // 这里返回一个基本的网格布局（适配缩小后的卡片尺寸）
        const positions = {};
        const moduleIds = Object.keys(this.config.modules);
        const cols = 4; // 每行4个模块
        const moduleWidth = 196; // 对应缩小后：168px + 28px间距
        const moduleHeight = 140; // 对应缩小后的高度
        const padding = 20;

        moduleIds.forEach((moduleId, index) => {
            const row = Math.floor(index / cols);
            const col = index % cols;

            positions[moduleId] = {
                x: col * (moduleWidth + padding) + 50,
                y: row * (moduleHeight + padding) + 50
            };
        });

        return positions;
    }

    /**
     * 更新保存按钮状态
     */
    updateSaveButtonState() {
        const saveBtn = document.getElementById('save-positions-btn');
        if (saveBtn) {
            if (this.hasUnsavedChanges) {
                saveBtn.textContent = '保存位置 *';
                saveBtn.classList.add('has-changes');
            } else {
                saveBtn.textContent = '保存位置';
                saveBtn.classList.remove('has-changes');
            }
        }
    }

    /**
     * 检查是否有未保存的更改
     */
    hasUnsavedChanges() {
        return this.hasUnsavedChanges;
    }

    /**
     * 获取保存的位置信息
     */
    getSavedPositionsInfo() {
        try {
            const savedData = localStorage.getItem(this.storageKey);
            if (savedData) {
                const data = JSON.parse(savedData);
                return {
                    timestamp: data.timestamp,
                    version: data.version,
                    moduleCount: Object.keys(data.positions || {}).length
                };
            }
        } catch (error) {
            console.error('[PositionManager] 获取保存信息失败:', error);
        }
        return null;
    }
}

// 导出位置管理器类
window.PositionManager = PositionManager;
