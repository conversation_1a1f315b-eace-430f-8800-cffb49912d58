/**
 * TradeFusion 数据流程图模块
 * 负责加载和显示Mermaid流程图
 */
class FlowDiagramManager {
    constructor() {
        this.isVisible = false;
        this.mermaidInitialized = false;
        this.diagramData = null;
        
        this.initializeElements();
        this.bindEvents();
        this.initializeMermaid();
    }

    initializeElements() {
        this.showBtn = document.getElementById('show-flow-diagram-btn');
        this.hideBtn = document.getElementById('hide-flow-diagram-btn');
        this.container = document.getElementById('flow-diagram-container');
        this.closeBtn = document.getElementById('close-flow-diagram');
        this.diagramDiv = document.getElementById('mermaid-diagram');
    }

    bindEvents() {
        if (this.showBtn) {
            this.showBtn.addEventListener('click', () => this.showDiagram());
        }
        
        if (this.hideBtn) {
            this.hideBtn.addEventListener('click', () => this.hideDiagram());
        }
        
        if (this.closeBtn) {
            this.closeBtn.addEventListener('click', () => this.hideDiagram());
        }

        // ESC键关闭
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape' && this.isVisible) {
                this.hideDiagram();
            }
        });
    }

    async initializeMermaid() {
        try {
            if (typeof mermaid !== 'undefined') {
                mermaid.initialize({
                    startOnLoad: false,
                    theme: 'default',
                    flowchart: {
                        useMaxWidth: true,
                        htmlLabels: true,
                        curve: 'basis'
                    },
                    mindmap: {
                        useMaxWidth: true,
                        padding: 10
                    }
                });
                this.mermaidInitialized = true;
                console.log('[FlowDiagram] Mermaid初始化成功');
            } else {
                console.warn('[FlowDiagram] Mermaid库未加载');
            }
        } catch (error) {
            console.error('[FlowDiagram] Mermaid初始化失败:', error);
        }
    }

    async loadDiagramData() {
        try {
            // 加载保存的流程图文件
            const response = await fetch('../TradeFusion数据流程图_最新版.mmd');
            if (response.ok) {
                this.diagramData = await response.text();
                console.log('[FlowDiagram] 流程图数据加载成功');
                return true;
            } else {
                console.warn('[FlowDiagram] 流程图文件未找到，使用默认数据');
                this.diagramData = this.getDefaultDiagramData();
                return true;
            }
        } catch (error) {
            console.error('[FlowDiagram] 加载流程图数据失败:', error);
            this.diagramData = this.getDefaultDiagramData();
            return true;
        }
    }

    getDefaultDiagramData() {
        return `graph TD
    subgraph "第1层：数据采集层"
        A1[采集_本地数据<br/>DAT文件解析]
        A2[人气_东财采集<br/>东方财富爬虫]
        A3[人气_同花采集<br/>同花顺爬虫]
        A4[选股宝抓取<br/>网站数据采集]
    end
    
    subgraph "第2层：数据加工层"
        B1[板块涨停表<br/>SUM涨停评分]
        B2[板块精选<br/>SMA算法]
        B3[个股接力表<br/>4表关联算法]
    end
    
    subgraph "第3层：应用输出层"
        C1[综合人气190<br/>DAT文件]
        C2[选股宝STR文件<br/>大智慧格式]
        C3[接力数据<br/>DAT文件]
    end
    
    A1 --> B1
    A2 --> B1
    A3 --> B1
    A4 --> B1
    B1 --> B2
    B2 --> B3
    B1 --> C1
    B2 --> C2
    B3 --> C3`;
    }

    async renderDiagram() {
        if (!this.mermaidInitialized) {
            console.warn('[FlowDiagram] Mermaid未初始化');
            return false;
        }

        if (!this.diagramData) {
            await this.loadDiagramData();
        }

        try {
            // 清空容器
            this.diagramDiv.innerHTML = '';
            
            // 创建唯一ID
            const diagramId = 'mermaid-' + Date.now();
            
            // 渲染图表
            const { svg } = await mermaid.render(diagramId, this.diagramData);
            this.diagramDiv.innerHTML = svg;
            
            console.log('[FlowDiagram] 流程图渲染成功');
            return true;
        } catch (error) {
            console.error('[FlowDiagram] 流程图渲染失败:', error);
            this.diagramDiv.innerHTML = `
                <div style="display: flex; align-items: center; justify-content: center; height: 100%; color: #ef4444;">
                    <div style="text-align: center;">
                        <div style="font-size: 48px; margin-bottom: 16px;">⚠️</div>
                        <div style="font-size: 18px; margin-bottom: 8px;">流程图加载失败</div>
                        <div style="font-size: 14px; color: #64748b;">${error.message}</div>
                    </div>
                </div>
            `;
            return false;
        }
    }

    async showDiagram() {
        if (this.isVisible) return;

        try {
            // 显示容器
            this.container.style.display = 'block';
            this.isVisible = true;

            // 渲染图表
            await this.renderDiagram();

            // 更新按钮状态
            if (this.showBtn) this.showBtn.disabled = true;
            if (this.hideBtn) this.hideBtn.disabled = false;

            console.log('[FlowDiagram] 流程图已显示');
        } catch (error) {
            console.error('[FlowDiagram] 显示流程图失败:', error);
            this.hideDiagram();
        }
    }

    hideDiagram() {
        if (!this.isVisible) return;

        this.container.style.display = 'none';
        this.isVisible = false;

        // 更新按钮状态
        if (this.showBtn) this.showBtn.disabled = false;
        if (this.hideBtn) this.hideBtn.disabled = true;

        console.log('[FlowDiagram] 流程图已隐藏');
    }

    // 刷新流程图数据
    async refreshDiagram() {
        this.diagramData = null;
        if (this.isVisible) {
            await this.renderDiagram();
        }
    }

    // 获取当前状态
    getStatus() {
        return {
            visible: this.isVisible,
            initialized: this.mermaidInitialized,
            hasData: !!this.diagramData
        };
    }
}

// 导出到全局
window.FlowDiagramManager = FlowDiagramManager;
