#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
验证浏览器路径脚本
确认Playwright是否使用项目本地浏览器
"""

import os
import sys
from pathlib import Path

def verify_browser_path():
    """验证浏览器路径"""
    
    print("=" * 60)
    print("🔍 TradeFusion浏览器路径验证")
    print("=" * 60)
    
    # 设置项目根目录
    project_root = Path(__file__).parent
    browsers_dir = project_root / 'browsers'
    
    print(f"📁 项目根目录: {project_root}")
    print(f"📁 浏览器目录: {browsers_dir}")
    print(f"📁 浏览器目录存在: {browsers_dir.exists()}")
    
    # 设置环境变量
    os.environ['PLAYWRIGHT_BROWSERS_PATH'] = str(browsers_dir)
    print(f"🌍 环境变量设置: PLAYWRIGHT_BROWSERS_PATH={browsers_dir}")
    
    try:
        # 导入Playwright
        from playwright.sync_api import sync_playwright
        
        print(f"\n🚀 启动Playwright...")
        p = sync_playwright().start()
        
        # 获取浏览器路径
        chromium_path = p.chromium.executable_path
        firefox_path = p.firefox.executable_path if hasattr(p.firefox, 'executable_path') else "未安装"
        webkit_path = p.webkit.executable_path if hasattr(p.webkit, 'executable_path') else "未安装"
        
        print(f"\n📋 浏览器路径信息:")
        print(f"🔹 Chromium: {chromium_path}")
        print(f"🔹 Firefox: {firefox_path}")
        print(f"🔹 WebKit: {webkit_path}")
        
        # 验证是否使用项目本地浏览器
        if str(browsers_dir) in chromium_path:
            print(f"\n✅ 成功！使用项目本地Chromium浏览器")
        else:
            print(f"\n❌ 警告！仍在使用系统默认Chromium浏览器")
            print(f"   期望路径包含: {browsers_dir}")
            print(f"   实际路径: {chromium_path}")
        
        # 检查浏览器文件是否存在
        chromium_file = Path(chromium_path)
        if chromium_file.exists():
            print(f"✅ 浏览器文件存在: {chromium_file}")
            print(f"📊 文件大小: {chromium_file.stat().st_size / 1024 / 1024:.1f} MB")
        else:
            print(f"❌ 浏览器文件不存在: {chromium_file}")
        
        # 测试浏览器启动
        print(f"\n🧪 测试浏览器启动...")
        browser = p.chromium.launch(headless=True)
        page = browser.new_page()
        page.goto("https://www.baidu.com")
        title = page.title()
        print(f"✅ 浏览器启动成功，页面标题: {title}")
        
        browser.close()
        p.stop()
        
        print(f"\n🎉 验证完成！")
        
    except Exception as e:
        print(f"\n❌ 验证过程中出现错误: {e}")
        import traceback
        traceback.print_exc()

def main():
    """主函数"""
    try:
        verify_browser_path()
    except KeyboardInterrupt:
        print(f"\n⚠️ 用户中断验证")
    except Exception as e:
        print(f"\n❌ 验证过程中出现未知错误: {e}")

if __name__ == "__main__":
    main()
