/**
 * ModuleRenderer - 模块渲染器（简化版）
 * 
 * 功能：专注于模块节点的渲染和UI交互
 * 职责：单一职责 - 只负责UI渲染和用户交互
 * 
 * <AUTHOR> Team
 * @version 2.0.0 (简化版)
 */

class ModuleRenderer {
    constructor(app, config) {
        this.app = app;
        this.config = config;
        this.container = null;
        this.moduleElements = new Map();
        this.eventManager = EventUtils.createEventManager();

        // 绑定事件监听器
        this.bindEvents();
    }

    /**
     * 初始化渲染器
     */
    init(container) {
        this.container = container;
        this.render();
    }

    /**
     * 绑定事件监听器
     */
    bindEvents() {
        try {
            // 模块状态变化
            this.app.on('module:status-changed', (data) => {
                this.updateModuleStatus(data.moduleId, data.status);
            });

            // 模块启动/停止事件
            this.app.on('module:started', (data) => {
                this.updateModuleStatus(data.moduleId, 'running');
            });

            this.app.on('module:stopped', (data) => {
                this.updateModuleStatus(data.moduleId, 'idle');
            });

            // 日志相关事件
            this.app.on('module:log', (data) => {
                this.addModuleLog(data.moduleId, data.message, data.type || 'info');
            });

            this.app.on('module:clear-logs', (data) => {
                this.clearModuleLogs(data.moduleId);
            });
        } catch (error) {
            this.app.handleError(error, 'ModuleRenderer事件绑定失败');
        }
    }

    /**
     * 渲染所有模块节点
     */
    render() {
        if (!this.container || !this.config || !this.config.modules) {
            console.error('[ModuleRenderer] 缺少容器或配置信息');
            return;
        }

        // 清空容器
        this.container.innerHTML = '';

        // 渲染每个模块
        Object.keys(this.config.modules).forEach(moduleId => {
            const moduleConfig = this.config.modules[moduleId];
            this.renderModule(moduleId, moduleConfig);
        });

        console.log('[ModuleRenderer] 所有模块节点渲染完成');

        this.app.emit('ui:modules-rendered', {
            count: Object.keys(this.config.modules).length
        });
    }

    /**
     * 更新切换按钮状态
     */
    updateToggleButton(button, status) {
        if (!button.classList.contains('toggle-btn')) return;

        const isRunning = status === 'running';
        const isStarting = status === 'starting';
        const isStopping = status === 'stopping';

        // 更新按钮类名
        button.classList.remove('running', 'stopped');
        button.classList.add(isRunning ? 'running' : 'stopped');

        // 更新按钮文本
        if (isStarting) {
            button.textContent = '启动中...';
            button.disabled = true;
        } else if (isStopping) {
            button.textContent = '停止中...';
            button.disabled = true;
        } else {
            button.textContent = isRunning ? '停止' : '启动';
            button.disabled = false;
            button.dataset.action = isRunning ? 'stop' : 'start';
        }
    }

    /**
     * 渲染单个模块节点
     */
    renderModule(moduleId, moduleConfig) {
        const moduleElement = DOMUtils.createElement('div', {
            className: `module-node ${moduleConfig.type}`,
            id: `module-${moduleId}`,
            dataset: {
                type: moduleConfig.type,
                moduleId: moduleId
            }
        });

        // 设置位置
        DOMUtils.setStyle(moduleElement, {
            left: `${moduleConfig.position.x}px`,
            top: `${moduleConfig.position.y}px`
        });

        // 设置模块内容
        moduleElement.innerHTML = this.generateModuleHTML(moduleId, moduleConfig);

        // 为数据库模块添加调整大小功能
        if (moduleConfig.type === 'database') {
            this.addResizeHandles(moduleElement, moduleId);
        }

        // 绑定模块事件
        this.bindModuleEvents(moduleElement, moduleId, moduleConfig);

        // 添加到容器
        this.container.appendChild(moduleElement);

        // 存储元素引用
        this.moduleElements.set(moduleId, moduleElement);

        console.log(`[ModuleRenderer] 模块 ${moduleId} 渲染完成`);
    }

    /**
     * 为数据库模块添加调整大小手柄
     */
    addResizeHandles(moduleElement, moduleId) {
        try {
            // 创建调整大小手柄
            const rightHandle = DOMUtils.createElement('div', {
                className: 'resize-handle right'
            });

            const bottomHandle = DOMUtils.createElement('div', {
                className: 'resize-handle bottom'
            });

            const cornerHandle = DOMUtils.createElement('div', {
                className: 'resize-handle corner'
            });

            // 添加手柄到模块
            moduleElement.appendChild(rightHandle);
            moduleElement.appendChild(bottomHandle);
            moduleElement.appendChild(cornerHandle);

            // 绑定调整大小事件
            this.bindResizeEvents(moduleElement, moduleId, rightHandle, bottomHandle, cornerHandle);

        } catch (error) {
            this.app.handleError(error, `为模块 ${moduleId} 添加调整大小手柄失败`);
        }
    }

    /**
     * 绑定调整大小事件
     */
    bindResizeEvents(moduleElement, moduleId, rightHandle, bottomHandle, cornerHandle) {
        const self = this; // 保存this引用
        let isResizing = false;
        let resizeType = '';
        let startX = 0;
        let startY = 0;
        let startWidth = 0;
        let startHeight = 0;

        const startResize = (e, type) => {
            try {
                isResizing = true;
                resizeType = type;

                // 使用全局坐标，避免相对坐标计算错误
                startX = e.clientX || (e.touches && e.touches[0] ? e.touches[0].clientX : 0);
                startY = e.clientY || (e.touches && e.touches[0] ? e.touches[0].clientY : 0);

                const rect = moduleElement.getBoundingClientRect();
                startWidth = rect.width;
                startHeight = rect.height;

                EventUtils.preventDefault(e);
                EventUtils.stopPropagation(e);

                // 添加全局事件监听
                document.addEventListener('mousemove', handleResize, { passive: false });
                document.addEventListener('mouseup', stopResize);
                document.addEventListener('touchmove', handleResize, { passive: false });
                document.addEventListener('touchend', stopResize);

                // 添加视觉反馈
                DOMUtils.addClass(moduleElement, 'resizing');

                console.log(`[ModuleRenderer] 开始调整模块 ${moduleId} 大小，类型: ${type}`);
            } catch (error) {
                self.app.handleError(error, `开始调整模块 ${moduleId} 大小失败`);
            }
        };

        const handleResize = (e) => {
            if (!isResizing) return;

            try {
                // 使用全局坐标
                const currentX = e.clientX || (e.touches && e.touches[0] ? e.touches[0].clientX : startX);
                const currentY = e.clientY || (e.touches && e.touches[0] ? e.touches[0].clientY : startY);

                const deltaX = currentX - startX;
                const deltaY = currentY - startY;

                let newWidth = startWidth;
                let newHeight = startHeight;

                if (resizeType.includes('right')) {
                    newWidth = Math.max(120, Math.min(400, startWidth + deltaX));
                }

                if (resizeType.includes('bottom')) {
                    newHeight = Math.max(80, Math.min(300, startHeight + deltaY));
                }

                // 应用新尺寸
                DOMUtils.setStyle(moduleElement, {
                    width: `${newWidth}px`,
                    height: `${newHeight}px`
                });

                // 触发连接线更新
                self.app.emit('module:position-changed', { moduleId });

                EventUtils.preventDefault(e);
            } catch (error) {
                self.app.handleError(error, `调整模块 ${moduleId} 大小时出错`);
            }
        };

        const stopResize = () => {
            if (!isResizing) return;

            try {
                isResizing = false;
                resizeType = '';

                // 移除全局事件监听
                document.removeEventListener('mousemove', handleResize);
                document.removeEventListener('mouseup', stopResize);
                document.removeEventListener('touchmove', handleResize);
                document.removeEventListener('touchend', stopResize);

                // 移除视觉反馈
                DOMUtils.removeClass(moduleElement, 'resizing');

                // 保存新尺寸到配置
                const rect = moduleElement.getBoundingClientRect();
                if (!self.config.modules[moduleId].size) {
                    self.config.modules[moduleId].size = {};
                }
                self.config.modules[moduleId].size.width = rect.width;
                self.config.modules[moduleId].size.height = rect.height;

                // 触发尺寸变化事件
                self.app.emit('module:size-changed', {
                    moduleId,
                    width: rect.width,
                    height: rect.height
                });

                console.log(`[ModuleRenderer] 模块 ${moduleId} 尺寸已调整为: ${rect.width}x${rect.height}`);
            } catch (error) {
                self.app.handleError(error, `结束调整模块 ${moduleId} 大小时出错`);
            }
        };

        // 绑定手柄事件
        try {
            this.eventManager.add(rightHandle, 'mousedown', (e) => startResize(e, 'right'));
            this.eventManager.add(bottomHandle, 'mousedown', (e) => startResize(e, 'bottom'));
            this.eventManager.add(cornerHandle, 'mousedown', (e) => startResize(e, 'right-bottom'));

            // 触摸事件支持
            this.eventManager.add(rightHandle, 'touchstart', (e) => startResize(e, 'right'));
            this.eventManager.add(bottomHandle, 'touchstart', (e) => startResize(e, 'bottom'));
            this.eventManager.add(cornerHandle, 'touchstart', (e) => startResize(e, 'right-bottom'));
        } catch (error) {
            this.app.handleError(error, `绑定模块 ${moduleId} 调整大小事件失败`);
        }
    }

    /**
     * 生成数据库模块HTML内容（极简设计）
     */
    generateDatabaseModuleHTML(moduleId, moduleConfig, statusClass, statusText) {
        return `
            <div class="module-header">
                <div class="module-title">${moduleConfig.name}</div>
            </div>
        `;
    }

    /**
     * 获取状态指示器状态
     */
    getIndicatorStatus(status) {
        switch (status) {
            case 'running': return 'running';
            case 'active': return 'running';
            case 'error': return 'stopped';
            default: return 'idle';
        }
    }

    /**
     * 生成模块HTML内容
     */
    generateModuleHTML(moduleId, moduleConfig) {
        const statusClass = moduleConfig.status || 'idle';
        const statusText = this.getStatusText(statusClass);

        // 数据库模块使用特殊模板
        if (moduleConfig.type === 'database') {
            return this.generateDatabaseModuleHTML(moduleId, moduleConfig, statusClass, statusText);
        }

        return `
            <div class="module-header">
                <div class="module-title">${moduleConfig.name}</div>
            </div>

            <div class="module-description">
                ${moduleConfig.description}
            </div>

            <div class="module-status">
                <div class="status-text ${statusClass}">
                    <div class="status-indicator"></div>
                    ${statusText}
                </div>
                ${moduleConfig.hasControls ? this.generateControlButtons(moduleId, statusClass) : ''}
            </div>

            <div class="module-logs" id="logs-${moduleId}">
                <div class="logs-header">
                    <span class="logs-title">📋 运行日志</span>
                    <button class="logs-toggle" data-module="${moduleId}" title="展开/收起日志">
                        <span class="toggle-icon">▼</span>
                    </button>
                </div>
                <div class="logs-content" id="logs-content-${moduleId}">
                    <div class="log-item waiting">
                        <span class="log-time">--:--:--</span>
                        <span class="log-message">等待模块启动...</span>
                    </div>
                </div>
            </div>
        `;
    }

    /**
     * 生成控制按钮（两按钮水平布局）
     */
    generateControlButtons(moduleId, status) {
        const isRunning = status === 'running';
        const isStarting = status === 'starting';
        const isStopping = status === 'stopping';
        const isDisabled = isStarting || isStopping;

        const startText = isStarting ? '启动中...' : '启动';

        return `
                <button class="module-btn btn-start"
                        data-action="start"
                        data-module="${moduleId}"
                        ${isRunning || isDisabled ? 'disabled' : ''}>
                    ${startText}
                </button>
                <button class="module-btn btn-config"
                        data-action="config"
                        data-module="${moduleId}"
                        ${isDisabled ? 'disabled' : ''}>
                    配置
                </button>
        `;
    }

    /**
     * 绑定模块事件
     */
    bindModuleEvents(moduleElement, moduleId, moduleConfig) {
        // 控制按钮事件（支持切换按钮）
        const buttons = moduleElement.querySelectorAll('.module-btn');
        buttons.forEach(button => {
            button.addEventListener('click', (e) => {
                e.stopPropagation();
                const action = button.dataset.action;
                const moduleId = button.dataset.module;

                if (button.disabled) return;

                switch (action) {
                    case 'start':
                        this.app.emit('module:start', { moduleId });
                        break;
                    case 'stop':
                        this.app.emit('module:stop', { moduleId });
                        break;
                    case 'config':
                        this.app.emit('module:config', { moduleId });
                        break;
                }
            });
        });

        // 日志切换按钮事件
        const logsToggle = moduleElement.querySelector('.logs-toggle');
        if (logsToggle) {
            logsToggle.addEventListener('click', (e) => {
                e.stopPropagation();
                this.toggleModuleLogs(moduleId);
            });
        }

        // 简化的拖拽功能
        this.bindDragEvents(moduleElement, moduleId);
    }

    /**
     * 绑定拖拽事件（性能优化版）
     */
    bindDragEvents(moduleElement, moduleId) {
        let isDragging = false;
        let startX, startY, startLeft, startTop;
        let animationId = null;
        let lastMoveTime = 0;
        const throttleDelay = 16; // ~60fps

        // 优化的mousemove处理函数
        const handleMouseMove = (e) => {
            if (!isDragging) return;

            const now = Date.now();
            if (now - lastMoveTime < throttleDelay) return;
            lastMoveTime = now;

            // 取消之前的动画帧
            if (animationId) {
                cancelAnimationFrame(animationId);
            }

            // 使用requestAnimationFrame优化DOM更新
            animationId = requestAnimationFrame(() => {
                const deltaX = e.clientX - startX;
                const deltaY = e.clientY - startY;
                const newX = startLeft + deltaX;
                const newY = startTop + deltaY;

                // 使用transform代替直接修改left/top，性能更好
                moduleElement.style.transform = `translate(${deltaX}px, ${deltaY}px)`;

                // 同时更新实际位置用于最终保存
                moduleElement.style.left = `${newX}px`;
                moduleElement.style.top = `${newY}px`;
            });
        };

        moduleElement.addEventListener('mousedown', (e) => {
            if (e.target.closest('.module-btn')) return;

            isDragging = true;
            startX = e.clientX;
            startY = e.clientY;
            startLeft = parseInt(moduleElement.style.left) || 0;
            startTop = parseInt(moduleElement.style.top) || 0;

            // 添加拖拽优化类，禁用复杂动画
            moduleElement.classList.add('dragging', 'dragging-optimized');

            // 绑定移动事件（优化：只在拖拽时绑定）
            document.addEventListener('mousemove', handleMouseMove, { passive: true });

            e.preventDefault();
        });

        document.addEventListener('mouseup', () => {
            if (isDragging) {
                isDragging = false;

                // 清理动画帧
                if (animationId) {
                    cancelAnimationFrame(animationId);
                    animationId = null;
                }

                // 移除事件监听器（优化：避免内存泄漏）
                document.removeEventListener('mousemove', handleMouseMove);

                // 恢复正常样式
                moduleElement.classList.remove('dragging', 'dragging-optimized');
                moduleElement.style.transform = ''; // 清除transform

                // 触发位置更新事件
                this.app.emit('module:position-changed', {
                    moduleId,
                    position: {
                        x: parseInt(moduleElement.style.left) || 0,
                        y: parseInt(moduleElement.style.top) || 0
                    }
                });
            }
        });
    }

    /**
     * 更新模块状态
     */
    updateModuleStatus(moduleId, status) {
        const moduleElement = this.moduleElements.get(moduleId);
        if (!moduleElement) return;

        // 更新状态文本
        const statusElement = moduleElement.querySelector('.status-text');
        if (statusElement) {
            statusElement.className = `status-text ${status}`;
            statusElement.innerHTML = `
                <div class="status-indicator"></div>
                ${this.getStatusText(status)}
            `;
        }

        // 更新启动按钮
        const startButton = moduleElement.querySelector('.btn-start');
        if (startButton) {
            const isRunning = status === 'running';
            const isStarting = status === 'starting';
            const isDisabled = isRunning || isStarting;

            startButton.disabled = isDisabled;
            startButton.textContent = isStarting ? '启动中...' : '启动';
        }

        console.log(`[ModuleRenderer] 模块 ${moduleId} 状态更新为 ${status}`);
    }

    // 已删除 getModuleIcon 方法 - 不再需要图标

    /**
     * 获取状态文本
     */
    getStatusText(status) {
        const statusTexts = {
            'running': '运行中',
            'idle': '已停止',
            'starting': '启动中',
            'stopping': '停止中',
            'error': '错误',
            'active': '活跃'
        };
        return statusTexts[status] || '未知';
    }

    /**
     * 获取模块元素
     */
    getModuleElement(moduleId) {
        return this.moduleElements.get(moduleId);
    }

    /**
     * 添加模块日志
     */
    addModuleLog(moduleId, message, type = 'info') {
        const logsContent = document.getElementById(`logs-content-${moduleId}`);
        if (!logsContent) return;

        // 创建日志项
        const logItem = document.createElement('div');
        logItem.className = `log-item ${type}`;

        const now = new Date();
        const timeStr = now.toLocaleTimeString('zh-CN', { hour12: false });

        logItem.innerHTML = `
            <span class="log-time">${timeStr}</span>
            <span class="log-message">${message}</span>
        `;

        // 如果是第一条日志，清除等待提示
        if (logsContent.querySelector('.log-item.waiting')) {
            logsContent.innerHTML = '';
        }

        // 添加新日志
        logsContent.appendChild(logItem);

        // 限制日志数量（最多保留20条）
        const logItems = logsContent.querySelectorAll('.log-item');
        if (logItems.length > 20) {
            logsContent.removeChild(logItems[0]);
        }

        // 自动滚动到底部
        logsContent.scrollTop = logsContent.scrollHeight;
    }

    /**
     * 清除模块日志
     */
    clearModuleLogs(moduleId) {
        const logsContent = document.getElementById(`logs-content-${moduleId}`);
        if (logsContent) {
            logsContent.innerHTML = `
                <div class="log-item waiting">
                    <span class="log-time">--:--:--</span>
                    <span class="log-message">等待模块启动...</span>
                </div>
            `;
        }
    }

    /**
     * 切换日志显示/隐藏
     */
    toggleModuleLogs(moduleId) {
        const logsContent = document.getElementById(`logs-content-${moduleId}`);
        const toggleIcon = document.querySelector(`[data-module="${moduleId}"] .toggle-icon`);

        if (logsContent && toggleIcon) {
            const isVisible = logsContent.style.display !== 'none';

            if (isVisible) {
                logsContent.style.display = 'none';
                toggleIcon.textContent = '▶';
            } else {
                logsContent.style.display = 'block';
                toggleIcon.textContent = '▼';
                // 滚动到底部
                logsContent.scrollTop = logsContent.scrollHeight;
            }
        }
    }
}

// 导出模块渲染器
window.ModuleRenderer = ModuleRenderer;
