# {{ AURA-X: Modify - 将SQLite连接改为PostgreSQL连接. Approval: 寸止(ID:1737734400). }}
import sys
import os
import psycopg2
import psycopg2.extras
import struct
import pytz
from datetime import datetime
from pathlib import Path

# 重新设置项目根目录
current_file = Path(__file__).resolve()
project_root = current_file.parent.parent  # 回退到YMJATTUU目录

# 确保项目根目录在系统路径中
if str(project_root) not in sys.path:
    sys.path.insert(0, str(project_root))

# 导入TradeFusion统一日志标准
from 公共模块.TradeFusion统一日志标准 import 获取日志器
logger = 获取日志器("所属板块强度")

try:
    from 公共模块.交易日期 import get_trading_date
except ImportError as e:
    sys.exit(1)

def generate_timestamp(trading_date):
    """生成北京时间8点对应的UTC时间戳"""
    tz_sh = pytz.timezone('Asia/Shanghai')
    naive_dt = datetime(
        trading_date.year,
        trading_date.month,
        trading_date.day,
        8, 0, 0
    )
    try:
        localized_dt = tz_sh.localize(naive_dt, is_dst=None)
    except pytz.exceptions.AmbiguousTimeError:
        localized_dt = tz_sh.localize(naive_dt, is_dst=False)
    return int(localized_dt.astimezone(pytz.utc).timestamp())

def update_dat_file(filepath, target_ts, new_value):
    """更新或追加dat文件记录"""
    try:
        with open(filepath, 'rb+') as f:
            found = False
            while True:
                pos = f.tell()
                record = f.read(8)
                if not record:
                    break
                if len(record) != 8:
                    break
                ts = struct.unpack('<I', record[:4])[0]
                if ts == target_ts:
                    f.seek(pos + 4)
                    f.write(struct.pack('<f', new_value))
                    found = True
                    break
            if not found:
                f.seek(0, 2)
                f.write(struct.pack('<I', target_ts) + struct.pack('<f', new_value))
    except FileNotFoundError:
        with open(filepath, 'ab') as f:
            f.write(struct.pack('<I', target_ts) + struct.pack('<f', new_value))

def main():
    # 导入配置管理
    # {{ AURA-X: Modify - 修改为PostgreSQL连接配置. Approval: 寸止(ID:1737734400). }}
    try:
        from 公共模块.配置管理 import get_config
        config = get_config()
        dat_dir = os.path.join(config.get('data_sources.dzh_base_path'), '所属板块强度')
    except ImportError as e:
        # 使用默认路径
        dat_dir = r'E:\dzh2\USERDATA\SelfData\所属板块强度'

    # {{ AURA-X: Modify - 统一PostgreSQL密码为标准密码. Approval: 寸止(ID:1737734400). }}
    # PostgreSQL连接配置
    db_config = {
        'host': 'localhost',
        'port': 5432,
        'database': 'tradefusion',
        'user': 'postgres',
        'password': 'ymjatTUU520'
    }
    
    # 获取单个交易日
    try:
        date_obj = datetime.strptime(str(get_trading_date()), '%Y%m%d').date()
        current_date = int(date_obj.strftime('%Y%m%d'))
    except Exception as e:
        return False, f"获取交易日失败: {e}", 0

    # 连接数据库
    processed_count = 0
    cleared_count = 0
    # {{ AURA-X: Modify - 修改为PostgreSQL连接. Approval: 寸止(ID:1737734400). }}
    # 初始化数据库连接
    conn = None
    try:
        conn = psycopg2.connect(**db_config)
        cursor = conn.cursor()
        
        # {{ AURA-X: Modify - 修复PostgreSQL语法错误. Approval: 寸止(ID:1737734400). }}
        # 创建DAT文件处理记录表（如果不存在）
        cursor.execute('''CREATE TABLE IF NOT EXISTS "DAT文件处理记录表" (
            "日期" INTEGER,
            "股票代码" TEXT,
            PRIMARY KEY ("日期", "股票代码")
        )''')

        # 清理过期数据
        cursor.execute('DELETE FROM "DAT文件处理记录表" WHERE "日期" != %s', (current_date,))

        # 获取当日数据
        cursor.execute('''
            SELECT "股票代码", "所属板块评分" AS strength
            FROM "所属板块评分表"
            WHERE "日期" = %s AND "所属板块评分" IS NOT NULL
        ''', (current_date,))
        
        records = cursor.fetchall()
        
        # 处理记录
        target_ts = generate_timestamp(date_obj)
        processed_stocks = set()
        
        for stock_code, strength in records:
            try:
                filename = f"{stock_code.strip()}.dat"
                filepath = os.path.join(dat_dir, filename)
                update_dat_file(filepath, target_ts, float(strength))
                
                # {{ AURA-X: Modify - 修复PostgreSQL语法错误. Approval: 寸止(ID:1737734400). }}
                # 更新DAT文件处理记录表
                cursor.execute('''
                    INSERT INTO "DAT文件处理记录表" ("日期", "股票代码")
                    VALUES (%s, %s)
                    ON CONFLICT ("日期", "股票代码") DO NOTHING
                ''', (current_date, stock_code))
                processed_count += 1
                processed_stocks.add(stock_code)
            except Exception as e:
                continue
        
        # {{ AURA-X: Modify - 修复PostgreSQL语法错误. Approval: 寸止(ID:1737734400). }}
        # 获取需要清零的股票列表
        cursor.execute('SELECT "股票代码" FROM "DAT文件处理记录表" WHERE "日期" = %s', (current_date,))
        all_stocks = {row[0] for row in cursor.fetchall()}
        stocks_to_clear = all_stocks - processed_stocks
        
        # 执行清零操作
        for stock_code in stocks_to_clear:
            try:
                filename = f"{stock_code.strip()}.dat"
                filepath = os.path.join(dat_dir, filename)
                update_dat_file(filepath, target_ts, 0.0)
                cleared_count += 1
            except Exception as e:
                continue
        
        conn.commit()
        return True, date_obj.strftime('%Y-%m-%d'), processed_count + cleared_count

    except Exception as e:
        return False, str(e), 0
    finally:
        conn.close()

if __name__ == '__main__':
    success, message, total_count = main()
    if success:
        logger.记录模块执行("DAT文件生成完成", total_count)
    else:
        logger.记录错误(f"DAT文件生成失败 - {message}")