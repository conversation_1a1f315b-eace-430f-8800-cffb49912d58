# TradeFusion桌面版数据流规范文档

> **文档目的**：规范TradeFusion桌面版的数据流逻辑，防止错误修改  
> **适用场景**：代码修改、连接关系调整、模块配置变更时的参考依据  
> **创建时间**：2025-01-10  
> **版本**：v1.0

## 📋 核心架构原则

### 1. 调度中心设计原则
- **调度中心不是直接控制器**：调度中心启动数据流程，不直接调度所有模块
- **数据流驱动**：数据在模块间按依赖关系自动流转
- **时序严格性**：XGB流程有严格的T1-T11时序依赖，不可并行

### 2. 数据流分类
- **RQ流程**：简单并行，4个独立任务
- **XGB流程**：复杂串行，11步严格依赖
- **数据库流**：模块与数据库表之间的读写操作
- **文件流**：CSV等中间文件的传递

## 🔄 RQ调度中心流程（简单并行）

### 流程特征
- **调度周期**：60秒循环
- **执行方式**：4个任务并行执行
- **数据特点**：人气排名数据，实时性要求高

### 标准流程
```
RQ调度中心 (启动4个任务)
├── T1: 人气_东财采集 → 个股人气表
├── T2: 人气_同花采集 → 个股人气表
├── T3: 综合人气190 ← 个股人气表
└── T4: 生成DZH3人气股票板块 ← 个股人气表
```

### 连接关系规范
1. **调度关系**：RQ调度中心 → 4个模块（启动信号）
2. **数据采集**：采集模块 → 个股人气表（写入）
3. **数据输出**：个股人气表 → 输出模块（读取）

### 关键约束
- 采集模块和输出模块不直接连接
- 必须通过个股人气表中转
- 输出模块直接读取数据库，不需要调度中心二次调度

## ⚙️ XGB调度中心流程（复杂串行）

### 流程特征
- **调度周期**：60秒循环
- **执行方式**：11步严格串行，有明确的T1-T11时序
- **数据特点**：选股宝数据处理，逻辑复杂，依赖性强

### 标准流程（11步严格串行）
```
XGB调度中心 (启动11步串行流程)
T1: 选股宝抓取 → bsj_xgb_ie.csv
T2: 选股宝清洗 ← bsj_xgb_ie.csv → csj_xgb_qx.csv
T3: 个股解读_板块信息_关联表 ← csj_xgb_qx.csv → 3张基础表
T4: 选股宝_大智慧str ← 3张基础表 → STR文件
T5: 板块涨停表 ← 2张表 → 板块涨停表
T6: 板块精选 ← 板块涨停表(5天历史) → 板块精选表
T7: 个股接力表 ← 4张表 → 个股接力表
T8: 采集_本地数据 → 个股连板高度表
T9: A3_所属板块评分表 ← 2张表 → 所属板块评分表
T10: A4_所属板块强度 ← 所属板块评分表 → DAT文件
T11: 接力 ← 个股接力表 → DAT文件
```

### 时序依赖规范
- **T1-T3**：数据获取和基础表建立（必须串行）
- **T4**：基于T3的结果，读取3张基础表
- **T5-T6**：板块分析流程（T6依赖T5）
- **T7**：复杂接力算法（依赖T6和其他3张表）
- **T8**：独立的本地数据处理
- **T9-T11**：输出文件生成（依赖前面的分析结果）

### 关键约束
- **严禁并行**：T1-T11必须严格按顺序执行
- **依赖检查**：每步执行前必须确认前置条件完成
- **数据完整性**：中间表数据必须完整才能进入下一步

## 🏗️ 关键模块特征规范

### 1. 数据枢纽模块
**模块名称**：个股解读_板块信息_关联表  
**关键特征**：一个模块写入3张表  
**连接规范**：
```
个股解读_板块信息_关联表 → 个股解读表
个股解读_板块信息_关联表 → 板块信息表  
个股解读_板块信息_关联表 → 个股板块关联表
```
**标签规范**：必须明确标识"写入:表名"

### 2. 复杂算法模块
**模块名称**：个股接力表  
**关键特征**：读取4张表的复杂算法  
**连接规范**：
```
板块精选表 → 个股接力表
个股板块关联表 → 个股接力表
个股人气表 → 个股接力表
板块涨停表 → 个股接力表
个股接力表 → 个股接力表(数据库)
```
**标签规范**：必须明确标识"读取:表名"

### 3. 多表整合模块
**模块名称**：选股宝_大智慧str  
**关键特征**：读取3张表生成STR文件  
**连接规范**：
```
个股板块关联表 → 选股宝_大智慧str
板块信息表 → 选股宝_大智慧str
个股解读表 → 选股宝_大智慧str
```
**标签规范**：必须明确标识"读取:表名"

## 📁 中间文件流程规范

### CSV文件流转
```
选股宝抓取 → bsj_xgb_ie.csv → 选股宝清洗 → csj_xgb_qx.csv → 数据库导入
```

### 文件命名规范
- **bsj_xgb_ie.csv**：选股宝原始数据文件
- **csj_xgb_qx.csv**：选股宝清洗后数据文件
- **a_dfcf_rq.csv**：东财人气数据文件
- **a_ths_ie.csv**：同花人气数据文件

### 文件流转约束
- 文件必须完整生成后才能被下一模块读取
- 文件损坏或不完整时必须重新生成
- 中间文件不能跳过，必须按顺序流转

## 💾 数据库表分层规范

### 基础数据表（第一层）
- **个股人气表**：存储东财+同花人气排名
- **个股连板高度表**：存储连板高度+涨停评分
- **个股解读表**：存储个股解读信息
- **板块信息表**：存储板块涨幅+消息
- **个股板块关联表**：存储股票-板块关联关系

### 统计分析表（第二层）
- **板块涨停表**：板块涨停评分，只保留前7名
- **板块精选表**：精选板块，SMA算法+双重筛选
- **个股接力表**：接力值数据，X支+Y支股票
- **所属板块评分表**：个股板块评分

### 表间依赖关系
- 第二层表依赖第一层表
- 不能跨层直接依赖
- 表的读写必须通过对应的处理模块

## 🎨 连接线标签规范

### 调度关系标签
- **RQ流程**：`启动采集`、`启动输出`
- **XGB流程**：`T1:数据抓取`、`T2:数据清洗`...`T11:接力DAT生成`

### 数据流标签
- **文件流**：具体文件名（如`bsj_xgb_ie.csv`）
- **数据库写入**：`写入:表名`
- **数据库读取**：`读取:表名`
- **特殊标识**：`读取:表名(5天历史)`

### 标签命名约束
- 必须明确区分读取和写入操作
- 特殊算法必须标注特征（如历史数据）
- 时序步骤必须标注T1-T11
- 禁止使用模糊标签（如"数据"、"信息"）

## ⚠️ 修改约束和禁止事项

### 严禁修改的核心逻辑
1. **XGB流程的T1-T11时序**：绝对不能改为并行
2. **数据枢纽的3表写入**：不能减少或增加表数量
3. **复杂算法的4表读取**：不能改变读取的表数量
4. **调度中心的启动逻辑**：不能改为直接控制所有模块

### 允许的调整范围
1. **模块位置**：可以调整布局位置，但不能改变层次关系
2. **连接线样式**：可以调整颜色、粗细，但不能改变连接关系
3. **标签文字**：可以优化描述，但必须保持语义准确
4. **模块图标**：可以调整视觉效果，但不能改变模块类型

### 修改前必须检查
1. **依赖关系完整性**：确保所有依赖关系正确
2. **时序逻辑正确性**：确保串行流程不被破坏
3. **数据流闭环**：确保数据有明确的起点和终点
4. **标签语义准确性**：确保标签准确描述数据流向

## 🔧 技术实现规范

### 配置文件结构
**文件位置**：`TradeFusion桌面版/modules-config.json`

**关键配置节点**：
```json
{
  "modules": {
    // 26个模块的位置、类型、状态配置
  },
  "connections": [
    // 连接关系配置，包含from、to、type、label
  ],
  "config": {
    // 全局配置：Python路径、工作目录等
  }
}
```

### 连接类型定义
- **rq-flow**：RQ调度中心流程（紫色）
- **xgb-flow**：XGB调度中心流程（橙色）
- **database-flow**：数据库读写操作（绿色）
- **data-flow**：数据文件传递（青色）

### 模块类型定义
- **scheduler**：调度中心（特殊边框和阴影）
- **data-collection**：数据采集（紫色图标）
- **analysis**：分析处理（橙色图标）
- **data-hub**：数据枢纽（黄色图标，特殊标识）
- **database**：数据库表（绿色图标，圆角边框）
- **output**：输出模块（红色图标）

## 📊 布局层次规范

### 垂直层次结构（从上到下）
1. **第1层（Y=50）**：调度中心
2. **第2层（Y=200）**：数据采集
3. **第3层（Y=350-500）**：数据处理和基础数据库
4. **第4层（Y=650-950）**：分析处理和统计表
5. **第5层（Y=1200-1600）**：高级分析和输出

### 水平分区规范
- **左侧区域（X=50-400）**：RQ流程相关模块
- **中间区域（X=700-1000）**：共享分析模块
- **右侧区域（X=1100-1300）**：XGB流程相关模块

## 🎯 质量检查清单

### 连接关系检查
- [ ] RQ调度中心连接4个模块
- [ ] XGB调度中心连接11个模块（T1-T11）
- [ ] 数据枢纽模块连接3张表
- [ ] 复杂算法模块连接4张表
- [ ] 多表整合模块连接3张表

### 时序逻辑检查
- [ ] XGB流程T1-T11严格串行
- [ ] CSV文件流转顺序正确
- [ ] 数据库表分层依赖正确
- [ ] 没有循环依赖

## 📚 参考文档
- `TradeFusion数据流程分析报告.md`：详细的业务逻辑分析
- `TradeFusion完整模块依赖思维导图.mmd`：模块依赖关系图
- `TradeFusion完整数据流程图.mmd`：完整的数据流程图

---

**⚠️ 重要提醒**：任何对数据流逻辑的修改都必须参考此文档，确保不破坏核心业务逻辑！
