/**
 * 事件工具模块
 * 
 * 功能：提供通用的事件处理工具函数
 * 职责：统一事件管理，防止内存泄漏
 * 
 * <AUTHOR> Team
 * @version 2.0.0
 */

class EventUtils {
    constructor() {
        this.listeners = new Map(); // 存储事件监听器引用
        this.debounceTimers = new Map(); // 防抖定时器
        this.throttleTimers = new Map(); // 节流定时器
    }

    /**
     * 添加事件监听器（带自动清理）
     */
    static addEventListener(element, event, handler, options = {}) {
        if (!element || !event || !handler) return null;

        const wrappedHandler = (e) => {
            try {
                handler(e);
            } catch (error) {
                console.error(`[EventUtils] 事件处理错误 ${event}:`, error);
            }
        };

        element.addEventListener(event, wrappedHandler, options);

        // 返回清理函数
        return () => {
            element.removeEventListener(event, wrappedHandler, options);
        };
    }

    /**
     * 移除事件监听器
     */
    static removeEventListener(element, event, handler, options = {}) {
        if (element && event && handler) {
            element.removeEventListener(event, handler, options);
        }
    }

    /**
     * 一次性事件监听器
     */
    static once(element, event, handler, options = {}) {
        if (!element || !event || !handler) return null;

        const onceHandler = (e) => {
            try {
                handler(e);
            } catch (error) {
                console.error(`[EventUtils] 一次性事件处理错误 ${event}:`, error);
            } finally {
                element.removeEventListener(event, onceHandler, options);
            }
        };

        element.addEventListener(event, onceHandler, options);
        return onceHandler;
    }

    /**
     * 防抖函数
     */
    static debounce(func, delay = 300, immediate = false) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                timeout = null;
                if (!immediate) func.apply(this, args);
            };
            const callNow = immediate && !timeout;
            clearTimeout(timeout);
            timeout = setTimeout(later, delay);
            if (callNow) func.apply(this, args);
        };
    }

    /**
     * 节流函数
     */
    static throttle(func, limit = 100) {
        let inThrottle;
        return function executedFunction(...args) {
            if (!inThrottle) {
                func.apply(this, args);
                inThrottle = true;
                setTimeout(() => inThrottle = false, limit);
            }
        };
    }

    /**
     * 创建自定义事件
     */
    static createEvent(type, detail = null, options = {}) {
        const defaultOptions = {
            bubbles: true,
            cancelable: true,
            detail: detail
        };
        
        return new CustomEvent(type, { ...defaultOptions, ...options });
    }

    /**
     * 触发自定义事件
     */
    static trigger(element, eventType, detail = null, options = {}) {
        if (!element || !eventType) return false;
        
        const event = this.createEvent(eventType, detail, options);
        return element.dispatchEvent(event);
    }

    /**
     * 委托事件处理
     */
    static delegate(parent, selector, event, handler, options = {}) {
        if (!parent || !selector || !event || !handler) return null;

        const delegateHandler = (e) => {
            const target = e.target.closest(selector);
            if (target && parent.contains(target)) {
                try {
                    handler.call(target, e);
                } catch (error) {
                    console.error(`[EventUtils] 委托事件处理错误 ${event}:`, error);
                }
            }
        };

        parent.addEventListener(event, delegateHandler, options);

        // 返回清理函数
        return () => {
            parent.removeEventListener(event, delegateHandler, options);
        };
    }

    /**
     * 阻止事件冒泡
     */
    static stopPropagation(event) {
        if (event && event.stopPropagation) {
            event.stopPropagation();
        }
    }

    /**
     * 阻止默认行为
     */
    static preventDefault(event) {
        if (event && event.preventDefault) {
            event.preventDefault();
        }
    }

    /**
     * 阻止事件冒泡和默认行为
     */
    static stop(event) {
        this.stopPropagation(event);
        this.preventDefault(event);
    }

    /**
     * 获取事件目标元素
     */
    static getTarget(event) {
        return event ? (event.target || event.srcElement) : null;
    }

    /**
     * 获取事件的当前目标元素
     */
    static getCurrentTarget(event) {
        return event ? event.currentTarget : null;
    }

    /**
     * 检查是否按下了特定键
     */
    static isKey(event, key) {
        if (!event) return false;
        return event.key === key || event.code === key || event.keyCode === key;
    }

    /**
     * 检查是否按下了修饰键
     */
    static hasModifier(event, modifier) {
        if (!event) return false;
        
        switch (modifier.toLowerCase()) {
            case 'ctrl':
            case 'control':
                return event.ctrlKey;
            case 'shift':
                return event.shiftKey;
            case 'alt':
                return event.altKey;
            case 'meta':
            case 'cmd':
                return event.metaKey;
            default:
                return false;
        }
    }

    /**
     * 获取鼠标位置
     */
    static getMousePosition(event, relativeTo = null) {
        if (!event) return { x: 0, y: 0 };
        
        let x = event.clientX;
        let y = event.clientY;
        
        if (relativeTo) {
            const rect = relativeTo.getBoundingClientRect();
            x -= rect.left;
            y -= rect.top;
        }
        
        return { x, y };
    }

    /**
     * 检查是否为触摸事件
     */
    static isTouchEvent(event) {
        return event && event.type && event.type.startsWith('touch');
    }

    /**
     * 获取触摸位置
     */
    static getTouchPosition(event, relativeTo = null) {
        if (!event || !event.touches || event.touches.length === 0) {
            return { x: 0, y: 0 };
        }
        
        const touch = event.touches[0];
        let x = touch.clientX;
        let y = touch.clientY;
        
        if (relativeTo) {
            const rect = relativeTo.getBoundingClientRect();
            x -= rect.left;
            y -= rect.top;
        }
        
        return { x, y };
    }

    /**
     * 统一获取指针位置（鼠标或触摸）
     */
    static getPointerPosition(event, relativeTo = null) {
        if (this.isTouchEvent(event)) {
            return this.getTouchPosition(event, relativeTo);
        } else {
            return this.getMousePosition(event, relativeTo);
        }
    }

    /**
     * 等待DOM加载完成
     */
    static ready(callback) {
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', callback);
        } else {
            callback();
        }
    }

    /**
     * 等待窗口加载完成
     */
    static loaded(callback) {
        if (document.readyState === 'complete') {
            callback();
        } else {
            window.addEventListener('load', callback);
        }
    }

    /**
     * 监听窗口大小变化
     */
    static onResize(callback, delay = 100) {
        const debouncedCallback = this.debounce(callback, delay);
        window.addEventListener('resize', debouncedCallback);
        
        // 返回清理函数
        return () => {
            window.removeEventListener('resize', debouncedCallback);
        };
    }

    /**
     * 监听滚动事件
     */
    static onScroll(element, callback, options = {}) {
        const { throttle = true, delay = 16 } = options;
        const handler = throttle ? this.throttle(callback, delay) : callback;
        
        const target = element || window;
        target.addEventListener('scroll', handler, { passive: true });
        
        // 返回清理函数
        return () => {
            target.removeEventListener('scroll', handler);
        };
    }

    /**
     * 批量添加事件监听器
     */
    static addMultipleListeners(element, events, handler, options = {}) {
        if (!element || !events || !handler) return [];
        
        const cleanupFunctions = [];
        const eventList = Array.isArray(events) ? events : [events];
        
        eventList.forEach(event => {
            const cleanup = this.addEventListener(element, event, handler, options);
            if (cleanup) {
                cleanupFunctions.push(cleanup);
            }
        });
        
        // 返回批量清理函数
        return () => {
            cleanupFunctions.forEach(cleanup => cleanup());
        };
    }

    /**
     * 事件管理器类
     */
    static createEventManager() {
        return new EventManager();
    }
}

/**
 * 事件管理器类
 * 用于统一管理组件的所有事件监听器
 */
class EventManager {
    constructor() {
        this.cleanupFunctions = [];
    }

    /**
     * 添加事件监听器
     */
    add(element, event, handler, options = {}) {
        const cleanup = EventUtils.addEventListener(element, event, handler, options);
        if (cleanup) {
            this.cleanupFunctions.push(cleanup);
        }
        return cleanup;
    }

    /**
     * 添加一次性事件监听器
     */
    once(element, event, handler, options = {}) {
        const cleanup = () => {}; // once事件会自动清理
        EventUtils.once(element, event, handler, options);
        return cleanup;
    }

    /**
     * 添加委托事件
     */
    delegate(parent, selector, event, handler, options = {}) {
        const cleanup = EventUtils.delegate(parent, selector, event, handler, options);
        if (cleanup) {
            this.cleanupFunctions.push(cleanup);
        }
        return cleanup;
    }

    /**
     * 清理所有事件监听器
     */
    cleanup() {
        this.cleanupFunctions.forEach(cleanup => {
            try {
                cleanup();
            } catch (error) {
                console.error('[EventManager] 清理事件监听器时出错:', error);
            }
        });
        this.cleanupFunctions = [];
    }

    /**
     * 获取监听器数量
     */
    getListenerCount() {
        return this.cleanupFunctions.length;
    }
}

// 导出事件工具类
window.EventUtils = EventUtils;
window.EventManager = EventManager;
