# ===================== 人气_同花采集.py =====================
import os
import sys
import time
import traceback
from pathlib import Path
from datetime import datetime, timedelta
from playwright.sync_api import sync_playwright

# 预先导入可能用到的模块，避免运行时动态导入
try:
    import psycopg2
except ImportError:
    psycopg2 = None

# 必须先设置路径再导入TradeFusion模块
def get_project_paths():
    """获取项目路径配置并设置Python路径"""
    script_path = Path(__file__).absolute()
    project_root = script_path.parent.parent

    paths = {
        'root': project_root,
        'logs': project_root / 'logs',
        'crawler': project_root / '数据2_网络采集',
        'common': project_root / '公共模块'
    }

    # 确保目录存在
    for key, path_val in paths.items():
        if key in ['logs', 'crawler', 'common']:
            if not path_val.exists():
                path_val.mkdir(parents=True, exist_ok=True)

    # 设置Python路径
    if str(paths['root']) not in sys.path:
        sys.path.insert(0, str(paths['root']))
    if str(paths['common']) not in sys.path:
        sys.path.insert(0, str(paths['common']))

    return paths

PATHS = get_project_paths()

# 设置Playwright使用项目本地浏览器
def setup_local_browsers():
    """设置使用项目本地浏览器"""
    import os
    browsers_dir = PATHS['root'] / 'browsers'
    if browsers_dir.exists():
        os.environ['PLAYWRIGHT_BROWSERS_PATH'] = str(browsers_dir)
        print(f"🌍 使用项目本地浏览器: {browsers_dir}")
    else:
        print(f"⚠️ 项目本地浏览器目录不存在，使用系统默认浏览器")

setup_local_browsers()

# 现在可以安全导入TradeFusion模块
from 公共模块.TradeFusion统一日志标准 import 获取日志器
logger = 获取日志器("人气_同花采集")

# ==================== 模块配置常量 ====================
# 同花顺采集配置 - 模块内部自管理，无外部依赖
class TonghuashunConfig:
    """同花顺采集配置常量"""
    # 目标网站配置
    TARGET_URL = 'https://eq.10jqka.com.cn/frontend/thsTopRank/index.html?fontzoom=no&client_userid=R9Po6&share_hxapp=gsc&share_action=webpage_share.hot_list_1748317879184&back_source=qqhy#/'

    # 浏览器配置
    BROWSER_TYPE = 'chromium'  # 浏览器类型：chromium, firefox, webkit
    HEADLESS = False           # 是否无头模式
    TIMEOUT = 60000           # 页面超时时间（毫秒）



    # 数据库配置
    DB_CONFIG = {
        'host': 'localhost',
        'port': 5432,
        'database': 'tradefusion',
        'user': 'postgres',
        'password': 'ymjatTUU520'
    }

    # 时间配置（秒）
    PAGE_LOAD_WAIT = 3         # 页面加载等待时间
    SCROLL_WAIT = 0.8          # 滚动间隔时间
    TAB_CLICK_WAIT = 1         # 标签点击等待时间
    CYCLE_INTERVAL = 60        # 循环执行间隔（秒）

    # 数据采集配置
    MAX_RANK = 100             # 最大排名
    MIN_STOCKS_REQUIRED = 50   # 最低股票数量要求
    SCROLL_ITERATIONS = 20     # 滚动次数
    CHECK_INTERVAL = 5         # 每N次滚动检查一次数据

    # 数据库查询配置
    RECENT_DAYS_LIMIT = 30     # 优先查询最近N天的数据

class SimpleBrowserManager:
    """简化的浏览器管理器 - 原生Playwright实现，支持持久化连接"""

    # 类级别的共享实例，实现浏览器复用
    _shared_instance = None

    def __init__(self):
        self.playwright = None
        self.browser = None
        self.context = None
        self.page = None

    @classmethod
    def get_shared_instance(cls):
        """获取共享的浏览器管理器实例"""
        if cls._shared_instance is None:
            cls._shared_instance = cls()
        return cls._shared_instance

    def start_browser(self):
        """启动浏览器"""
        try:
            if self.playwright is None:
                self.playwright = sync_playwright().start()

            if self.browser is None:
                # 根据配置选择浏览器类型
                if TonghuashunConfig.BROWSER_TYPE == 'firefox':
                    self.browser = self.playwright.firefox.launch(headless=TonghuashunConfig.HEADLESS)
                elif TonghuashunConfig.BROWSER_TYPE == 'webkit':
                    self.browser = self.playwright.webkit.launch(headless=TonghuashunConfig.HEADLESS)
                else:  # chromium (默认)
                    self.browser = self.playwright.chromium.launch(
                        headless=TonghuashunConfig.HEADLESS,
                        args=['--no-sandbox', '--disable-dev-shm-usage']
                    )

            if self.context is None:
                self.context = self.browser.new_context()

            if self.page is None:
                self.page = self.context.new_page()

        except Exception as e:
            logger.记录错误(f"浏览器启动失败", e)
            raise

    def get_page(self, url=None, force_refresh=False):
        """获取页面对象"""
        self.start_browser()

        # start_browser()成功后page必定不为None，无需额外检查

        if url:
            if self.page.url != url:
                self.page.goto(url, wait_until='domcontentloaded', timeout=TonghuashunConfig.TIMEOUT)
            elif force_refresh:
                self.page.reload(wait_until='domcontentloaded', timeout=TonghuashunConfig.TIMEOUT)

        return self.page

    def close(self):
        """清理页面状态，但保持浏览器开启以便复用"""
        # 实际清理页面状态：清除cookies、localStorage等
        try:
            if self.page:
                # 清理页面状态但不关闭页面
                self.page.evaluate("() => { localStorage.clear(); sessionStorage.clear(); }")
        except Exception:
            # 清理失败不影响主流程
            pass

    def force_close(self):
        """强制关闭浏览器（仅在程序退出时使用）"""
        try:
            if self.page:
                self.page.close()
                self.page = None
            if self.context:
                self.context.close()
                self.context = None
            if self.browser:
                self.browser.close()
                self.browser = None
            if self.playwright:
                self.playwright.stop()
                self.playwright = None
        except Exception:
            pass

    @classmethod
    def cleanup_shared_instance(cls):
        """清理共享实例（程序退出时调用）"""
        if cls._shared_instance:
            cls._shared_instance.force_close()
            cls._shared_instance = None

# 导入公共模块
try:
    from 公共模块.交易日期 import get_trading_date
    from 数据2_网络采集.人气临时表管理 import 获取临时表管理器
    from 数据2_网络采集.个股人气表 import main as 人气融合_main
except ImportError as e:
    print(f"导入公共模块失败: {e}")
    print(f"Python路径: {sys.path}")
    print(f"当前工作目录: {os.getcwd()}")
    print(f"请确保以下目录存在并包含必要模块: {PATHS['root']}")
    print(f"目录内容: {list(PATHS['root'].glob('*'))}")
    sys.exit(1)

class THS_Crawler:
    def __init__(self):
        self._db_connection = None

    def _get_db_connection(self):
        """获取数据库连接（复用连接）"""
        if self._db_connection is None or self._db_connection.closed:
            if psycopg2 is None:
                raise ImportError("psycopg2 模块未安装，无法连接PostgreSQL数据库")
            self._db_connection = psycopg2.connect(**TonghuashunConfig.DB_CONFIG)
        return self._db_connection

    def _close_db_connection(self):
        """关闭数据库连接"""
        if self._db_connection and not self._db_connection.closed:
            self._db_connection.close()
            self._db_connection = None

    def _get_stock_extraction_js(self, count_only=False):
        """生成股票数据提取的JavaScript代码"""
        if count_only:
            return f'''() => {{
                const items = Array.from(document.querySelectorAll('div[data-v-4b7d5302], div[data-v-2e888cf8]'));
                const stockData = {{}};
                const maxRank = {TonghuashunConfig.MAX_RANK};

                for (let i = 0; i < items.length; i++) {{
                    const item = items[i];
                    try {{
                        const rankElement = item.querySelector('div.THSMF-M.bold.flex');
                        if (rankElement) {{
                            const rank = parseInt(rankElement.textContent.trim());
                            if (!isNaN(rank) && rank <= maxRank) {{
                                stockData[rank] = true;
                            }}
                        }}
                    }} catch (e) {{}}
                }}

                return Object.keys(stockData).length;
            }}'''
        else:
            return f'''() => {{
                const items = Array.from(document.querySelectorAll('div[data-v-4b7d5302], div[data-v-2e888cf8]'));
                const stockItems = [];
                const maxRank = {TonghuashunConfig.MAX_RANK};
                const processedRanks = new Set();

                for (let i = 0; i < items.length; i++) {{
                    const item = items[i];
                    try {{
                        const rankElement = item.querySelector('div.THSMF-M.bold.flex');
                        if (rankElement) {{
                            const rank = parseInt(rankElement.textContent.trim());
                            if (!isNaN(rank) && rank <= maxRank && !processedRanks.has(rank)) {{
                                const nameElement = item.querySelector('span.ellipsis');
                                if (nameElement) {{
                                    const name = nameElement.textContent.trim();
                                    stockItems.push({{rank, name}});
                                    processedRanks.add(rank);
                                }}
                            }}
                        }}
                    }} catch (e) {{}}
                }}

                return stockItems;
            }}'''

    def get_stock_codes(self, stocks):
        """从抓取的股票数据中提取股票代码和排名"""
        processed_codes = []
        for stock in stocks:
            # 获取股票名称和排名
            stock_name = stock['name']
            rank = stock['rank']

            # 通过股票名称查找对应的股票代码
            stock_code = self.find_stock_code_by_name(stock_name)

            if stock_code:
                # 直接使用数据库查询得到的股票代码
                processed_codes.append((stock_code, stock_name, rank))
            # 未找到股票代码的情况直接忽略

        return processed_codes

    def find_stock_code_by_name(self, stock_name):
        """通过股票名称查找股票代码 - 优化版本"""
        try:
            # 使用复用的数据库连接
            conn = self._get_db_connection()
            cursor = conn.cursor()

            # 优化查询：只查询最近N天的数据，减少扫描范围
            recent_date = int((datetime.now() - timedelta(days=TonghuashunConfig.RECENT_DAYS_LIMIT)).strftime('%Y%m%d'))

            query = '''
            SELECT "股票代码" FROM "个股人气表"
            WHERE "股票名称" = %s
            AND "日期" >= %s
            ORDER BY "日期" DESC
            LIMIT 1
            '''

            cursor.execute(query, (stock_name, recent_date))
            result = cursor.fetchone()
            # 不关闭连接，复用连接

            if result:
                return result[0]
            else:
                # 如果最近N天没有，再查询全部历史数据（兜底方案）
                return self._find_stock_code_fallback(stock_name)

        except Exception:
            return None

    def _find_stock_code_fallback(self, stock_name):
        """兜底查询：查询全部历史数据"""
        try:
            conn = self._get_db_connection()
            cursor = conn.cursor()

            query = '''
            SELECT "股票代码" FROM "个股人气表"
            WHERE "股票名称" = %s
            ORDER BY "日期" DESC
            LIMIT 1
            '''

            cursor.execute(query, (stock_name,))
            result = cursor.fetchone()
            # 不关闭连接，复用连接

            return result[0] if result else None

        except Exception:
            return None

    def run(self):
        """运行爬虫，使用持久化浏览器"""
        try:
            # 获取共享的浏览器管理器实例（支持浏览器复用）
            browser_manager = SimpleBrowserManager.get_shared_instance()

            # 访问目标网页
            page = browser_manager.get_page(TonghuashunConfig.TARGET_URL, force_refresh=True)
            time.sleep(TonghuashunConfig.PAGE_LOAD_WAIT)  # 等待页面加载

            # 尝试点击热股标签
            try:
                hot_tab = page.get_by_text("热股")
                if hot_tab:
                    hot_tab.click()
                    time.sleep(TonghuashunConfig.TAB_CLICK_WAIT)
            except Exception:
                pass

            # 使用键盘滚动页面以加载全部股票
            for i in range(TonghuashunConfig.SCROLL_ITERATIONS):
                page.keyboard.press("PageDown")
                time.sleep(TonghuashunConfig.SCROLL_WAIT)  # 给足够时间让懒加载触发

                # 每N次滚动检查一次数据
                if (i + 1) % TonghuashunConfig.CHECK_INTERVAL == 0:
                    current_count = page.evaluate(self._get_stock_extraction_js(count_only=True))

                    # 如果已经获取到足够股票，则停止滚动
                    if current_count >= TonghuashunConfig.MAX_RANK:
                        break

            # 最后一次滚动后再检查一次，确保不遗漏数据
            final_count = page.evaluate(self._get_stock_extraction_js(count_only=True))
            if final_count < TonghuashunConfig.MAX_RANK:
                # 如果还没达到目标，再滚动几次
                for i in range(5):  # 额外滚动5次
                    page.keyboard.press("PageDown")
                    time.sleep(TonghuashunConfig.SCROLL_WAIT)

            # 提取股票数据
            stocks = page.evaluate(self._get_stock_extraction_js(count_only=False))

            # 按排名排序
            stocks.sort(key=lambda x: x['rank'])

            # 检查是否获取了足够的股票
            if len(stocks) < TonghuashunConfig.MIN_STOCKS_REQUIRED:
                logger.记录错误(f"只获取到 {len(stocks)} 支股票，少于最低要求{TonghuashunConfig.MIN_STOCKS_REQUIRED}支")
                return [], None

            # 提前获取交易日期，供后续处理使用
            trading_date = get_trading_date()

            # 处理股票代码
            processed_data = self.get_stock_codes(stocks)

            # 验证处理结果的数据质量
            if not processed_data:
                logger.记录错误(f"股票代码处理失败，所有{len(stocks)}支股票都未找到对应代码")
                return [], None

            # 检查数据质量：至少要有一定比例的股票找到代码
            success_rate = len(processed_data) / len(stocks)
            if success_rate < 0.3:  # 成功率低于30%认为数据质量有问题
                logger.记录错误(f"数据质量较差，{len(stocks)}支股票中只有{len(processed_data)}支找到代码，成功率{success_rate:.1%}")

            if processed_data:
                # 写入临时表（替代CSV文件）
                try:
                    temp_manager = 获取临时表管理器()
                    temp_manager.写入同花数据(processed_data)
                    temp_manager.close()

                except Exception as e:
                    logger.记录错误(f"写入临时表失败", e)
                    return [], None

            # 返回处理后的数据，包含股票代码和名称
            return [(code, name) for code, name, _ in processed_data], trading_date

        except Exception as e:
            logger.记录错误(f"网站数据采集失败", e)
            traceback.print_exc()
            return [], None
        finally:
            # 保持浏览器开启，不关闭以便复用
            browser_manager.close()  # 这里只是清理页面状态，不会真正关闭浏览器
            # 关闭数据库连接
            self._close_db_connection()

def _trigger_popularity_fusion():
    """触发个股人气表融合模块"""
    try:
        # 调用人气融合函数
        result = 人气融合_main()

        if result:
            pass
        else:
            logger.记录错误(f"人气融合模块执行失败")

    except Exception as e:
        logger.记录错误(f"触发人气融合模块异常", e)

def main():
    try:
        crawler = THS_Crawler()
        processed_data, date = crawler.run()

        # 记录采集结果（只负责采集，不调用数据库）
        if processed_data:
            logger.记录模块执行(f"网站数据采集完成", len(processed_data))

            # 🔄 同花数据采集完成后，立即触发人气融合模块
            _trigger_popularity_fusion()
        else:
            logger.记录错误(f"网站数据采集失败，未获取到数据")
        return processed_data, date
    except Exception as e:
        logger.记录错误(f"程序异常", e)
        traceback.print_exc()
        return [], None

def run_continuous():
    """循环运行模式：按配置间隔执行"""
    try:
        while True:
            start_time = time.time()

            try:
                main()
            except Exception as e:
                logger.记录错误(f"执行失败", e)

            # 计算等待时间
            execution_time = time.time() - start_time
            sleep_time = max(0, TonghuashunConfig.CYCLE_INTERVAL - execution_time)

            if sleep_time > 0:
                time.sleep(sleep_time)
            else:
                logger.记录错误(f"执行时间{execution_time:.1f}秒超过{TonghuashunConfig.CYCLE_INTERVAL}秒间隔")

    except KeyboardInterrupt:
        pass
    except Exception as e:
        logger.记录错误(f"循环运行异常", e)
    finally:
        # 程序退出时清理浏览器资源
        SimpleBrowserManager.cleanup_shared_instance()

if __name__ == "__main__":
    import atexit

    # 注册程序退出时的清理函数
    atexit.register(SimpleBrowserManager.cleanup_shared_instance)

    try:
        if len(sys.argv) > 1 and sys.argv[1] == "--once":
            # 单次执行模式
            main()
        else:
            # 循环执行模式（默认）
            run_continuous()
    except KeyboardInterrupt:
        # 用户中断时也清理浏览器
        SimpleBrowserManager.cleanup_shared_instance()
    except Exception:
        # 异常退出时也清理浏览器
        SimpleBrowserManager.cleanup_shared_instance()
        raise