#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
TradeFusion临时表重命名脚本
将临时表命名统一为"临时表_"开头的格式

重命名映射：
- 东财人气_临时表 → 临时表_东财人气
- 同花人气_临时表 → 临时表_同花人气
- 选股宝原始_临时表 → 临时表_选股宝原始
- 选股宝清洗_临时表 → 临时表_选股宝清洗
"""

import sqlite3
import os
from pathlib import Path

# 重命名映射表
RENAME_MAPPING = {
    '东财人气_临时表': '临时表_东财人气',
    '同花人气_临时表': '临时表_同花人气',
    '选股宝原始_临时表': '临时表_选股宝原始',
    '选股宝清洗_临时表': '临时表_选股宝清洗'
}

def rename_temp_tables():
    """重命名所有临时表"""
    # 获取数据库路径
    current_file = Path(__file__).resolve()
    project_root = current_file.parent.parent
    db_path = project_root / "数据库0_实体模块" / "股票数据.db"
    
    if not db_path.exists():
        print(f"❌ 数据库文件不存在: {db_path}")
        return False
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        print("🔄 开始重命名临时表...")
        
        for old_name, new_name in RENAME_MAPPING.items():
            # 检查旧表是否存在
            cursor.execute("""
                SELECT name FROM sqlite_master 
                WHERE type='table' AND name=?
            """, (old_name,))
            
            if not cursor.fetchone():
                print(f"ℹ️  表 {old_name} 不存在，跳过")
                continue
            
            print(f"📝 重命名: {old_name} → {new_name}")
            
            # 获取旧表结构
            cursor.execute(f"PRAGMA table_info('{old_name}')")
            columns = cursor.fetchall()
            
            # 构建新表的CREATE语句
            column_defs = []
            pk_columns = []
            
            for col in columns:
                col_name = col[1]
                col_type = col[2]
                not_null = " NOT NULL" if col[3] else ""
                default_val = f" DEFAULT {col[4]}" if col[4] is not None else ""
                
                column_defs.append(f"{col_name} {col_type}{not_null}{default_val}")
                
                if col[5] > 0:  # 是主键
                    pk_columns.append(col_name)
            
            # 添加主键约束
            if pk_columns:
                column_defs.append(f"PRIMARY KEY ({', '.join(pk_columns)})")
            
            create_sql = f"CREATE TABLE {new_name} ({', '.join(column_defs)})"
            
            # 创建新表
            cursor.execute(create_sql)
            
            # 复制数据
            cursor.execute(f"INSERT INTO {new_name} SELECT * FROM {old_name}")
            copied_count = cursor.rowcount
            
            # 删除旧表
            cursor.execute(f"DROP TABLE {old_name}")
            
            print(f"✅ 完成: 迁移了 {copied_count} 条记录")
        
        # 提交更改
        conn.commit()
        print("🎉 所有临时表重命名成功！")
        
        # 验证结果
        cursor.execute("""
            SELECT name FROM sqlite_master 
            WHERE type='table' AND name LIKE '临时表_%'
            ORDER BY name
        """)
        new_tables = [row[0] for row in cursor.fetchall()]
        print(f"📊 新的临时表: {', '.join(new_tables)}")
        
        return True
        
    except Exception as e:
        print(f"❌ 重命名失败: {str(e)}")
        if conn:
            conn.rollback()
        return False
        
    finally:
        if conn:
            conn.close()

def main():
    """主函数"""
    print("=" * 60)
    print("TradeFusion临时表重命名工具")
    print("统一临时表命名格式：临时表_功能名称")
    print("=" * 60)
    
    success = rename_temp_tables()
    
    if success:
        print("\n🎯 重命名完成！")
        print("📝 接下来需要更新相关模块的引用：")
        print("   - 数据库1_基础表模块/人气临时表管理.py")
        print("   - 数据2_网络采集/选股宝抓取.py")
        print("   - 数据2_网络采集/选股宝清洗.py")
        print("   - 数据库1_基础表模块/个股解读_板块信息_关联表.py")
        print("\n⚠️  请重新运行相关模块以验证功能正常")
    else:
        print("\n❌ 重命名失败，请检查错误信息")
    
    return success

if __name__ == "__main__":
    main()
