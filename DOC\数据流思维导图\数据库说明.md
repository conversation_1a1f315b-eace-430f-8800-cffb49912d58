# TradeFusion数据库结构报告

## 基本信息

- **数据库路径**: `E:\TradeFusion\数据库0_实体模块\股票数据.db`
- **SQLite版本**: 3.45.3
- **扫描时间**: 2025-07-23 09:25:34

## 数据库概览

- **总表数**: 15张
- **总字段数**: 77个
- **总索引数**: 37个
- **有主键的表**: 15/15张
- **有索引的表**: 13/15张
- **平均每表字段数**: 5.1个

## 表汇总列表

| 序号 | 表名 | 类型 | 字段数 | 主键策略 | 索引数 |
|------|------|------|--------|----------|--------|
| 1 | DAT文件处理记录表 | 正式表 | 2 | 复合主键 | 0 |
| 2 | mcp_insights | 正式表 | 3 | 单字段主键 | 0 |
| 3 | 个股人气表 | 正式表 | 7 | 复合主键 | 4 |
| 4 | 个股接力表 | 正式表 | 5 | 复合主键 | 3 |
| 5 | 个股板块关联表 | 正式表 | 4 | 复合主键 | 5 |
| 6 | 个股解读表 | 正式表 | 3 | 复合主键 | 1 |
| 7 | 个股连板高度表 | 正式表 | 9 | 复合主键 | 2 |
| 8 | 临时表_东财人气 | 临时表 | 7 | 单字段主键 | 3 |
| 9 | 临时表_同花人气 | 临时表 | 7 | 单字段主键 | 3 |
| 10 | 临时表_选股宝原始 | 临时表 | 5 | 单字段主键 | 2 |
| 11 | 临时表_选股宝清洗 | 临时表 | 10 | 单字段主键 | 4 |
| 12 | 所属板块评分表 | 正式表 | 3 | 复合主键 | 2 |
| 13 | 板块信息表 | 正式表 | 4 | 复合主键 | 2 |
| 14 | 板块涨停表 | 正式表 | 4 | 复合主键 | 3 |
| 15 | 板块精选表 | 正式表 | 4 | 复合主键 | 3 |

## 按类型分组

### 临时表 (4张)

- 临时表_东财人气
- 临时表_同花人气
- 临时表_选股宝原始
- 临时表_选股宝清洗

### 正式表 (11张)

- DAT文件处理记录表
- mcp_insights
- 个股人气表
- 个股接力表
- 个股板块关联表
- 个股解读表
- 个股连板高度表
- 所属板块评分表
- 板块信息表
- 板块涨停表
- 板块精选表

## 字段汇总列表

| 序号 | 表名 | 所有字段列表 |
|------|------|-------------|
| 1 | DAT文件处理记录表 | 🔑主键:[日期, 股票代码] |
| 2 | mcp_insights | 🔑主键:[id] | 📝字段:[insight, created_at] |
| 3 | 个股人气表 | 🔑主键:[日期, 股票代码] | 📝字段:[东财人气排名, 同花人气排名, 综合人气评分, 股票名称, 更新时间戳] |
| 4 | 个股接力表 | 🔑主键:[日期, 股票代码] | 📝字段:[接力, 股票类型, 更新时间戳] |
| 5 | 个股板块关联表 | 🔑主键:[日期, 股票代码, 所属板块名称] | 📝字段:[更新时间戳] |
| 6 | 个股解读表 | 🔑主键:[日期, 股票代码] | 📝字段:[个股解读] |
| 7 | 个股连板高度表 | 🔑主键:[日期, 股票代码] | 📝字段:[连板高度, 涨停评分, 涨停时间, 一字板, T字板, 黄金换手, 成交金额] |
| 8 | 临时表_东财人气 | 🔑主键:[id] | 📝字段:[股票代码, 股票名称, 人气排名, 采集时间, 处理状态, 备注] |
| 9 | 临时表_同花人气 | 🔑主键:[id] | 📝字段:[股票代码, 股票名称, 人气排名, 采集时间, 处理状态, 备注] |
| 10 | 临时表_选股宝原始 | 🔑主键:[id] | 📝字段:[原始数据, 采集时间, 处理状态, 备注] |
| 11 | 临时表_选股宝清洗 | 🔑主键:[id] | 📝字段:[日期, 股票代码, 板块名称, 板块涨幅, 板块消息, 个股解读, 采集时间, 处理状态, 备注] |
| 12 | 所属板块评分表 | 🔑主键:[日期, 股票代码] | 📝字段:[所属板块评分] |
| 13 | 板块信息表 | 🔑主键:[日期, 板块名称] | 📝字段:[板块涨幅, 板块消息] |
| 14 | 板块涨停表 | 🔑主键:[日期, 板块名称] | 📝字段:[板块评分, 更新时间戳] |
| 15 | 板块精选表 | 🔑主键:[日期, 板块名称] | 📝字段:[综合评分, 更新时间戳] |

## 按表分组的字段汇总

### DAT文件处理记录表

- **类型**: 正式表
- **字段数**: 2个
- **主键字段**: 日期, 股票代码
- **普通字段**: 无

### mcp_insights

- **类型**: 正式表
- **字段数**: 3个
- **主键字段**: id
- **普通字段**: insight, created_at

### 个股人气表

- **类型**: 正式表
- **字段数**: 7个
- **主键字段**: 日期, 股票代码
- **普通字段**: 东财人气排名, 同花人气排名, 综合人气评分, 股票名称, 更新时间戳

### 个股接力表

- **类型**: 正式表
- **字段数**: 5个
- **主键字段**: 日期, 股票代码
- **普通字段**: 接力, 股票类型, 更新时间戳

### 个股板块关联表

- **类型**: 正式表
- **字段数**: 4个
- **主键字段**: 日期, 股票代码, 所属板块名称
- **普通字段**: 更新时间戳

### 个股解读表

- **类型**: 正式表
- **字段数**: 3个
- **主键字段**: 日期, 股票代码
- **普通字段**: 个股解读

### 个股连板高度表

- **类型**: 正式表
- **字段数**: 9个
- **主键字段**: 日期, 股票代码
- **普通字段**: 连板高度, 涨停评分, 涨停时间, 一字板, T字板, 黄金换手, 成交金额

### 临时表_东财人气

- **类型**: 临时表
- **字段数**: 7个
- **主键字段**: id
- **普通字段**: 股票代码, 股票名称, 人气排名, 采集时间, 处理状态, 备注

### 临时表_同花人气

- **类型**: 临时表
- **字段数**: 7个
- **主键字段**: id
- **普通字段**: 股票代码, 股票名称, 人气排名, 采集时间, 处理状态, 备注

### 临时表_选股宝原始

- **类型**: 临时表
- **字段数**: 5个
- **主键字段**: id
- **普通字段**: 原始数据, 采集时间, 处理状态, 备注

### 临时表_选股宝清洗

- **类型**: 临时表
- **字段数**: 10个
- **主键字段**: id
- **普通字段**: 日期, 股票代码, 板块名称, 板块涨幅, 板块消息, 个股解读, 采集时间, 处理状态, 备注

### 所属板块评分表

- **类型**: 正式表
- **字段数**: 3个
- **主键字段**: 日期, 股票代码
- **普通字段**: 所属板块评分

### 板块信息表

- **类型**: 正式表
- **字段数**: 4个
- **主键字段**: 日期, 板块名称
- **普通字段**: 板块涨幅, 板块消息

### 板块涨停表

- **类型**: 正式表
- **字段数**: 4个
- **主键字段**: 日期, 板块名称
- **普通字段**: 板块评分, 更新时间戳

### 板块精选表

- **类型**: 正式表
- **字段数**: 4个
- **主键字段**: 日期, 板块名称
- **普通字段**: 综合评分, 更新时间戳

## 详细表结构

### DAT文件处理记录表

**主键策略**: 复合主键 (日期, 股票代码)

**字段结构**:

| 列名 | 类型 | 非空 | 主键 |
|------|------|------|------|
| 日期 | INTEGER | 否 | 是 |
| 股票代码 | TEXT | 否 | 是 |

**索引策略**: 无额外索引

---

### mcp_insights

**主键策略**: 单字段主键 (id)

**字段结构**:

| 列名 | 类型 | 非空 | 主键 |
|------|------|------|------|
| id | INTEGER | 否 | 是 |
| insight | TEXT | 是 | 否 |
| created_at | TIMESTAMP | 否 | 否 |

**索引策略**: 无额外索引

---

### 个股人气表

**主键策略**: 复合主键 (日期, 股票代码)

**字段结构**:

| 列名 | 类型 | 非空 | 主键 |
|------|------|------|------|
| 日期 | INTEGER | 否 | 是 |
| 股票代码 | CHAR(8) | 否 | 是 |
| 东财人气排名 | INTEGER | 否 | 否 |
| 同花人气排名 | INTEGER | 否 | 否 |
| 综合人气评分 | REAL | 否 | 否 |
| 股票名称 | VARCHAR(50) | 否 | 否 |
| 更新时间戳 | TEXT | 否 | 否 |

**索引策略**:

- **idx_date_only** (普通索引)
  ```sql
  CREATE INDEX idx_date_only ON 个股人气表(日期 DESC)
  ```
- **idx_stock_code** (普通索引)
  ```sql
  CREATE INDEX idx_stock_code ON 个股人气表(股票代码)
  ```
- **idx_stock_name_date** (普通索引)
  ```sql
  CREATE INDEX idx_stock_name_date ON 个股人气表(股票名称, 日期 DESC)
  ```
- **idx_个股人气表_日期_股票** (普通索引)
  ```sql
  CREATE INDEX idx_个股人气表_日期_股票 
ON 个股人气表(日期, 股票代码)
  ```

---

### 个股接力表

**主键策略**: 复合主键 (日期, 股票代码)

**字段结构**:

| 列名 | 类型 | 非空 | 主键 |
|------|------|------|------|
| 日期 | INTEGER | 是 | 是 |
| 股票代码 | CHAR(8) | 是 | 是 |
| 接力 | INTEGER | 否 | 否 |
| 股票类型 | TEXT | 否 | 否 |
| 更新时间戳 | TEXT | 否 | 否 |

**索引策略**:

- **idx_个股接力表_日期_接力值** (普通索引)
  ```sql
  CREATE INDEX idx_个股接力表_日期_接力值 ON 个股接力表(日期, 接力 DESC)
  ```
- **idx_个股接力表_更新时间戳** (普通索引)
  ```sql
  CREATE INDEX idx_个股接力表_更新时间戳
ON 个股接力表(更新时间戳)
  ```
- **idx_个股接力表_股票类型** (普通索引)
  ```sql
  CREATE INDEX idx_个股接力表_股票类型 ON 个股接力表(股票类型)
  ```

---

### 个股板块关联表

**主键策略**: 复合主键 (日期, 股票代码, 所属板块名称)

**字段结构**:

| 列名 | 类型 | 非空 | 主键 |
|------|------|------|------|
| 日期 | INTEGER | 否 | 是 |
| 股票代码 | CHAR(8) | 否 | 是 |
| 所属板块名称 | TEXT | 否 | 是 |
| 更新时间戳 | TEXT | 否 | 否 |

**索引策略**:

- **idx_个股板块关联表_日期_股票** (普通索引)
  ```sql
  CREATE INDEX idx_个股板块关联表_日期_股票 
ON 个股板块关联表(日期, 股票代码)
  ```
- **idx_个股板块关联表_更新时间戳** (普通索引)
  ```sql
  CREATE INDEX idx_个股板块关联表_更新时间戳
ON 个股板块关联表(更新时间戳)
  ```
- **idx_关联表_日期** (普通索引)
  ```sql
  CREATE INDEX idx_关联表_日期 ON 个股板块关联表(日期)
  ```
- **个股板块关联表_所属板块名称索引** (普通索引)
  ```sql
  CREATE INDEX 个股板块关联表_所属板块名称索引 ON 个股板块关联表 (所属板块名称)
  ```
- **个股板块关联表_股票代码索引** (普通索引)
  ```sql
  CREATE INDEX 个股板块关联表_股票代码索引 ON 个股板块关联表 (股票代码)
  ```

---

### 个股解读表

**主键策略**: 复合主键 (日期, 股票代码)

**字段结构**:

| 列名 | 类型 | 非空 | 主键 |
|------|------|------|------|
| 日期 | INTEGER | 否 | 是 |
| 股票代码 | CHAR(8) | 否 | 是 |
| 个股解读 | TEXT | 否 | 否 |

**索引策略**:

- **idx_个股解读表_股票代码** (普通索引)
  ```sql
  CREATE INDEX idx_个股解读表_股票代码 ON 个股解读表(股票代码)
  ```

---

### 个股连板高度表

**主键策略**: 复合主键 (日期, 股票代码)

**字段结构**:

| 列名 | 类型 | 非空 | 主键 |
|------|------|------|------|
| 日期 | INTEGER | 否 | 是 |
| 股票代码 | CHAR(8) | 否 | 是 |
| 连板高度 | REAL | 否 | 否 |
| 涨停评分 | REAL | 否 | 否 |
| 涨停时间 | REAL | 否 | 否 |
| 一字板 | REAL | 否 | 否 |
| T字板 | REAL | 否 | 否 |
| 黄金换手 | REAL | 否 | 否 |
| 成交金额 | REAL | 否 | 否 |

**索引策略**:

- **idx_个股连板高度表_日期_股票** (普通索引)
  ```sql
  CREATE INDEX idx_个股连板高度表_日期_股票 ON 个股连板高度表(日期, 股票代码)
  ```
- **idx_个股连板高度表_涨停评分** (普通索引)
  ```sql
  CREATE INDEX idx_个股连板高度表_涨停评分 ON 个股连板高度表(涨停评分 DESC)
  ```

---

### 临时表_东财人气

**主键策略**: 单字段主键 (id)

**字段结构**:

| 列名 | 类型 | 非空 | 主键 |
|------|------|------|------|
| id | INTEGER | 否 | 是 |
| 股票代码 | TEXT | 是 | 否 |
| 股票名称 | TEXT | 是 | 否 |
| 人气排名 | INTEGER | 是 | 否 |
| 采集时间 | TIMESTAMP | 否 | 否 |
| 处理状态 | TEXT | 否 | 否 |
| 备注 | TEXT | 否 | 否 |

**索引策略**:

- **idx_临时表_东财人气_处理状态** (普通索引)
  ```sql
  CREATE INDEX idx_临时表_东财人气_处理状态 ON 临时表_东财人气(处理状态)
  ```
- **idx_临时表_东财人气_股票代码** (普通索引)
  ```sql
  CREATE INDEX idx_临时表_东财人气_股票代码 ON 临时表_东财人气(股票代码)
  ```
- **idx_临时表_东财人气_采集时间** (普通索引)
  ```sql
  CREATE INDEX idx_临时表_东财人气_采集时间 ON 临时表_东财人气(采集时间)
  ```

---

### 临时表_同花人气

**主键策略**: 单字段主键 (id)

**字段结构**:

| 列名 | 类型 | 非空 | 主键 |
|------|------|------|------|
| id | INTEGER | 否 | 是 |
| 股票代码 | TEXT | 是 | 否 |
| 股票名称 | TEXT | 是 | 否 |
| 人气排名 | INTEGER | 是 | 否 |
| 采集时间 | TIMESTAMP | 否 | 否 |
| 处理状态 | TEXT | 否 | 否 |
| 备注 | TEXT | 否 | 否 |

**索引策略**:

- **idx_临时表_同花人气_处理状态** (普通索引)
  ```sql
  CREATE INDEX idx_临时表_同花人气_处理状态 ON 临时表_同花人气(处理状态)
  ```
- **idx_临时表_同花人气_股票代码** (普通索引)
  ```sql
  CREATE INDEX idx_临时表_同花人气_股票代码 ON 临时表_同花人气(股票代码)
  ```
- **idx_临时表_同花人气_采集时间** (普通索引)
  ```sql
  CREATE INDEX idx_临时表_同花人气_采集时间 ON 临时表_同花人气(采集时间)
  ```

---

### 临时表_选股宝原始

**主键策略**: 单字段主键 (id)

**字段结构**:

| 列名 | 类型 | 非空 | 主键 |
|------|------|------|------|
| id | INTEGER | 否 | 是 |
| 原始数据 | TEXT | 是 | 否 |
| 采集时间 | TIMESTAMP | 否 | 否 |
| 处理状态 | TEXT | 否 | 否 |
| 备注 | TEXT | 否 | 否 |

**索引策略**:

- **idx_临时表_选股宝原始_处理状态** (普通索引)
  ```sql
  CREATE INDEX idx_临时表_选股宝原始_处理状态 ON 临时表_选股宝原始(处理状态)
  ```
- **idx_临时表_选股宝原始_采集时间** (普通索引)
  ```sql
  CREATE INDEX idx_临时表_选股宝原始_采集时间 ON 临时表_选股宝原始(采集时间)
  ```

---

### 临时表_选股宝清洗

**主键策略**: 单字段主键 (id)

**字段结构**:

| 列名 | 类型 | 非空 | 主键 |
|------|------|------|------|
| id | INTEGER | 否 | 是 |
| 日期 | INTEGER | 是 | 否 |
| 股票代码 | TEXT | 是 | 否 |
| 板块名称 | TEXT | 是 | 否 |
| 板块涨幅 | TEXT | 是 | 否 |
| 板块消息 | TEXT | 是 | 否 |
| 个股解读 | TEXT | 是 | 否 |
| 采集时间 | TIMESTAMP | 否 | 否 |
| 处理状态 | TEXT | 否 | 否 |
| 备注 | TEXT | 否 | 否 |

**索引策略**:

- **idx_临时表_选股宝清洗_处理状态** (普通索引)
  ```sql
  CREATE INDEX idx_临时表_选股宝清洗_处理状态 ON 临时表_选股宝清洗(处理状态)
  ```
- **idx_临时表_选股宝清洗_日期** (普通索引)
  ```sql
  CREATE INDEX idx_临时表_选股宝清洗_日期 ON 临时表_选股宝清洗(日期)
  ```
- **idx_临时表_选股宝清洗_板块名称** (普通索引)
  ```sql
  CREATE INDEX idx_临时表_选股宝清洗_板块名称 ON 临时表_选股宝清洗(板块名称)
  ```
- **idx_临时表_选股宝清洗_股票代码** (普通索引)
  ```sql
  CREATE INDEX idx_临时表_选股宝清洗_股票代码 ON 临时表_选股宝清洗(股票代码)
  ```

---

### 所属板块评分表

**主键策略**: 复合主键 (日期, 股票代码)

**字段结构**:

| 列名 | 类型 | 非空 | 主键 |
|------|------|------|------|
| 日期 | INTEGER | 是 | 是 |
| 股票代码 | CHAR(8) | 是 | 是 |
| 所属板块评分 | INTEGER | 否 | 否 |

**索引策略**:

- **idx_所属板块评分表_所属板块评分** (普通索引)
  ```sql
  CREATE INDEX idx_所属板块评分表_所属板块评分 ON 所属板块评分表(所属板块评分 DESC)
  ```
- **idx_所属板块评分表_日期_股票** (普通索引)
  ```sql
  CREATE INDEX idx_所属板块评分表_日期_股票 ON 所属板块评分表(日期, 股票代码)
  ```

---

### 板块信息表

**主键策略**: 复合主键 (日期, 板块名称)

**字段结构**:

| 列名 | 类型 | 非空 | 主键 |
|------|------|------|------|
| 日期 | INTEGER | 否 | 是 |
| 板块名称 | TEXT | 否 | 是 |
| 板块涨幅 | REAL | 否 | 否 |
| 板块消息 | TEXT | 否 | 否 |

**索引策略**:

- **idx_板块信息表_板块名称_日期** (普通索引)
  ```sql
  CREATE INDEX idx_板块信息表_板块名称_日期 ON 板块信息表(板块名称, 日期 DESC)
  ```
- **板块名称索引** (普通索引)
  ```sql
  CREATE INDEX 板块名称索引 ON 板块信息表 (板块名称)
  ```

---

### 板块涨停表

**主键策略**: 复合主键 (日期, 板块名称)

**字段结构**:

| 列名 | 类型 | 非空 | 主键 |
|------|------|------|------|
| 日期 | INTEGER | 是 | 是 |
| 板块名称 | TEXT | 是 | 是 |
| 板块评分 | REAL | 否 | 否 |
| 更新时间戳 | TEXT | 否 | 否 |

**索引策略**:

- **idx_板块涨停表_更新时间戳** (普通索引)
  ```sql
  CREATE INDEX idx_板块涨停表_更新时间戳
ON 板块涨停表(更新时间戳)
  ```
- **idx_板块涨停表_板块评分** (普通索引)
  ```sql
  CREATE INDEX idx_板块涨停表_板块评分 ON 板块涨停表(板块评分 DESC)
  ```
- **idx_涨停表_日期板块** (普通索引)
  ```sql
  CREATE INDEX idx_涨停表_日期板块 ON 板块涨停表(日期, 板块名称)
  ```

---

### 板块精选表

**主键策略**: 复合主键 (日期, 板块名称)

**字段结构**:

| 列名 | 类型 | 非空 | 主键 |
|------|------|------|------|
| 日期 | INTEGER | 是 | 是 |
| 板块名称 | TEXT | 是 | 是 |
| 综合评分 | REAL | 否 | 否 |
| 更新时间戳 | TEXT | 否 | 否 |

**索引策略**:

- **idx_板块精选表_更新时间戳** (普通索引)
  ```sql
  CREATE INDEX idx_板块精选表_更新时间戳
ON 板块精选表(更新时间戳)
  ```
- **idx_板块精选表_综合评分** (普通索引)
  ```sql
  CREATE INDEX idx_板块精选表_综合评分 ON 板块精选表(综合评分 DESC)
  ```
- **idx_精选表_日期** (普通索引)
  ```sql
  CREATE INDEX idx_精选表_日期 
                    ON [板块精选表](日期)
                
  ```

---
