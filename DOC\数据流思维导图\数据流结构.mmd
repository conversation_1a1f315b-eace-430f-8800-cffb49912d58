graph TD
    %% TradeFusion 数据流结构思维导图
    %% 创建时间：2025-07-23
    %% 版本：v1.0 - 样式分离管理版
    
    %% ===== 样式定义区域 =====

    %% 模块节点样式
    classDef moduleStyle fill:#e1f5fe,stroke:#01579b,stroke-width:2px,color:#000
    classDef dbStyle fill:#f3e5f5,stroke:#4a148c,stroke-width:2px,color:#000

    %% 数据流样式配置（实线连接 -->）
    classDef dataFlowStyle stroke:#2e7d32,stroke-width:3px
    classDef dataFlowHeavy stroke:#1b5e20,stroke-width:4px
    classDef dataFlowLight stroke:#4caf50,stroke-width:2px

    %% 调用关系样式配置（虚线连接 -.->）
    classDef callFlowStyle stroke:#d84315,stroke-width:2px,stroke-dasharray: 5 5
    classDef callFlowTrigger stroke:#ff5722,stroke-width:3px,stroke-dasharray: 8 4
    classDef callFlowSchedule stroke:#9c27b0,stroke-width:1px,stroke-dasharray: 3 3

    %% 数据采集层模块
    A1[🔍 采集_本地数据.py<br/>12秒高频采集<br/>本地.dat文件<br/>📋调用逻辑：写入C1后调用B3]
    A2[🔍 人气_东财采集.py<br/>60秒网络爬虫<br/>东方财富热榜<br/>📋调用逻辑：写入C2后调用B2]
    A3[🔍 人气_同花采集.py<br/>60秒网络爬虫<br/>同花顺热股榜<br/>📋调用逻辑：写入C3后调用B2]
    A4[🔍 选股宝抓取.py<br/>60秒自动循环爬虫<br/>选股宝涨停板块<br/>📋调用逻辑：写入C4后调用B1→D1]

    %% 数据处理层模块
    B1[⚙️ 选股宝清洗.py<br/>由A4自动触发<br/>文本解析清洗<br/>📋调用逻辑：写入C5后调用D1]
    B2[⚙️ 个股人气表.py<br/>由A2+A3自动触发<br/>多源数据融合<br/>📋调用逻辑：写入C6后调用E1+E2]
    B3[⚙️ 板块涨停表.py<br/>由A1自动触发<br/>板块评分统计<br/>📋调用逻辑：写入C7后调用D2→D3→D4]

    %% 数据存储层表
    C1[(💾 个股连板高度表<br/>日期,股票代码,连板高度<br/>涨停评分,涨停时间等)]
    C2[(💾 临时表_东财人气<br/>股票代码,股票名称,人气排名)]
    C3[(💾 临时表_同花人气<br/>股票代码,股票名称,人气排名)]
    C4[(💾 临时表_选股宝原始<br/>原始网页文本数据)]
    C5[(💾 临时表_选股宝清洗<br/>日期,股票代码,板块名称<br/>板块涨幅,板块消息,个股解读)]
    C6[(💾 个股人气表<br/>日期,股票代码,东财人气排名<br/>同花人气排名,综合人气评分)]
    C7[(💾 板块涨停表<br/>日期,板块名称,板块评分<br/>只保留前7名板块)]
    C8[(💾 个股板块关联表<br/>日期,股票代码,所属板块名称)]
    C9[(💾 个股解读表<br/>日期,股票代码,个股解读)]
    C10[(💾 板块信息表<br/>日期,板块名称,板块涨幅,板块消息)]
    C11[(💾 所属板块评分表<br/>日期,股票代码,所属板块评分)]
    C12[(💾 板块精选表<br/>日期,板块名称,综合评分<br/>SMA算法精选结果)]
    C13[(💾 个股接力表<br/>日期,股票代码,接力,股票类型<br/>X支+Y支股票接力值)]

    %% 业务逻辑层模块
    D1[🧠 个股解读_板块信息_关联表.py<br/>由B1自动触发<br/>数据库导入处理<br/>📋调用逻辑：写入C8+C9+C10后调用E5]
    D2[🧠 A3_所属板块评分表.py<br/>由B3自动触发<br/>板块评分计算<br/>📋调用逻辑：写入C11后调用E4]
    D3[🧠 板块精选.py<br/>由B3自动触发<br/>SMA算法精选板块<br/>📋调用逻辑：写入C12后调用D4]
    D4[🧠 个股接力表.py<br/>由D3自动触发<br/>X支+Y支接力计算<br/>📋调用逻辑：写入C13后调用E3]

    %% 输出应用层模块
    E1[📊 综合人气190.py<br/>由B2自动触发<br/>生成190人气.dat文件<br/>📋调用逻辑：无后续调用]
    E2[📊 生成DZH3人气板块.py<br/>由B2自动触发<br/>生成自选股人气.BLK文件<br/>📋调用逻辑：无后续调用]
    E3[📊 接力.py<br/>由D4自动触发<br/>生成接力.dat文件<br/>📋调用逻辑：无后续调用]
    E4[📊 A4_所属板块强度.py<br/>由D2自动触发<br/>生成所属板块强度.dat文件<br/>📋调用逻辑：无后续调用]
    E5[📊 选股宝_大智慧str.py<br/>由D1自动触发<br/>生成选股宝STR文件<br/>📋调用逻辑：无后续调用]

    %% ===== 数据流连接（实线 -->）=====

    %% 采集层到存储层 - 标准数据流
    A1 --> C1
    A2 --> C2
    A3 --> C3
    A4 --> C4

    %% 处理层数据流 - 重要数据流（加粗显示）
    C4 --> B1
    B1 --> C5
    C2 --> B2
    C3 --> B2
    B2 --> C6

    %% 业务逻辑层数据流 - 标准数据流
    C5 --> D1
    D1 --> C8
    D1 --> C9
    D1 --> C10
    C1 --> B3
    C8 --> B3
    B3 --> C7
    C7 --> D2
    C8 --> D2
    D2 --> C11
    C7 --> D3
    D3 --> C12

    %% 核心计算数据流 - 重要数据流（加粗显示）
    C12 --> D4
    C6 --> D4
    C8 --> D4
    D4 --> C13

    %% 输出层数据流 - 轻量数据流
    C6 --> E1
    C6 --> E2
    C13 --> E3
    C11 --> E4
    C9 --> E5
    C10 --> E5
    C8 --> E5

    %% ===== 调用关系连接（虚线 -.->）=====

    %% 采集层自动触发关系 - 触发样式（加粗虚线）
    A1 -.-> B3
    A2 -.-> B2
    A3 -.-> B2
    A4 -.-> B1

    %% 处理层自动触发关系 - 触发样式（加粗虚线）
    B1 -.-> D1
    B3 -.-> D2
    B3 -.-> D3
    D3 -.-> D4

    %% 输出层调用关系 - 标准调用样式
    B2 -.-> E1
    B2 -.-> E2
    D4 -.-> E3
    D2 -.-> E4
    D1 -.-> E5

    %% ===== 节点样式应用 =====
    class A1,A2,A3,A4,B1,B2,B3,D1,D2,D3,D4,E1,E2,E3,E4,E5 moduleStyle
    class C1,C2,C3,C4,C5,C6,C7,C8,C9,C10,C11,C12,C13 dbStyle

    %% ===== 连接线样式应用 =====

    %% 数据流样式应用（实线）
    %% 采集层到存储层 - 标准数据流（绿色，3px）
    linkStyle 0 stroke:#2e7d32,stroke-width:3px
    linkStyle 1 stroke:#2e7d32,stroke-width:3px
    linkStyle 2 stroke:#2e7d32,stroke-width:3px
    linkStyle 3 stroke:#2e7d32,stroke-width:3px

    %% 处理层数据流 - 重要数据流（深绿色，4px）
    linkStyle 4 stroke:#1b5e20,stroke-width:4px
    linkStyle 5 stroke:#1b5e20,stroke-width:4px
    linkStyle 6 stroke:#1b5e20,stroke-width:4px
    linkStyle 7 stroke:#1b5e20,stroke-width:4px
    linkStyle 8 stroke:#1b5e20,stroke-width:4px

    %% 业务逻辑层数据流 - 标准数据流（绿色，3px）
    linkStyle 9 stroke:#2e7d32,stroke-width:3px
    linkStyle 10 stroke:#2e7d32,stroke-width:3px
    linkStyle 11 stroke:#2e7d32,stroke-width:3px
    linkStyle 12 stroke:#2e7d32,stroke-width:3px
    linkStyle 13 stroke:#2e7d32,stroke-width:3px
    linkStyle 14 stroke:#2e7d32,stroke-width:3px
    linkStyle 15 stroke:#2e7d32,stroke-width:3px
    linkStyle 16 stroke:#2e7d32,stroke-width:3px
    linkStyle 17 stroke:#2e7d32,stroke-width:3px
    linkStyle 18 stroke:#2e7d32,stroke-width:3px
    linkStyle 19 stroke:#2e7d32,stroke-width:3px
    linkStyle 20 stroke:#2e7d32,stroke-width:3px

    %% 核心计算数据流 - 重要数据流（深绿色，4px）
    linkStyle 21 stroke:#1b5e20,stroke-width:4px
    linkStyle 22 stroke:#1b5e20,stroke-width:4px
    linkStyle 23 stroke:#1b5e20,stroke-width:4px
    linkStyle 24 stroke:#1b5e20,stroke-width:4px

    %% 输出层数据流 - 轻量数据流（浅绿色，2px）
    linkStyle 25 stroke:#4caf50,stroke-width:2px
    linkStyle 26 stroke:#4caf50,stroke-width:2px
    linkStyle 27 stroke:#4caf50,stroke-width:2px
    linkStyle 28 stroke:#4caf50,stroke-width:2px
    linkStyle 29 stroke:#4caf50,stroke-width:2px
    linkStyle 30 stroke:#4caf50,stroke-width:2px
    linkStyle 31 stroke:#4caf50,stroke-width:2px

    %% 调用关系样式应用（虚线）
    %% 采集层自动触发关系 - 触发样式（红色，3px，虚线8-4）
    linkStyle 32 stroke:#ff5722,stroke-width:3px,stroke-dasharray:8 4
    linkStyle 33 stroke:#ff5722,stroke-width:3px,stroke-dasharray:8 4
    linkStyle 34 stroke:#ff5722,stroke-width:3px,stroke-dasharray:8 4
    linkStyle 35 stroke:#ff5722,stroke-width:3px,stroke-dasharray:8 4

    %% 处理层自动触发关系 - 触发样式（红色，3px，虚线8-4）
    linkStyle 36 stroke:#ff5722,stroke-width:3px,stroke-dasharray:8 4
    linkStyle 37 stroke:#ff5722,stroke-width:3px,stroke-dasharray:8 4
    linkStyle 38 stroke:#ff5722,stroke-width:3px,stroke-dasharray:8 4
    linkStyle 39 stroke:#ff5722,stroke-width:3px,stroke-dasharray:8 4

    %% 输出层调用关系 - 标准调用样式（橙红色，2px，虚线5-5）
    linkStyle 40 stroke:#d84315,stroke-width:2px,stroke-dasharray:5 5
    linkStyle 41 stroke:#d84315,stroke-width:2px,stroke-dasharray:5 5
    linkStyle 42 stroke:#d84315,stroke-width:2px,stroke-dasharray:5 5
    linkStyle 43 stroke:#d84315,stroke-width:2px,stroke-dasharray:5 5
    linkStyle 44 stroke:#d84315,stroke-width:2px,stroke-dasharray:5 5

    %% ===== 样式应用说明 =====
    %%
    %% 数据流样式（实线 -->）：
    %% - dataFlowStyle: 标准数据流（绿色，3px）
    %% - dataFlowHeavy: 重要数据流（深绿色，4px）
    %% - dataFlowLight: 轻量数据流（浅绿色，2px）
    %%
    %% 调用关系样式（虚线 -.->）：
    %% - callFlowStyle: 标准调用关系（橙红色，2px，虚线5-5）
    %% - callFlowTrigger: 自动触发关系（红色，3px，虚线8-4）
    %% - callFlowSchedule: 调度器调用（紫色，1px，虚线3-3）
    %%
    %% 图例说明：
    %% 🔲 模块：方块表示 [模块名]
    %% 🗄️ 数据库：圆柱体表示 [(数据库名)]
    %% ➡️ 数据流：实线表示 --> （绿色系）
    %% ⚡ 调用关系：虚线表示 -.-> （橙红色系）
