/**
 * 用户体验增强模块
 * 
 * 功能：快捷键、撤销重做、主题切换
 * 职责：提升用户操作体验和界面交互
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 */

class UserExperience {
    constructor(app) {
        this.app = app;
        this.shortcuts = new Map();
        this.history = [];
        this.historyIndex = -1;
        this.maxHistorySize = 50;
        this.currentTheme = 'light';
        this.themes = new Map();
        
        this.initializeUX();
    }

    /**
     * 初始化用户体验功能
     */
    initializeUX() {
        // 初始化快捷键
        this.initializeShortcuts();
        
        // 初始化撤销重做
        this.initializeHistory();
        
        // 初始化主题系统
        this.initializeThemes();
        
        // 绑定事件
        this.bindEvents();
        
        console.log('[UserExperience] 用户体验增强已初始化');
    }

    /**
     * 初始化快捷键
     */
    initializeShortcuts() {
        // 注册默认快捷键
        this.registerShortcut('Ctrl+S', () => {
            this.saveCurrentState();
            this.app.showNotification('保存', '当前状态已保存', 'success');
        }, '保存当前状态');

        this.registerShortcut('Ctrl+Z', () => {
            this.undo();
        }, '撤销操作');

        this.registerShortcut('Ctrl+Y', () => {
            this.redo();
        }, '重做操作');

        this.registerShortcut('Ctrl+Shift+R', () => {
            this.app.renderer?.render();
            this.app.showNotification('刷新', '界面已刷新', 'info');
        }, '刷新界面');

        this.registerShortcut('Ctrl+Shift+C', () => {
            this.app.toggleConnectionsVisibility();
        }, '切换连接线显示');

        this.registerShortcut('F1', () => {
            this.showHelpDialog();
        }, '显示帮助');

        this.registerShortcut('Ctrl+Shift+T', () => {
            this.toggleTheme();
        }, '切换主题');

        this.registerShortcut('Ctrl+Shift+P', () => {
            this.showPerformanceReport();
        }, '显示性能报告');

        this.registerShortcut('Escape', () => {
            this.closeAllDialogs();
        }, '关闭对话框');

        console.log('[UserExperience] 快捷键已注册');
    }

    /**
     * 注册快捷键
     */
    registerShortcut(combination, callback, description = '') {
        this.shortcuts.set(combination, {
            callback,
            description,
            registeredAt: Date.now()
        });
    }

    /**
     * 处理键盘事件
     */
    handleKeyDown(event) {
        const combination = this.getKeyCombination(event);
        const shortcut = this.shortcuts.get(combination);
        
        if (shortcut) {
            event.preventDefault();
            event.stopPropagation();
            
            try {
                shortcut.callback(event);
                console.log(`[UserExperience] 执行快捷键: ${combination}`);
            } catch (error) {
                console.error(`[UserExperience] 快捷键执行失败 ${combination}:`, error);
            }
        }
    }

    /**
     * 获取按键组合
     */
    getKeyCombination(event) {
        const parts = [];
        
        if (event.ctrlKey) parts.push('Ctrl');
        if (event.altKey) parts.push('Alt');
        if (event.shiftKey) parts.push('Shift');
        if (event.metaKey) parts.push('Meta');
        
        // 特殊键
        const specialKeys = {
            27: 'Escape',
            112: 'F1', 113: 'F2', 114: 'F3', 115: 'F4',
            116: 'F5', 117: 'F6', 118: 'F7', 119: 'F8',
            120: 'F9', 121: 'F10', 122: 'F11', 123: 'F12'
        };
        
        if (specialKeys[event.keyCode]) {
            parts.push(specialKeys[event.keyCode]);
        } else if (event.key.length === 1) {
            parts.push(event.key.toUpperCase());
        }
        
        return parts.join('+');
    }

    /**
     * 初始化历史记录
     */
    initializeHistory() {
        // 监听状态变化事件
        this.app.on('module:position-changed', (data) => {
            this.recordAction('move-module', {
                moduleId: data.moduleId,
                oldPosition: data.oldPosition,
                newPosition: data.newPosition
            });
        });

        this.app.on('module:added', (data) => {
            this.recordAction('add-module', {
                moduleId: data.moduleId,
                config: data.config
            });
        });

        this.app.on('module:removed', (data) => {
            this.recordAction('remove-module', {
                moduleId: data.moduleId,
                config: data.config
            });
        });

        this.app.on('connection:added', (data) => {
            this.recordAction('add-connection', {
                from: data.from,
                to: data.to,
                type: data.type
            });
        });

        this.app.on('connection:removed', (data) => {
            this.recordAction('remove-connection', {
                from: data.from,
                to: data.to,
                type: data.type
            });
        });
    }

    /**
     * 记录操作
     */
    recordAction(type, data) {
        // 移除当前位置之后的历史记录
        if (this.historyIndex < this.history.length - 1) {
            this.history = this.history.slice(0, this.historyIndex + 1);
        }
        
        // 添加新操作
        const action = {
            type,
            data,
            timestamp: Date.now(),
            id: this.generateActionId()
        };
        
        this.history.push(action);
        this.historyIndex++;
        
        // 限制历史记录大小
        if (this.history.length > this.maxHistorySize) {
            this.history.shift();
            this.historyIndex--;
        }
        
        // 触发历史变化事件
        this.app.emit('history:changed', {
            canUndo: this.canUndo(),
            canRedo: this.canRedo(),
            historySize: this.history.length
        });
    }

    /**
     * 撤销操作
     */
    undo() {
        if (!this.canUndo()) {
            this.app.showNotification('撤销', '没有可撤销的操作', 'warning');
            return;
        }
        
        const action = this.history[this.historyIndex];
        this.historyIndex--;
        
        try {
            this.executeUndoAction(action);
            this.app.showNotification('撤销', `已撤销: ${this.getActionDescription(action)}`, 'info');
            console.log(`[UserExperience] 撤销操作: ${action.type}`);
        } catch (error) {
            console.error('[UserExperience] 撤销操作失败:', error);
            this.app.showNotification('撤销失败', error.message, 'error');
            this.historyIndex++; // 恢复索引
        }
        
        this.app.emit('history:changed', {
            canUndo: this.canUndo(),
            canRedo: this.canRedo(),
            historySize: this.history.length
        });
    }

    /**
     * 重做操作
     */
    redo() {
        if (!this.canRedo()) {
            this.app.showNotification('重做', '没有可重做的操作', 'warning');
            return;
        }
        
        this.historyIndex++;
        const action = this.history[this.historyIndex];
        
        try {
            this.executeRedoAction(action);
            this.app.showNotification('重做', `已重做: ${this.getActionDescription(action)}`, 'info');
            console.log(`[UserExperience] 重做操作: ${action.type}`);
        } catch (error) {
            console.error('[UserExperience] 重做操作失败:', error);
            this.app.showNotification('重做失败', error.message, 'error');
            this.historyIndex--; // 恢复索引
        }
        
        this.app.emit('history:changed', {
            canUndo: this.canUndo(),
            canRedo: this.canRedo(),
            historySize: this.history.length
        });
    }

    /**
     * 执行撤销动作
     */
    executeUndoAction(action) {
        switch (action.type) {
            case 'move-module':
                this.undoMoveModule(action.data);
                break;
            case 'add-module':
                this.undoAddModule(action.data);
                break;
            case 'remove-module':
                this.undoRemoveModule(action.data);
                break;
            case 'add-connection':
                this.undoAddConnection(action.data);
                break;
            case 'remove-connection':
                this.undoRemoveConnection(action.data);
                break;
            default:
                throw new Error(`未知的撤销操作类型: ${action.type}`);
        }
    }

    /**
     * 执行重做动作
     */
    executeRedoAction(action) {
        switch (action.type) {
            case 'move-module':
                this.redoMoveModule(action.data);
                break;
            case 'add-module':
                this.redoAddModule(action.data);
                break;
            case 'remove-module':
                this.redoRemoveModule(action.data);
                break;
            case 'add-connection':
                this.redoAddConnection(action.data);
                break;
            case 'remove-connection':
                this.redoRemoveConnection(action.data);
                break;
            default:
                throw new Error(`未知的重做操作类型: ${action.type}`);
        }
    }

    /**
     * 撤销模块移动
     */
    undoMoveModule(data) {
        if (this.app.positionManager) {
            this.app.positionManager.setModulePosition(data.moduleId, data.oldPosition);
        }
    }

    /**
     * 重做模块移动
     */
    redoMoveModule(data) {
        if (this.app.positionManager) {
            this.app.positionManager.setModulePosition(data.moduleId, data.newPosition);
        }
    }

    /**
     * 初始化主题系统
     */
    initializeThemes() {
        // 注册默认主题
        this.registerTheme('light', {
            name: '浅色主题',
            variables: {
                '--bg-primary': '#ffffff',
                '--bg-secondary': '#f8fafc',
                '--text-primary': '#1e293b',
                '--text-secondary': '#64748b',
                '--border-color': '#e2e8f0',
                '--accent-color': '#3b82f6'
            }
        });

        this.registerTheme('dark', {
            name: '深色主题',
            variables: {
                '--bg-primary': '#1e293b',
                '--bg-secondary': '#0f172a',
                '--text-primary': '#f1f5f9',
                '--text-secondary': '#94a3b8',
                '--border-color': '#334155',
                '--accent-color': '#60a5fa'
            }
        });

        // 加载保存的主题
        const savedTheme = localStorage.getItem('tradefusion_theme');
        if (savedTheme && this.themes.has(savedTheme)) {
            this.applyTheme(savedTheme);
        }
    }

    /**
     * 注册主题
     */
    registerTheme(id, theme) {
        this.themes.set(id, theme);
    }

    /**
     * 应用主题
     */
    applyTheme(themeId) {
        const theme = this.themes.get(themeId);
        if (!theme) {
            console.warn(`[UserExperience] 主题不存在: ${themeId}`);
            return;
        }
        
        const root = document.documentElement;
        
        // 应用CSS变量
        Object.entries(theme.variables).forEach(([variable, value]) => {
            root.style.setProperty(variable, value);
        });
        
        this.currentTheme = themeId;
        localStorage.setItem('tradefusion_theme', themeId);
        
        // 添加主题类
        document.body.className = document.body.className.replace(/theme-\w+/g, '');
        document.body.classList.add(`theme-${themeId}`);
        
        this.app.emit('theme:changed', { themeId, theme });
        console.log(`[UserExperience] 主题已切换: ${theme.name}`);
    }

    /**
     * 切换主题
     */
    toggleTheme() {
        const nextTheme = this.currentTheme === 'light' ? 'dark' : 'light';
        this.applyTheme(nextTheme);
        this.app.showNotification('主题', `已切换到${this.themes.get(nextTheme).name}`, 'info');
    }

    /**
     * 显示帮助对话框
     */
    showHelpDialog() {
        const shortcuts = Array.from(this.shortcuts.entries())
            .map(([key, shortcut]) => `<tr><td><code>${key}</code></td><td>${shortcut.description}</td></tr>`)
            .join('');
        
        const helpHTML = `
            <div style="max-width: 600px;">
                <h3>🔧 快捷键列表</h3>
                <table style="width: 100%; border-collapse: collapse;">
                    <thead>
                        <tr style="background: #f8fafc;">
                            <th style="padding: 8px; text-align: left; border: 1px solid #e2e8f0;">快捷键</th>
                            <th style="padding: 8px; text-align: left; border: 1px solid #e2e8f0;">功能</th>
                        </tr>
                    </thead>
                    <tbody>
                        ${shortcuts}
                    </tbody>
                </table>
            </div>
        `;
        
        this.showDialog('帮助', helpHTML);
    }

    /**
     * 显示性能报告
     */
    showPerformanceReport() {
        if (this.app.getPerformanceReport) {
            const report = this.app.getPerformanceReport();
            if (report) {
                const reportHTML = `
                    <div style="max-width: 500px;">
                        <h3>📊 性能报告</h3>
                        <p><strong>内存使用:</strong> ${report.summary.memory.current?.usage || 'N/A'}</p>
                        <p><strong>平均渲染时间:</strong> ${report.summary.rendering.averageTime}ms</p>
                        <p><strong>连接线更新时间:</strong> ${report.summary.connections.averageUpdateTime}ms</p>
                        <p><strong>错误数量:</strong> ${report.summary.errors.total}</p>
                    </div>
                `;
                this.showDialog('性能报告', reportHTML);
            }
        }
    }

    /**
     * 显示对话框
     */
    showDialog(title, content) {
        const dialogId = 'ux-dialog-' + Date.now();
        const dialogHTML = `
            <div id="${dialogId}" style="position: fixed; top: 0; left: 0; width: 100%; height: 100%; 
                 background: rgba(0,0,0,0.5); z-index: 10000; display: flex; align-items: center; justify-content: center;">
                <div style="background: white; padding: 24px; border-radius: 12px; max-height: 80vh; overflow-y: auto;">
                    <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 16px;">
                        <h2 style="margin: 0; color: #1e293b;">${title}</h2>
                        <button onclick="document.getElementById('${dialogId}').remove()" 
                                style="background: none; border: none; font-size: 24px; cursor: pointer;">×</button>
                    </div>
                    ${content}
                </div>
            </div>
        `;
        
        document.body.insertAdjacentHTML('beforeend', dialogHTML);
    }

    /**
     * 关闭所有对话框
     */
    closeAllDialogs() {
        const dialogs = document.querySelectorAll('[id^="ux-dialog-"]');
        dialogs.forEach(dialog => dialog.remove());
    }

    /**
     * 绑定事件
     */
    bindEvents() {
        document.addEventListener('keydown', (e) => this.handleKeyDown(e));
        
        // 阻止默认的一些快捷键
        document.addEventListener('keydown', (e) => {
            if ((e.ctrlKey || e.metaKey) && ['s', 'r'].includes(e.key.toLowerCase())) {
                e.preventDefault();
            }
        });
    }

    /**
     * 工具方法
     */
    canUndo() {
        return this.historyIndex >= 0;
    }

    canRedo() {
        return this.historyIndex < this.history.length - 1;
    }

    generateActionId() {
        return Date.now().toString(36) + Math.random().toString(36).substr(2);
    }

    getActionDescription(action) {
        const descriptions = {
            'move-module': '移动模块',
            'add-module': '添加模块',
            'remove-module': '删除模块',
            'add-connection': '添加连接',
            'remove-connection': '删除连接'
        };
        return descriptions[action.type] || action.type;
    }

    saveCurrentState() {
        // 保存当前状态到localStorage
        const state = {
            config: this.app.config,
            positions: this.app.positionManager?.positions,
            theme: this.currentTheme,
            timestamp: Date.now()
        };
        
        localStorage.setItem('tradefusion_saved_state', JSON.stringify(state));
    }

    /**
     * 清理资源
     */
    cleanup() {
        this.shortcuts.clear();
        this.history = [];
        this.historyIndex = -1;
        this.themes.clear();
        
        console.log('[UserExperience] 资源清理完成');
    }
}

// 导出用户体验类
window.UserExperience = UserExperience;
