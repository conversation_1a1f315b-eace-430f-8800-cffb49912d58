{"name": "tradefusion-desktop", "version": "2.0.0", "description": "TradeFusion 数据流可视化管理界面 - Electron桌面版", "main": "main.js", "scripts": {"start": "electron .", "dev": "electron . --dev", "build": "electron-builder", "dist": "electron-builder --publish=never", "pack": "electron-builder --dir"}, "keywords": ["TradeFusion", "数据流", "可视化", "桌面应用"], "author": "TradeFusion Team", "license": "MIT", "devDependencies": {"electron": "^22.0.0"}, "dependencies": {}, "build": {"appId": "com.tradefusion.desktop", "productName": "TradeFusion桌面版", "directories": {"output": "dist"}, "files": ["**/*", "!node_modules", "!dist", "!archive"], "win": {"target": "nsis", "icon": "assets/icon.ico"}, "nsis": {"oneClick": false, "allowToChangeInstallationDirectory": true, "createDesktopShortcut": true, "createStartMenuShortcut": true}}}