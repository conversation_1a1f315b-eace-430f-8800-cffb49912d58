#!/usr/bin/env python3
"""
TradeFusion触发器堵塞优化 - 索引创建脚本
解决个股接力表4表关联查询和人气数据更新的性能问题
"""

import sqlite3
import sys
import time
from pathlib import Path

# 添加项目根目录到路径
current_file = Path(__file__).resolve()
project_root = current_file.parent.parent
if str(project_root) not in sys.path:
    sys.path.insert(0, str(project_root))

def 创建优化索引():
    """创建所有优化索引"""
    
    # 获取数据库路径
    try:
        from 公共模块.配置管理 import get_config
        config = get_config()
        db_path = str(config.get_db_path())
    except ImportError:
        db_path = str(project_root / "数据库0_实体模块/股票数据.db")
    
    print(f"🔴[索引优化] 🟡开始创建优化索引")
    print(f"数据库路径: {db_path}")
    
    conn = None
    try:
        conn = sqlite3.connect(db_path, timeout=30)
        cursor = conn.cursor()
        
        # 读取索引SQL文件
        sql_file = current_file.parent / "优化索引.sql"
        with open(sql_file, 'r', encoding='utf-8') as f:
            sql_content = f.read()
        
        # 分割SQL语句并执行
        sql_statements = [stmt.strip() for stmt in sql_content.split(';') if stmt.strip() and not stmt.strip().startswith('--')]
        
        created_count = 0
        for sql in sql_statements:
            if sql.upper().startswith('CREATE INDEX'):
                try:
                    start_time = time.time()
                    cursor.execute(sql)
                    execution_time = time.time() - start_time
                    
                    # 提取索引名称
                    index_name = sql.split('IF NOT EXISTS')[1].split('ON')[0].strip()
                    print(f"✅ 索引 {index_name} 创建成功 ({execution_time:.2f}秒)")
                    created_count += 1
                    
                except sqlite3.Error as e:
                    if "already exists" in str(e):
                        index_name = sql.split('IF NOT EXISTS')[1].split('ON')[0].strip()
                        print(f"ℹ️  索引 {index_name} 已存在")
                    else:
                        print(f"❌ 索引创建失败: {str(e)}")
        
        conn.commit()
        print(f"\n🔴[索引优化] 🟢完成 - 创建了 {created_count} 个新索引")
        
        # 验证索引创建
        cursor.execute("SELECT name FROM sqlite_master WHERE type='index' AND name LIKE 'idx_%'")
        all_indexes = cursor.fetchall()
        print(f"数据库中共有 {len(all_indexes)} 个优化索引")
        
        return True
        
    except Exception as e:
        print(f"🔴[索引优化] 🟡执行失败: {str(e)}")
        return False
    finally:
        if conn:
            conn.close()

def 分析查询性能():
    """分析关键查询的性能"""
    
    try:
        from 公共模块.配置管理 import get_config
        config = get_config()
        db_path = str(config.get_db_path())
    except ImportError:
        db_path = str(project_root / "数据库0_实体模块/股票数据.db")
    
    conn = None
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        print(f"\n🔴[性能分析] 🟡分析关键查询性能")
        
        # 获取最新日期
        cursor.execute("SELECT MAX(日期) FROM 个股板块关联表")
        latest_date = cursor.fetchone()[0]
        
        if not latest_date:
            print("❌ 无有效数据进行性能分析")
            return
        
        # 测试个股接力表关键查询
        test_queries = [
            ("个股板块关联表查询", f"SELECT COUNT(*) FROM 个股板块关联表 WHERE 日期 = {latest_date}"),
            ("个股人气表查询", f"SELECT COUNT(*) FROM 个股人气表 WHERE 日期 = {latest_date} AND 综合人气评分 > 80"),
            ("板块精选表查询", f"SELECT COUNT(*) FROM 板块精选表 WHERE 日期 = {latest_date}"),
            ("4表关联查询模拟", f"""
                SELECT COUNT(DISTINCT g.股票代码)
                FROM 个股板块关联表 g
                INNER JOIN 个股人气表 r ON g.股票代码 = r.股票代码 AND r.日期 = {latest_date}
                WHERE g.日期 = {latest_date} AND r.综合人气评分 > 80
            """)
        ]
        
        for query_name, query_sql in test_queries:
            try:
                start_time = time.time()
                cursor.execute(query_sql)
                result = cursor.fetchone()[0]
                execution_time = time.time() - start_time
                
                print(f"✅ {query_name}: {result}条记录, 耗时{execution_time:.3f}秒")
                
            except Exception as e:
                print(f"❌ {query_name}执行失败: {str(e)}")
        
    except Exception as e:
        print(f"🔴[性能分析] 🟡分析失败: {str(e)}")
    finally:
        if conn:
            conn.close()

if __name__ == "__main__":
    print("🔧 TradeFusion触发器堵塞优化工具")
    print("=" * 50)
    
    # 创建优化索引
    if 创建优化索引():
        # 分析查询性能
        分析查询性能()
        print("\n🎉 触发器堵塞优化完成！")
    else:
        print("\n❌ 触发器堵塞优化失败！")
        sys.exit(1)
