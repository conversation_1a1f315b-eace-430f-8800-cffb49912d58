@echo off
title TradeFusion VS Code Launcher

echo.
echo ========================================
echo   TradeFusion VS Code Launcher
echo ========================================
echo.

REM Set project root directory
set "PROJECT_ROOT=%~dp0"
cd /d "%PROJECT_ROOT%"

REM Check if virtual environment exists
if not exist "venv\Scripts\python.exe" (
    echo [ERROR] Virtual environment not found!
    echo         Please ensure venv is properly installed in venv directory
    echo.
    pause
    exit /b 1
)

REM Check if target Python file exists
if not exist "TradeFusion_VS Code.py" (
    echo [ERROR] TradeFusion_VS Code.py file not found!
    echo         Please ensure the file is in project root directory
    echo.
    pause
    exit /b 1
)

echo [OK] Environment check passed
echo [INFO] Project directory: %PROJECT_ROOT%
echo [INFO] Python environment: venv\Scripts\python.exe
echo [INFO] Target file: TradeFusion_VS Code.py
echo.

REM Activate virtual environment and start Python script
echo [START] Launching TradeFusion VS Code...
echo.

REM Set environment variables
set "VIRTUAL_ENV=%PROJECT_ROOT%venv"
set "PATH=%PROJECT_ROOT%venv\Scripts;%PATH%"
set "PYTHONPATH=%PROJECT_ROOT%"

REM Start Python script
"%PROJECT_ROOT%venv\Scripts\python.exe" "TradeFusion_VS Code.py"

REM Check execution result
if %ERRORLEVEL% EQU 0 (
    echo.
    echo [SUCCESS] TradeFusion VS Code completed successfully
) else (
    echo.
    echo [ERROR] TradeFusion VS Code failed with error code: %ERRORLEVEL%
)

echo.
echo Press any key to close window...
pause >nul
