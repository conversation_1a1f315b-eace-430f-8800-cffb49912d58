# {{ AURA-X: Modify - 将SQLite连接改为PostgreSQL连接. Approval: 寸止(ID:1737734400). }}
import psycopg2
import psycopg2.extras
from datetime import datetime
import os
import sys
from pathlib import Path

# 添加项目根目录到路径
current_file = Path(__file__).resolve()
project_root = current_file.parent.parent
if str(project_root) not in sys.path:
    sys.path.insert(0, str(project_root))

# 导入TradeFusion统一日志标准
from 公共模块.TradeFusion统一日志标准 import 获取日志器
logger = 获取日志器("所属板块评分表")

def calculate_stock_strength():
    # 连接数据库
    # {{ AURA-X: Modify - 修改为PostgreSQL连接配置. Approval: 寸止(ID:1737734400). }}
    # PostgreSQL连接配置
    db_config = {
        'host': 'localhost',
        'port': 5432,
        'database': 'tradefusion',
        'user': 'postgres',
        'password': 'ymjatTUU520'
    }
    conn = psycopg2.connect(**db_config)
    cursor = conn.cursor()
    
    try:
        # {{ AURA-X: Modify - 修改为PostgreSQL语法. Approval: 寸止(ID:1737734400). }}
        # 获取最新日期
        cursor.execute('SELECT MAX("日期") FROM "个股板块关联表"')
        max_date = cursor.fetchone()[0]
        if not max_date:
            return False, "未找到有效日期数据", 0

        # 创建临时表存储当日有效股票代码
        cursor.execute('''CREATE TEMP TABLE IF NOT EXISTS temp_stocks AS
                       SELECT DISTINCT "股票代码" FROM "个股板块关联表" WHERE "日期" = %s''', (max_date,))

        # 清空当日旧数据
        cursor.execute('DELETE FROM "所属板块评分表" WHERE "日期" = %s', (max_date,))

        # {{ AURA-X: Modify - 修改为PostgreSQL语法. Approval: 寸止(ID:1737734400). }}
        # 计算并插入新的板块评分数据
        insert_sql = '''
        INSERT INTO "所属板块评分表" ("日期", "股票代码", "所属板块评分")
        SELECT
            %s AS "日期",
            a."股票代码",
            COALESCE(SUM(b."板块评分"), 0) AS "所属板块评分"
        FROM "个股板块关联表" a
        LEFT JOIN "板块涨停表" b
            ON a."所属板块名称" = b."板块名称"
            AND a."日期" = b."日期"
        WHERE a."日期" = %s
        GROUP BY a."股票代码"
        '''
        cursor.execute(insert_sql, (max_date, max_date))
        
        conn.commit()
        # 🔄 所属板块评分表更新完成后，自动触发板块强度DAT文件生成模块
        _trigger_strength_dat_generation()

        return True, max_date, cursor.rowcount

    except Exception as e:
        conn.rollback()
        return False, str(e), 0
    finally:
        if 'conn' in locals() and conn:
            conn.close()

def _trigger_strength_dat_generation():
    """触发所属板块强度DAT文件生成模块"""
    try:
        # 触发A4_所属板块强度模块
        from 数据库写大智慧.A4_所属板块强度 import main as 板块强度_main

        logger.info(f"🔄 [所属板块评分表] 自动触发所属板块强度DAT文件生成模块...")

        success, message, count = 板块强度_main()
        if success:
            logger.info(f"✅ [所属板块评分表] 所属板块强度DAT文件生成模块执行成功")
        else:
            logger.error(f"❌ [所属板块评分表] 所属板块强度DAT文件生成模块执行失败: {message}")

    except Exception as e:
        logger.error(f"❌ [所属板块评分表] 触发板块强度DAT文件生成模块异常: {str(e)}")

def update_dat_file(filepath, target_ts, new_value):
    os.makedirs(os.path.dirname(filepath), exist_ok=True)  # 确保目录存在
    with open(filepath, 'r+') as f:
        lines = f.readlines()
        for i, line in enumerate(lines):
            if line.startswith(target_ts):
                lines[i] = f"{target_ts} {new_value}\n"
                break
        else:
            lines.append(f"{target_ts} {new_value}\n")
        f.seek(0)
        f.writelines(lines)

if __name__ == "__main__":
    success, message, count = calculate_stock_strength()
    date_str = datetime.now().strftime("%Y-%m-%d")
    if success:
        logger.记录数据库操作([("所属板块评分表", count)])
    else:
        logger.记录错误(f"板块评分计算失败 - {message}")