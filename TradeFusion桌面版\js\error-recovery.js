/**
 * 错误恢复模块
 * 
 * 功能：智能错误恢复和数据备份
 * 职责：错误检测、自动恢复、数据保护
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 */

class ErrorRecovery {
    constructor(app) {
        this.app = app;
        this.backupData = new Map();
        this.recoveryStrategies = new Map();
        this.isRecovering = false;
        this.maxRetries = 3;
        this.retryDelay = 1000;
        
        this.initializeRecovery();
    }

    /**
     * 初始化错误恢复系统
     */
    initializeRecovery() {
        // 注册恢复策略
        this.registerRecoveryStrategies();
        
        // 监听应用错误
        this.app.on('app:error', (errorInfo) => {
            this.handleError(errorInfo);
        });

        // 定期备份数据
        this.startAutoBackup();
        
        // 监听页面卸载，保存最后状态
        window.addEventListener('beforeunload', () => {
            this.saveEmergencyBackup();
        });

        console.log('[ErrorRecovery] 错误恢复系统已初始化');
    }

    /**
     * 注册恢复策略
     */
    registerRecoveryStrategies() {
        // 模块渲染失败恢复
        this.recoveryStrategies.set('module:render-failed', {
            priority: 'high',
            strategy: this.recoverModuleRender.bind(this),
            maxRetries: 3
        });

        // 连接线渲染失败恢复
        this.recoveryStrategies.set('connection:render-failed', {
            priority: 'medium',
            strategy: this.recoverConnectionRender.bind(this),
            maxRetries: 2
        });

        // 配置加载失败恢复
        this.recoveryStrategies.set('config:load-failed', {
            priority: 'high',
            strategy: this.recoverConfigLoad.bind(this),
            maxRetries: 1
        });

        // 事件系统失败恢复
        this.recoveryStrategies.set('event:system-failed', {
            priority: 'critical',
            strategy: this.recoverEventSystem.bind(this),
            maxRetries: 1
        });

        // 内存泄漏恢复
        this.recoveryStrategies.set('memory:leak-detected', {
            priority: 'medium',
            strategy: this.recoverMemoryLeak.bind(this),
            maxRetries: 1
        });
    }

    /**
     * 处理错误
     */
    async handleError(errorInfo) {
        if (this.isRecovering) {
            console.warn('[ErrorRecovery] 恢复过程中，跳过新错误处理');
            return;
        }

        const errorType = this.classifyError(errorInfo);
        const strategy = this.recoveryStrategies.get(errorType);

        if (strategy) {
            console.log(`[ErrorRecovery] 开始恢复策略: ${errorType}`);
            await this.executeRecoveryStrategy(errorType, strategy, errorInfo);
        } else {
            console.warn(`[ErrorRecovery] 未找到恢复策略: ${errorType}`);
            this.fallbackRecovery(errorInfo);
        }
    }

    /**
     * 错误分类
     */
    classifyError(errorInfo) {
        const { context, message } = errorInfo;
        
        if (context.includes('模块') && context.includes('渲染')) {
            return 'module:render-failed';
        }
        
        if (context.includes('连接线') || context.includes('ConnectionRenderer')) {
            return 'connection:render-failed';
        }
        
        if (context.includes('配置') || message.includes('config')) {
            return 'config:load-failed';
        }
        
        if (context.includes('事件') || message.includes('event')) {
            return 'event:system-failed';
        }
        
        if (message.includes('内存') || message.includes('memory')) {
            return 'memory:leak-detected';
        }
        
        return 'unknown:error';
    }

    /**
     * 执行恢复策略
     */
    async executeRecoveryStrategy(errorType, strategy, errorInfo) {
        this.isRecovering = true;
        let retryCount = 0;

        while (retryCount < strategy.maxRetries) {
            try {
                console.log(`[ErrorRecovery] 执行恢复策略 ${errorType} (尝试 ${retryCount + 1}/${strategy.maxRetries})`);
                
                const result = await strategy.strategy(errorInfo, retryCount);
                
                if (result.success) {
                    console.log(`[ErrorRecovery] 恢复成功: ${errorType}`);
                    this.app.emit('recovery:success', { errorType, retryCount });
                    break;
                } else {
                    throw new Error(result.error || '恢复策略执行失败');
                }
            } catch (error) {
                retryCount++;
                console.warn(`[ErrorRecovery] 恢复尝试 ${retryCount} 失败:`, error.message);
                
                if (retryCount < strategy.maxRetries) {
                    await this.delay(this.retryDelay * retryCount);
                } else {
                    console.error(`[ErrorRecovery] 恢复失败，已达到最大重试次数: ${errorType}`);
                    this.app.emit('recovery:failed', { errorType, retryCount, error });
                    this.fallbackRecovery(errorInfo);
                }
            }
        }

        this.isRecovering = false;
    }

    /**
     * 模块渲染恢复
     */
    async recoverModuleRender(errorInfo, retryCount) {
        try {
            // 清理可能损坏的DOM元素
            const moduleContainer = document.getElementById('modules-container');
            if (moduleContainer) {
                const brokenElements = moduleContainer.querySelectorAll('.module-node[data-error="true"]');
                brokenElements.forEach(el => el.remove());
            }

            // 重新初始化渲染器
            if (this.app.renderer) {
                await this.app.renderer.render();
            }

            return { success: true };
        } catch (error) {
            return { success: false, error: error.message };
        }
    }

    /**
     * 连接线渲染恢复
     */
    async recoverConnectionRender(errorInfo, retryCount) {
        try {
            // 清理SVG连接线
            const svg = document.getElementById('flow-svg');
            if (svg) {
                svg.innerHTML = '';
            }

            // 重新初始化连接线渲染器
            if (this.app.connectionRenderer) {
                await this.app.connectionRenderer.render();
            }

            return { success: true };
        } catch (error) {
            return { success: false, error: error.message };
        }
    }

    /**
     * 配置加载恢复
     */
    async recoverConfigLoad(errorInfo, retryCount) {
        try {
            // 尝试从备份恢复配置
            const backupConfig = this.getBackup('config');
            if (backupConfig) {
                this.app.config = backupConfig;
                console.log('[ErrorRecovery] 从备份恢复配置');
                return { success: true };
            }

            // 使用默认配置
            this.app.config = this.getDefaultConfig();
            console.log('[ErrorRecovery] 使用默认配置');
            return { success: true };
        } catch (error) {
            return { success: false, error: error.message };
        }
    }

    /**
     * 事件系统恢复
     */
    async recoverEventSystem(errorInfo, retryCount) {
        try {
            // 清理所有事件监听器
            if (this.app.eventManager) {
                this.app.eventManager.cleanup();
            }

            // 重新初始化事件系统
            this.app.eventManager = EventUtils.createEventManager();
            
            // 重新绑定核心事件
            this.app.initializeEvents();

            return { success: true };
        } catch (error) {
            return { success: false, error: error.message };
        }
    }

    /**
     * 内存泄漏恢复
     */
    async recoverMemoryLeak(errorInfo, retryCount) {
        try {
            // 强制垃圾回收（如果支持）
            if (window.gc) {
                window.gc();
            }

            // 清理缓存
            this.clearCaches();

            // 清理未使用的事件监听器
            this.cleanupEventListeners();

            return { success: true };
        } catch (error) {
            return { success: false, error: error.message };
        }
    }

    /**
     * 后备恢复策略
     */
    fallbackRecovery(errorInfo) {
        console.log('[ErrorRecovery] 执行后备恢复策略');
        
        // 显示错误恢复界面
        this.showRecoveryUI(errorInfo);
        
        // 保存错误状态
        this.saveErrorState(errorInfo);
    }

    /**
     * 显示恢复界面
     */
    showRecoveryUI(errorInfo) {
        const recoveryDiv = document.createElement('div');
        recoveryDiv.id = 'error-recovery-ui';
        recoveryDiv.innerHTML = `
            <div style="position: fixed; top: 0; left: 0; width: 100%; height: 100%; 
                        background: rgba(0,0,0,0.8); z-index: 10000; display: flex; 
                        align-items: center; justify-content: center;">
                <div style="background: white; padding: 30px; border-radius: 12px; 
                           max-width: 500px; text-align: center;">
                    <h2 style="color: #dc2626; margin-bottom: 20px;">🚨 系统遇到错误</h2>
                    <p style="margin-bottom: 20px; color: #374151;">
                        应用遇到了无法自动恢复的错误，您可以选择以下操作：
                    </p>
                    <div style="display: flex; gap: 10px; justify-content: center;">
                        <button onclick="location.reload()" 
                                style="padding: 10px 20px; background: #3b82f6; color: white; 
                                       border: none; border-radius: 6px; cursor: pointer;">
                            🔄 重新加载
                        </button>
                        <button onclick="errorRecovery.restoreFromBackup()" 
                                style="padding: 10px 20px; background: #10b981; color: white; 
                                       border: none; border-radius: 6px; cursor: pointer;">
                            📦 恢复备份
                        </button>
                        <button onclick="errorRecovery.hideRecoveryUI()" 
                                style="padding: 10px 20px; background: #6b7280; color: white; 
                                       border: none; border-radius: 6px; cursor: pointer;">
                            ❌ 关闭
                        </button>
                    </div>
                </div>
            </div>
        `;
        
        document.body.appendChild(recoveryDiv);
    }

    /**
     * 隐藏恢复界面
     */
    hideRecoveryUI() {
        const recoveryUI = document.getElementById('error-recovery-ui');
        if (recoveryUI) {
            recoveryUI.remove();
        }
    }

    /**
     * 从备份恢复
     */
    restoreFromBackup() {
        try {
            const configBackup = this.getBackup('config');
            const positionBackup = this.getBackup('positions');
            
            if (configBackup) {
                this.app.config = configBackup;
            }
            
            if (positionBackup && this.app.positionManager) {
                this.app.positionManager.positions = positionBackup;
            }
            
            // 重新初始化应用
            this.app.init();
            this.hideRecoveryUI();
            
            this.app.showNotification('恢复成功', '已从备份恢复应用状态', 'success');
        } catch (error) {
            console.error('[ErrorRecovery] 备份恢复失败:', error);
            this.app.showNotification('恢复失败', '备份恢复过程中出现错误', 'error');
        }
    }

    /**
     * 开始自动备份
     */
    startAutoBackup(interval = 30000) {
        setInterval(() => {
            this.createBackup();
        }, interval);
        
        console.log('[ErrorRecovery] 自动备份已启动');
    }

    /**
     * 创建备份
     */
    createBackup() {
        try {
            // 备份配置
            if (this.app.config) {
                this.setBackup('config', JSON.parse(JSON.stringify(this.app.config)));
            }

            // 备份位置信息
            if (this.app.positionManager && this.app.positionManager.positions) {
                this.setBackup('positions', JSON.parse(JSON.stringify(this.app.positionManager.positions)));
            }

            // 备份应用状态
            this.setBackup('appState', {
                timestamp: Date.now(),
                isInitialized: this.app.isInitialized,
                moduleCount: Object.keys(this.app.config?.modules || {}).length
            });

            console.log('[ErrorRecovery] 备份已创建');
        } catch (error) {
            console.warn('[ErrorRecovery] 创建备份失败:', error);
        }
    }

    /**
     * 保存紧急备份
     */
    saveEmergencyBackup() {
        try {
            const emergencyData = {
                config: this.app.config,
                positions: this.app.positionManager?.positions,
                timestamp: Date.now()
            };
            
            localStorage.setItem('tradefusion_emergency_backup', JSON.stringify(emergencyData));
            console.log('[ErrorRecovery] 紧急备份已保存');
        } catch (error) {
            console.warn('[ErrorRecovery] 保存紧急备份失败:', error);
        }
    }

    /**
     * 设置备份
     */
    setBackup(key, data) {
        this.backupData.set(key, {
            data: data,
            timestamp: Date.now()
        });
    }

    /**
     * 获取备份
     */
    getBackup(key) {
        const backup = this.backupData.get(key);
        return backup ? backup.data : null;
    }

    /**
     * 获取默认配置
     */
    getDefaultConfig() {
        return {
            modules: {},
            connections: [],
            settings: {
                theme: 'light',
                autoSave: true
            }
        };
    }

    /**
     * 清理缓存
     */
    clearCaches() {
        // 清理性能标记
        PerformanceUtils.clearPerformanceMarks();
        
        // 清理备份数据（保留最近的）
        const now = Date.now();
        this.backupData.forEach((backup, key) => {
            if (now - backup.timestamp > 300000) { // 5分钟前的备份
                this.backupData.delete(key);
            }
        });
    }

    /**
     * 清理事件监听器
     */
    cleanupEventListeners() {
        if (this.app.eventManager) {
            const listenerCount = this.app.eventManager.getListenerCount();
            if (listenerCount > 100) {
                console.warn(`[ErrorRecovery] 检测到大量事件监听器 (${listenerCount})，执行清理`);
                this.app.eventManager.cleanup();
                this.app.eventManager = EventUtils.createEventManager();
            }
        }
    }

    /**
     * 保存错误状态
     */
    saveErrorState(errorInfo) {
        try {
            const errorState = {
                error: errorInfo,
                appState: {
                    config: this.app.config,
                    timestamp: Date.now()
                }
            };
            
            localStorage.setItem('tradefusion_error_state', JSON.stringify(errorState));
        } catch (error) {
            console.warn('[ErrorRecovery] 保存错误状态失败:', error);
        }
    }

    /**
     * 延迟函数
     */
    delay(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }

    /**
     * 清理资源
     */
    cleanup() {
        this.backupData.clear();
        this.recoveryStrategies.clear();
        console.log('[ErrorRecovery] 资源清理完成');
    }
}

// 导出错误恢复类
window.ErrorRecovery = ErrorRecovery;
